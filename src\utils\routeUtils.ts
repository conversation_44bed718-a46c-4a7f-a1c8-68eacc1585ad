import { useRoute } from 'vue-router';

/**
 * 安全获取路由参数（支持 string、number、boolean、bigint 和 undefined）
 */
export function useRouteParam<T extends string | number | boolean | bigint | undefined>(
  key: string,
  options: {
    type: 'string' | 'number' | 'boolean' | 'bigint'; // 新增 bigint 类型
    default?: T; // 允许 undefined
  }
): T {
  const route = useRoute();
  const param = route?.params[key];

  if (param == null) {
    return options.default as T;
  }

  if (Array.isArray(param)) {
    return options.default as T;
  }

  switch (options.type) {
    case 'string':
      return param as T;
    
    case 'number': {
      const num = Number(param);
      return isNaN(num) ? (options.default as T) : num as T;
    }
    
    case 'boolean': {
      if (param === 'true') return true as T;
      if (param === 'false') return false as T;
      return options.default as T;
    }
    
    case 'bigint': {
      try {
        // 确保是有效的数字字符串后再转换
        if (/^-?\d+$/.test(param)) {
          return BigInt(param) as T;
        }
        return options.default as T;
      } catch {
        return options.default as T;
      }
    }
    
    default:
      return options.default as T;
  }
}