﻿import BaseApi from '/@/api/BaseApi';
import request from '/@/utils/request';

class dashboardApi extends BaseApi {
	GetQueryData(params?: any) {
		return request({
			url: this.baseurl + 'GetQueryData',
			method: 'get',
			params,
		});
	}

	GetTotalBillable(data?: any) {
		return request({
			url: this.baseurl + 'GetTotalBillable',
			method: 'post',
			data,
		});
	}

	GetInvoiceTotalAmount(data?: any) {
		return request({
			url: this.baseurl + 'GetInvoiceTotalAmount',
			method: 'post',
			data,
		});
	}

	GetProjectsByAmount(data?: any) {
		return request({
			url: this.baseurl + 'GetProjectsByAmount',
			method: 'post',
			data,
		});
	}

	GetInvoiceAmount(data?: any) {
		return request({
			url: this.baseurl + 'GetInvoiceAmount',
			method: 'post',
			data,
		});
	}


	
	GetProductionVolume(data?: any) {
		return request({
			url: this.baseurl + 'GetProductionVolume',
			method: 'post',
			data,
		});
	}

	GetTurnaroundTime(data?: any) {
		return request({
			url: this.baseurl + 'GetTurnaroundTime',
			method: 'post',
			data,
		});
	}

	GetAccuracy(data?: any) {
		return request({
			url: this.baseurl + 'GetAccuracy',
			method: 'post',
			data,
		});
	}
	
}

export default new dashboardApi('/api/dashboard/', 'id');
