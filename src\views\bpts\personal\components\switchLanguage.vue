<template>
	<div class="email-content">
		<el-card>
			<template #header>{{ $t('message.userFields.switchLanguage') }}</template>
			<el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-position="top" label-width="130px">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item>
							<el-select v-model="ruleForm.language" class="form-control" style="width: 500px"
								placeholder="Select" size="default">
								<el-option label="简体中文" value="zh-cn"></el-option>
								<el-option label="English" value="en"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item class="el-form-item_btn">
							<el-button :loading="saveLoading" color="#626aef" type="primary"
								@click="onSubmit(ruleForm.language)">{{
									$t('message.userButtons.Save')
								}}</el-button>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
		</el-card>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, onMounted, defineComponent, watch, getCurrentInstance, computed } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { useI18n } from 'vue-i18n';
import userApi from '/@/api/user';
import { IApiResultMessage } from '/@/types/models';
import { Local } from '/@/utils/storage';
import { useThemeConfig } from '/@/stores/themeConfig';
import { storeToRefs } from 'pinia';
import { useUserInfo } from '/@/stores/userInfo';
import other from '/@/utils/other';
export default defineComponent({
	name: 'SwitchLanguage',
	props: {
		info: Object,
	},
	setup(props, context) {

		const stores = useUserInfo();
		const { userInfos } = storeToRefs(stores);
		const storesThemeConfig = useThemeConfig();
		const { themeConfig } = storeToRefs(storesThemeConfig);
		const { t } = useI18n();
		const { proxy } = getCurrentInstance() as any;
		const state = reactive({
			messages: [] as IApiResultMessage[], // 初始为空数组
			saveLoading: false,
			ruleForm: {
				id: 0,
				language: 'English',
			},
			rules: {
				language: [{ required: true, message: 'Please select language', trigger: 'change' }],
			},
		});

		watch(
			() => props.info,
			(newVal: any, oldVal) => {
				state.ruleForm.language = newVal.email;
				state.ruleForm.id = newVal.id;
			}
		);
		// 获取布局配置信息
		const getThemeConfig = computed(() => {
			return themeConfig.value;
		});
		const onSubmit = (val: string) => {
			var obj = {
				id: state.ruleForm.id,
				Language: val,
			};
			userApi
				.UpdateUserLanguage(obj)
				.then(() => {
					Local.remove('themeConfig');
					getThemeConfig.value.globalI18n = val;
					Local.set('themeConfig', getThemeConfig.value);
					proxy.$i18n.locale = val;
					other.useTitle();
					ElMessage.success('Succeed');
				})
				.catch((rs) => {
					ElMessage.error(rs.resultMsg || rs.toString());
				})
				.finally(() => { });
		};
		// 页面加载时
		onMounted(() => {
			if (props.info) {
				state.ruleForm.language = props.info.language;
				state.ruleForm.id = props.info.id;
			}
		});
		return {
			onSubmit,
			...toRefs(state),
		};
	},
});
</script>


<style scoped>
.lpx-content {
	max-width: 1280px !important;
	padding: 1.25em 2em 3em;
	margin: 0 auto;
}

.lpx-avatar-img-lg {
	height: 144px;
	width: 144px;
	border-radius: 144px;
}

.mb-0 {
	margin-bottom: 0 !important;
}

.mb-3 {
	margin-bottom: 1.5rem !important;
}

.mt-2 {
	margin-top: 0.75rem !important;
}

.pb-3 {
	padding-bottom: 1.5rem !important;
}

.card .card-body img {
	max-width: 100%;
}

.text-center {
	text-align: center !important;
}

.border-bottom {
	border-bottom: 1px solid #e8eef3;
}

.card .card-title {
	font-size: 1.25em;
	font-weight: 500;
	color: #062a44;
}

.text-muted {
	--bs-text-opacity: 1;
	color: #325168 !important;
	font-size: 12px;
}

.text-muted {
	opacity: 0.8;
}

.fw-normal {
	font-weight: 400 !important;
}

h5,
.h5 {
	font-size: 13px !important;
}

.menu_btn_wrapper {
	display: flex;
	flex-direction: column;
}

.menu_btn {
	width: 100% !important;
}

.button_container {
	margin-bottom: 10px;
}
</style>