<template>
	<div class="pdf-upload-container">
		<el-dialog v-model="dialogVisible" title="Manage PDF Documents" width="800px" :close-on-click-modal="false" draggable @closed="handleDialogClosed">
			<div class="pdf-upload-dialog">
				<div class="dialog-header">
					<div class="header-text">
						<h3>Upload or manage PDF files for this invoice (Pro# {{ currentInvoice?.proNumber }})</h3>
						<p class="tip-text">Select document type to view or upload files</p>
					</div>
				</div>

				<!-- 文件类型选择 -->
				<div class="file-type-selector mb-4">
					<el-radio-group v-model="uploadParams.fileType" size="default" @change="handleFileTypeChange">
						<el-radio-button label="ALL" value="">ALL</el-radio-button>
						<el-radio-button label="POD" value="POD" />
						<el-radio-button label="BOL" value="BOL" />
						<el-radio-button label="Others" value="Others" />
						<el-radio-button label="Additional" value="Additional" />
						<el-radio-button label="Combine File" value="CombineFile" />
					</el-radio-group>
				</div>

				<!-- 上传区域 - 只在选择具体类型时显示 -->
				<div class="upload-area" v-if="showUploadArea">
					<scUploadFile name="files" v-model="newFiles" :limit="1" accept=".pdf,application/pdf" :action="action" :data="uploadParams" :on-success="onUploadSuccess" :on-before="onBeforeUpload" :showFileList="false" :drag="true">
						<template #default>
							<el-icon class="el-icon--upload"><upload-filled /></el-icon>
							<div class="el-upload__text">
								<template v-if="uploadParams.fileType === 'Additional'"> Drop <em>additional</em> PDF here or click to upload</template>
								<template v-else-if="isSystemFileMissing">
									<span class="missing-text">
										<em>{{ uploadParams.fileType }}</em> file missing
									</span>
									- drop here or click to upload
								</template>
								<template v-else>
									Drop <em>{{ uploadParams.fileType }}</em> file here or click to upload
								</template>
							</div>
						</template>
					</scUploadFile>

					<div class="form-tip mt-2">
						<el-icon>
							<InfoFilled />
						</el-icon>
						<span v-if="uploadParams.fileType === 'Additional'">Maximum 10 additional PDF files ({{ additionalFilesCount }} uploaded)</span>
						<span v-else>{{ existingFiles.length > 0 ? 'System file exists' : 'System file missing' }}</span>
					</div>
				</div>

				<!-- Upload trigger for system files when they exist -->
				<div class="upload-trigger" v-if="false">
					<span class="trigger-tip">Click to upload or replace existing file</span>
				</div>

				<!-- 已上传文件表格 -->
				<div class="uploaded-files-table mt-2">
					<el-table :data="existingFiles" v-loading="uploadLoading" border stripe size="small" empty-text="No data" style="width: 100%">
						<el-table-column prop="fileName" label="File Name" min-width="180">
							<template #default="{ row }">
								<el-link type="primary" @click="previewPDF(row)" :underline="false">
									{{ row.displayName }}
								</el-link>
							</template>
						</el-table-column>

						<el-table-column prop="uploadDate" label="Created At" width="170">
							<template #default="{ row }">
								{{ row.createdAt }}
							</template>
						</el-table-column>

						<el-table-column label="Actions" width="90" align="center">
							<template #default="{ row }">
								<vxe-button mode="text" status="primary" icon="vxe-icon-download" @click="onDownload(row)"></vxe-button>
								<vxe-button mode="text" status="danger" icon="vxe-icon-delete" @click="confirmDeleteFile(row)" :disabled="isSystemFile(row)"></vxe-button>
							</template>
						</el-table-column>
					</el-table>
				</div>
			</div>

			<template #footer>
				<div class="dialog-footer">
					<el-button @click="closeDialog">Cancel</el-button>
				</div>
			</template>
		</el-dialog>
		<FilePreview ref="viewerRef" />
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent, computed, ref } from 'vue';
import { Delete, Download, InfoFilled, Upload } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import shipmentFileApi from '/@/api/WWE_Shipment_File';
import fileApi from '/@/api/Files';
import appSettings from '/@/config/index.js';

import FilePreview from '/@/components/file/FilePreview.vue';

export default defineComponent({
	name: 'PdfUploadDialog',
	components: { Delete, InfoFilled, Upload, FilePreview },
	emits: ['complete'],
	setup(props, { emit }) {
		const viewerRef = ref();

		const state = reactive({
			dialogVisible: false,
			uploadLoading: false,
			newFiles: [] as any[],
			existingFiles: [] as any[],
			currentInvoice: null as any,
			action: `${appSettings.BASE_URL}Api/WWE_Shipment_File/UpLoadShipmentPDF`,
			uploadParams: {
				invoiceId: null,
				fileType: '',
			},
		});

		// 系统文件类型
		const SYSTEM_FILE_TYPES = ['POD', 'BOL', 'Others'];

		// 计算属性：根据选择的文件类型过滤文件列表
		const filteredFiles = computed(() => {
			if (!state.uploadParams.fileType) return state.existingFiles;
			if (state.uploadParams.fileType === 'Additional') {
				return state.existingFiles.filter((file) => !SYSTEM_FILE_TYPES.includes(file.fileType));
			}
			return state.existingFiles.filter((file) => file.fileType === state.uploadParams.fileType);
		});

		// 计算属性：是否是系统文件缺失状态
		const isSystemFileMissing = computed(() => {
			return SYSTEM_FILE_TYPES.includes(state.uploadParams.fileType) && state.existingFiles.length === 0;
		});

		// 计算属性：附加文件数量
		const additionalFilesCount = computed(() => {
			return state.existingFiles.filter((file) => !SYSTEM_FILE_TYPES.includes(file.fileType)).length;
		});

		const showUploadArea = computed(() => {
			if (state.uploadParams.fileType === '' || state.uploadParams.fileType === 'CombineFile') {
				return false;
			}
			return true;
		});

		const openDialog = (invoice: any, fileType: string) => {
			state.dialogVisible = true;
			state.currentInvoice = invoice;
			state.newFiles = [];
			state.uploadParams.fileType = fileType;
			state.uploadParams.invoiceId = invoice.invoiceId;
			state.action = `${appSettings.BASE_URL}Api/WWE_Shipment_File/UpLoadShipmentPDF`;

			loadPDF();
		};

		const closeDialog = () => {
			state.dialogVisible = false;
			emit('complete', { success: false });
		};

		const handleDialogClosed = () => {
			state.newFiles = [];
			state.existingFiles = [];
			state.currentInvoice = null;
			state.uploadParams.fileType = '';
		};

		const formatFileSize = (bytes: number) => {
			if (!bytes) return '0 K';
			const k = 1024;
			const sizes = ['K', 'K', 'MB', 'GB'];
			const i = Math.floor(Math.log(bytes) / Math.log(k));
			return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
		};

		const formatDate = (dateString: string) => {
			if (!dateString) return '';
			const date = new Date(dateString);
			return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
		};

		const previewPDF = async (row: any) => {
			console.log('previewPDF row:', row);
			const apiMethod = row.fileId ? (params: { fileId: string }) => fileApi.Download(params.fileId) : row.documentId ? (params: { documentId: string }) => fileApi.Document(params.documentId) : row.fileName ? (params: { name: string }) => fileApi.Combined(params.name) : null;

			if (!apiMethod) {
				ElMessage.warning('Invalid file ID');
				return;
			}

			viewerRef.value.openPreview({
				api: {
					method: apiMethod,
					params: row.fileId ? { fileId: row.fileId } : row.documentId ? { documentId: row.documentId } : { name: row.fileName },
				},
				fileName: row.fileName,
				displayName: row.displayName,
				title: row.displayName,
			});
		};

		const confirmDeleteFile = (row: any) => {
			console.log('confirmDeleteFile row:', row);
			ElMessageBox.confirm(`Delete ${row.displayName} permanently?`, 'Confirm', {
				confirmButtonText: 'Delete',
				cancelButtonText: 'Cancel',
				type: 'warning',
			})
				.then(() => {
					var request = {
						shipmentFileId: row.shipmentFileId,
						documentType: state.uploadParams.fileType,
						documentId: row.documentId,
						shipmentId: row.shipmentId,
					};

					shipmentFileApi
						.DeleteFile(request)
						.then(() => {
							state.existingFiles = state.existingFiles.filter((f) => f.shipmentFileId !== row.shipmentFileId);
							emit('complete', {
								success: true,
								action: 'delete',
							});
							ElMessage.success('File deleted');
						})
						.catch(() => {
							ElMessage.error('Delete failed');
						});
				})
				.catch(() => {});
		};

		const onDownload = (row: any) => {
			if (row.fileId) {
				downloadResource(row.fileId, row.fileName, fileApi.Download);
			} else if (row.documentId) {
				downloadResource(row.documentId, row.fileName, fileApi.Document);
			}else{
				downloadResource(row.fileName, row.fileName, fileApi.Combined);
			}
		};

		const downloadResource = async (id: string, originalFileName: string, apiMethod: (id: string) => Promise<any>) => {
			try {
				const response = await apiMethod(id);
				// 内联文件名提取逻辑
				const contentDisposition = response.headers['content-disposition'];
				//console.log(' response.headers:',  response.headers);
				const fileExtension = originalFileName?.split('.').pop() || '';
				let fileName = `default${fileExtension ? '.' + fileExtension : ''}`;

				if (contentDisposition) {
					const fileNameMatch = contentDisposition.match(/filename\*?=["']?(?:UTF-\d['"]*)?([^;\r\n"']*)["']?/i);
					if (fileNameMatch && fileNameMatch[1]) {
						fileName = fileNameMatch[1];
					}
				}

				// 内联文件下载逻辑
				const url = window.URL.createObjectURL(new Blob([response.data]));
				const a = document.createElement('a');
				a.href = url;
				a.download = fileName;
				document.body.appendChild(a);
				a.click();
				document.body.removeChild(a);
				window.URL.revokeObjectURL(url);
			} catch (error) {
				console.error('Export failed:', error);
				ElMessage.error('Export failed. Please try again later.');
			}
		};

		const loadPDF = () => {
			state.uploadLoading = true;
			shipmentFileApi
				.DetailList(state.uploadParams)
				.then((response) => {
					state.existingFiles = response.data || [];
				})
				.finally(() => {
					state.uploadLoading = false;
				});
		};

		const onUploadSuccess = () => {
			state.uploadLoading = false;
			state.newFiles = [];
			loadPDF();
			emit('complete', {
				success: true,
				action: 'delete',
			});
		};

		const onBeforeUpload = (file: any) => {
			state.uploadLoading = true;
			console.log('onBeforeUpload file:', file);
			return true;
		};

		const handleFileTypeChange = (type: string) => {
			state.uploadParams.fileType = type;
			loadPDF();
		};

		const getFileTypeTagType = (type: string) => {
			switch (type) {
				case 'POD':
					return 'success';
				case 'BOL':
					return 'primary';
				case 'Others':
					return 'warning';
				default:
					return 'info';
			}
		};

		const getUploadButtonText = () => {
			if (state.uploadParams.fileType === 'Additional') return 'upload additional pdf';
			if (isSystemFileMissing.value) return 'upload missing file';
			return 'Upload';
		};

		const isSystemFile = (file: any) => {
			return SYSTEM_FILE_TYPES.includes(file.fileType);
		};

		return {
			...toRefs(state),
			viewerRef,
			filteredFiles,
			showUploadArea,
			isSystemFileMissing,
			additionalFilesCount,
			onUploadSuccess,
			openDialog,
			closeDialog,
			handleDialogClosed,
			formatFileSize,
			formatDate,
			previewPDF,
			confirmDeleteFile,
			loadPDF,
			handleFileTypeChange,
			getFileTypeTagType,
			getUploadButtonText,
			isSystemFile,
			onDownload,
			onBeforeUpload,
		};
	},
});
</script>

<style lang="scss" scoped>
.pdf-upload-container {
	position: relative;
}

.pdf-upload-dialog {
	.dialog-header {
		display: flex;
		align-items: center;
		gap: 12px;
		margin-bottom: 16px;

		h3 {
			margin: 0;
			font-size: 16px;
			font-weight: 500;
		}

		.tip-text {
			margin: 4px 0 0;
			font-size: 13px;
			color: var(--el-text-color-secondary);
		}
	}

	.file-type-selector {
		background-color: #f5f7fa;
		padding: 8px 12px;
		border-radius: 4px;
		border: 1px solid var(--el-border-color-light);

		.el-radio-group {
			width: 100%;

			.el-radio-button {
				flex: 1;
				text-align: center;

				:deep(.el-radio-button__inner) {
					width: 100%;
				}
			}
		}
	}

	.upload-area {
	}

	.form-tip {
		display: flex;
		align-items: center;
		gap: 6px;
		font-size: 12px;
		color: var(--el-text-color-secondary);

		.el-icon {
			font-size: 14px;
		}
	}

	.uploaded-files-table {
		border: 1px solid var(--el-border-color);
		border-radius: 4px;
		overflow: hidden;

		.el-table {
			:deep(.el-table__cell) {
				padding: 8px 0;
			}
		}
	}
}

.dialog-footer {
	display: flex;
	justify-content: flex-end;
}

.mr-1 {
	margin-right: 4px;
}

.mb-4 {
	margin-bottom: 16px;
}

.mt-2 {
	margin-top: 8px;
}
</style>
