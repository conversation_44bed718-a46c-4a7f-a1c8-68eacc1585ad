<template>
	<el-drawer v-model="dialogVisible" :title="`View Comments`" direction="rtl" size="50%" :before-close="handleClose" class="comments-drawer" :destroy-on-close="true">
		<!-- Main content -->
		<div class="drawer-content">
			<!-- Comments list -->
			<el-scrollbar class="comments-scrollbar" v-loading="loadingComments">
				<div v-if="comments.length === 0" class="empty-comments">
					<el-empty :description="t('message.ticket.noComments')" />
				</div>

				<el-timeline v-else class="comments-timeline">
					<el-timeline-item :timestamp="comment.createdAt" placement="top" v-for="comment in comments" :key="comment.id">
						<div class="tl-item">
							<div class="timeline-avatar">
								<el-avatar :size="50" src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png" />
							</div>
							<div class="timeline-content">
								<div class="timeline-conte_name">
									<h5>Created By: {{ comment.createdById === 0 ? 'System' : comment.fullName }}</h5>
								</div>
								<div v-if="comment.files.length > 0" style="position: relative">
									<el-icon class="ele-icon">
										<ele-Link />
									</el-icon>
									<span>:</span>
									<el-popover placement="right" :width="400" trigger="click">
										<template #reference>
											<el-link type="primary">Attachments ({{ comment.files.length }})</el-link>
										</template>
										<el-table :data="comment.files">
											<el-table-column property="originalName" label="Name">
												<template #default="{ row }">
													<el-icon class="ele-icon"><ele-Download /></el-icon>
													<el-link class="link_text" @click="downloadFile(row)">{{ row.originalName }}</el-link>
													<el-tag v-if="showPreviewFile(row.ext)" class="ml-2 ml10" type="warning" style="cursor: pointer" @click="handleFilePreview(comment.files, row)">preview</el-tag>
												</template>
											</el-table-column>
										</el-table>
										<el-image-viewer v-if="previewVisibleRef" :url-list="previewSrcList" :initial-index="previewIndexRef" @close="closeImagePreview" />
									</el-popover>
								</div>

								<p v-html="comment.content"></p>

								<el-divider />
							</div>
						</div>
					</el-timeline-item>
				</el-timeline>
			</el-scrollbar>

			<!-- Add comment form -->
			<div class="comment-form">
				<el-collapse v-model="activeCollapse">
					<el-collapse-item name="comment" title="Add Comments">
						<el-form ref="formRef" :model="formData" size="default" @submit.prevent="onSubmit">
							<el-form-item>
								<scUploadFile name="files" v-model="fileList" :action="action"></scUploadFile>
							</el-form-item>

							<el-form-item prop="content">
								<EditorNote v-model:modelValue="editorNote.htmlVal" v-model:get-text="editorNote.textVal" :disable="editorNote.disable" :height="editorNote.height" :mode="editorNote.mode" :placeholder="t('message.ticket.commentPlaceholder')" />
							</el-form-item>

							<el-form-item class="form-actions">
								<el-button type="primary" @click="onSubmit" :loading="loading" :disabled="editorNote.textVal === ''">
									{{ t('message.page.buttonSave') }}
								</el-button>
								<el-button @click="onCancel">
									{{ t('message.page.buttonCancel') }}
								</el-button>
							</el-form-item>
						</el-form>
					</el-collapse-item>
				</el-collapse>
			</div>
		</div>
		<FilePreview ref="viewerRef" />
	</el-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, nextTick } from 'vue';
import { useI18n } from 'vue-i18n';
import { ElMessage } from 'element-plus';
import { Local } from '/@/utils/storage';
import commentApi from '/@/api/WWE_Comment/index';
import EditorNote from '/@/components/editor/noteV5.vue';
import { CommentAddOrUpdateDto } from '/@/models/CommentListDto';
import appSettings from '/@/config/index.js';
import FilePreview from '/@/components/file/FilePreview.vue';
import fileApi from '/@/api/Files';

const { t } = useI18n();
const props = defineProps({
	entityId: String,
	entityType: {
		type: String,
		default: 'Invoice',
		validator: (value: string) => ['Invoice', 'Shipment'].includes(value),
	},
});

const emit = defineEmits(['action']);

const viewerRef = ref();
const dialogVisible = ref(false);
const loadingComments = ref(false);
const loading = ref(false);
const activeCollapse = ref(['comment']);
const comments = ref([]);
const fileList = ref([]);
const action = computed(() => {
	return `${appSettings.BASE_URL}Api/Files/UpLoad`;
});

const editorNote = reactive({
	htmlVal: '',
	textVal: '',
	disable: false,
	height: '100px',
	mode: 'simple',
});

const formData = reactive({
	fileIds: [] as number[],
});

const entityId = computed(() => props.entityId);

const previewVisibleRef = ref(false);
const previewIndexRef = ref(0);
const previewSrcList = ref();

const open = () => {
	dialogVisible.value = true;
	nextTick(() => {
		fetchComments();
	});
};

const fetchComments = () => {
	if (!entityId.value || entityId.value <= 0) return;

	loadingComments.value = true;
	const params = {
		...(props.entityType === 'Invoice' ? { invoiceId: entityId.value } : { shipmentId: entityId.value }),
		order: 'createdAt',
		sort: 'desc',
	};

	commentApi
		.DetailList(params)
		.then((response) => {
			comments.value = response.data;
		})
		.catch(() => {
			ElMessage.error(t('message.ticket.loadCommentsFailed'));
		})
		.finally(() => {
			loadingComments.value = false;
		});
};

const onSubmit = () => {
	if (editorNote.textVal === '') {
		ElMessage.error(t('message.ticket.commentRequired'));
		return;
	}

	loading.value = true;

	const dto: CommentAddOrUpdateDto = {
		[props.entityType === 'Invoice' ? 'InvoiceId' : 'ShipmentId']: entityId.value,
		content: editorNote.htmlVal,
		fileIds: fileList.value.map((file) => file.fileId),
	};

	commentApi
		.Save(dto)
		.then(() => {
			resetForm();
			fetchComments();
			emit('action', 'refresh');
		})
		.catch(() => {
			ElMessage.error(t('message.ticket.saveFailed'));
		})
		.finally(() => {
			loading.value = false;
		});
};

const handleFilePreview = async (files: any[], row: any) => {
	console.log('handleFilePreview', row);
	if (!compareImg(row.ext)) {
		// 非图片文件，用 FilePreview 组件
		viewerRef.value.openPreview({
			api: {
				method: (params: { id: string }) => fileApi.Download(params.id),
				params: { id: row.fileId },
			},
			fileName: row.name,
			displayName: row.originalName,
			title: row.originalName,
		});
		return;
	}
	// 1. 获取所有图片的 Blob
	const imageFiles = files.filter((file) => compareImg(file.ext));
	const imageBlobs = await Promise.all(
		imageFiles.map(async (file) => {
			const res = await fileApi.Download(file.fileId);
			return new Blob([res.data], { type: res.headers['content-type'] });
		})
	);

	// 2. 生成 Blob URL
	previewSrcList.value = imageBlobs.map((blob) => URL.createObjectURL(blob));

	// 3. 设置当前图片索引
	const currentFileIndex = imageFiles.findIndex((file) => file.fileId === row.fileId);
	previewIndexRef.value = currentFileIndex;

	// 4. 显示预览
	previewVisibleRef.value = true;
};

const showPreviewFile = (type: string) => {
	const fileTypes = ['bmp', 'jpg', 'png', 'gif', 'svg', 'webp', 'ico', 'jpeg', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'mp3', 'mp4'];
	return fileTypes.includes(type.toLowerCase());
};
const compareImg = (ext: string) => {
	const imageExtensions = ['bmp', 'jpg', 'png', 'gif', 'svg', 'webp', 'ico', 'jpeg'];
	return imageExtensions.includes(ext.toLowerCase());
};

const closeImagePreview = () => {
	previewVisibleRef.value = false;
};

const downloadFile = (row: any) => {
	fileApi.Download(row.fileId).then((response) => {
		const blob = new Blob([response.data], { type: response.headers['content-type'] });
		const url = window.URL.createObjectURL(blob);
		const a = document.createElement('a');
		a.href = url;
		a.download = row.originalName;
		document.body.appendChild(a);
		a.click();
		window.URL.revokeObjectURL(url);
	});
};

const resetForm = () => {
	editorNote.htmlVal = '';
	editorNote.textVal = '';
	fileList.value = [];
};

const onCancel = () => {
	resetForm();
	handleClose();
};

const handleClose = () => {
	dialogVisible.value = false;
	emit('action', 'close');
};

defineExpose({
	open,
});
</script>

<style lang="scss" scoped>
.comments-drawer {
	.drawer-content {
		display: flex;
		flex-direction: column;
		height: 100%;
		padding: 20px;
	}

	.comments-scrollbar {
		flex: 1;
		.empty-comments {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 100%;
		}
	}

	.comment-form {
		border-top: 1px solid var(--el-border-color-light);
		background-color: var(--el-bg-color-page);
	}

	.form-actions {
		margin-top: 20px;
		text-align: right;
	}
}
</style>
