<template>
	<div class="system-edit-menu-container">
		<el-drawer v-model="state.showDialog" destroy-on-close :title="title" draggable :close-on-click-modal="false" :close-on-press-escape="false" width="500px" :direction="direction">
			<div class="drawer-content">
				<el-form :model="state.form" ref="formRef" :rules="state.rules" label-width="130px" label-position="top">
					<el-row :gutter="35">
						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.wWE_InvoiceFields.InvoiceId')" prop="invoiceId">
								<el-input v-model="state.form.invoiceId" clearable size="default" />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.wWE_InvoiceFields.AccountId')" prop="accountId">
								<el-input v-model="state.form.accountId" clearable size="default" />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.wWE_InvoiceFields.CarrierId')" prop="carrierId">
								<el-input v-model="state.form.carrierId" clearable size="default" />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.wWE_InvoiceFields.InvoiceNumber')" prop="invoiceNumber">
								<el-input v-model="state.form.invoiceNumber" clearable size="default" />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.wWE_InvoiceFields.ProNumber')" prop="proNumber">
								<el-input v-model="state.form.proNumber" clearable size="default" />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.wWE_InvoiceFields.Amount')" prop="amount">
								<el-input v-model="state.form.amount" clearable type="number" size="default" />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.wWE_InvoiceFields.DocRetrievalMethod')" prop="docRetrievalMethod">
								<el-input v-model="state.form.docRetrievalMethod" clearable size="default" />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.wWE_InvoiceFields.DocRetrievedStatus')" prop="docRetrievedStatus">
								<el-input v-model="state.form.docRetrievedStatus" clearable size="default" />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.wWE_InvoiceFields.DocRetrievalType')" prop="docRetrievalType">
								<el-input v-model="state.form.docRetrievalType" clearable size="default" />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.wWE_InvoiceFields.Status')" prop="status">
								<el-input v-model="state.form.status" clearable size="default" />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.wWE_InvoiceFields.Comment')" prop="comment">
								<el-input v-model="state.form.comment" clearable size="default" />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.wWE_InvoiceFields.CreatedByName')" prop="createdByName">
								<el-input v-model="state.form.createdByName" clearable size="default" />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.wWE_InvoiceFields.ModifiedByName')" prop="modifiedByName">
								<el-input v-model="state.form.modifiedByName" clearable size="default" />
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>

				<div class="drawer-footer">
					<span class="dialog-footer">
						<el-button @click="onCancel" size="small">{{ $t('message.page.buttonCancel') }}</el-button>
						<el-button :loading="state.loading" type="primary" @click="onSubmit" size="small">{{ $t('message.page.buttonSave') }}</el-button>
					</span>
				</div>
			</div>
		</el-drawer>
	</div>
</template>

<script lang="ts" setup name="wWE_Invoice/createOrEdit">
import { reactive, toRefs, ref, getCurrentInstance, computed, watch } from 'vue';

import { cloneDeep } from 'lodash-es';

import { useI18n } from 'vue-i18n';

import { ElMessageBox, ElMessage } from 'element-plus';

import type { DrawerProps } from 'element-plus';

import { formatNumberWithDecimal, formatDecimal } from '/@/utils/toolsValidate';
import DateSelector from '/@/components/common/DateSelector.vue';

import wWE_InvoiceApi from '/@/api/WWE_Invoice/index';

interface CreateOrEditInput {
	invoiceId: number;
	accountId: number;
	carrierId: number;
	invoiceNumber: string;
	proNumber: string;
	amount: string;
	docRetrievalMethod: string;
	docRetrievedStatus: string;
	docRetrievalType: string;
	status: string;
	comment: string;
	createdByName: string;
	modifiedByName: string;
}

defineProps({
	title: {
		type: String,
		default: '',
	},
});

const emit = defineEmits(['refreshData']);

const direction = ref<DrawerProps['direction']>('rtl');

const { t } = useI18n();
const formRef = ref();

const state = reactive({
	showDialog: false,
	loading: false,
	form: {} as CreateOrEditInput,
	rules: {
		invoiceId: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: ['blur', 'change'] }],
		accountId: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: ['blur', 'change'] }],
		carrierId: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: ['blur', 'change'] }],
		invoiceNumber: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: ['blur', 'change'] }],
		proNumber: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: ['blur', 'change'] }],
		amount: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: ['blur', 'change'] }],
		docRetrievalMethod: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: ['blur', 'change'] }],
		docRetrievedStatus: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: ['blur', 'change'] }],
		docRetrievalType: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: ['blur', 'change'] }],
		status: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: ['blur', 'change'] }],
		createdByName: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: ['blur', 'change'] }],
	},
});

const open = async (row: CreateOrEditInput) => {
	let formData = cloneDeep(row) as CreateOrEditInput;

	state.form = formData;

	let amount = String(state.form.amount);
	state.form.amount = formatNumberWithDecimal(amount);

	state.showDialog = true;
};

const onCancel = () => {
	closeDialog();
};

const closeDialog = () => {
	onInitForm();
	state.showDialog = false;
};

const onSubmit = async () => {
	formRef.value.validate(async (valid: boolean) => {
		if (!valid) return;

		state.loading = true;

		let obj = state.form;

		await wWE_InvoiceApi
			.Save(obj)
			.then(() => {
				ElMessage.success(t('message.page.Success'));
				emit('refreshData');

				closeDialog();
				state.loading = false;
			})
			.catch((error: HandledError) => {
				if (!error.isHandled) {
					const errorCode = error.code;
					const errorMessage = error.message;
					ElMessage.error(errorMessage);
				}
			})
			.finally(() => {
				state.loading = false;
			});
	});
};

const onInitForm = async () => {};

watch(
	() => state.form.amount,
	(newValue: any) => {
		state.form.amount = formatDecimal(newValue);
	}
);

defineExpose({
	open,
});
</script>

<style scoped>
.drawer-content {
	padding: 20px;
}

.drawer-footer {
	text-align: right;
}
</style>
