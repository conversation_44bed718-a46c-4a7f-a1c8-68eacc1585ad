import { Local, Session } from '/@/utils/storage';
import loginApi from '/@/api/login';
import { storeToRefs } from 'pinia';
import { useUserInfo } from '/@/stores/userInfo';
import mittBus from '/@/utils/mitt';
import { debugLog, isNotEmptyOrNull } from '/@/utils';
import { LocalStorageKeys, URLs } from '../constants';

const stores = useUserInfo();
const { userInfos } = storeToRefs(stores);

export function startRefreshingToken() {
	setInterval(
		() => {
			const webSystemUrl = import.meta.env.VITE_PUBLIC_PATH;

			let refreshToken = Local.get(LocalStorageKeys.RefreshToken);

			let userName = userInfos.value.userName;

			if (!refreshToken || !userName) return;

			loginApi
				.RefreshToken({ token: refreshToken, userName: userName })
				.then((rs) => {
					if (!rs.data) {
						Local.clearAndLogin();
						window.location.href = URLs.LOGIN;
						return;
					}

					const accessToken = rs.data.token;
					const accessTokenExp = rs.data.exp;
					const refreshToken = rs.data.refreshToken;

					Local.set(LocalStorageKeys.Token, accessToken);
					Local.set(LocalStorageKeys.TokenExp, accessTokenExp);
					Local.set(LocalStorageKeys.RefreshToken, refreshToken);
				})
				.catch((error: HandledError) => {
					const errorMessage = error.message;
					console.log('startRefreshingToken', errorMessage);
				});
		},
		1000 * 60 * 5
	); // 5分钟前端自动刷新一次token以及检测是否有用户1个小时候未使用系统，因为后台设置Token的过期时间是1小时
}

export function startRefreshingVersion() {
	setInterval(async () => {
		try {
			const response = await fetch('config.js?' + new Date().getTime());
			if (response.ok) {
				const data = await response.json();

				if (Local.get('version') && Local.get('version') !== data.version) {
					Local.set('version', data.version);
					mittBus.emit('versionUpdated');
				}
			} else {
				console.error('Failed to load config data');
			}
		} catch (error) {
			console.error('Error fetching config data:', error);
		}
	}, 1000 * 1);
}
