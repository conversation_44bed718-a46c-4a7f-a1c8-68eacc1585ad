<template>
	<div class="upload-ftp-container">
		<el-dialog v-model="dialogVisible" title="Upload Files to FTP" class="upload-ftp-dialog" width="650px" :close-on-click-modal="false" draggable @closed="handleDialogClosed">
			<div class="dialog-header">
				<p class="tip-text">Select records to prepare for FTP upload</p>
				<div>
					<el-radio-group v-model="generateType" size="default" @change="handleGenerateTypeChange">
						<el-radio label="selected">Selected Items</el-radio>
						<el-radio label="query">Current Query</el-radio>
					</el-radio-group>
				</div>
			</div>

			<div class="content-section">
				<el-table :data="displayedRecords" border style="width: 100%">
					<el-table-column prop="total" label="Total Records" align="center" />
					<el-table-column prop="ready" label="Ready for Upload" align="center" />
					<template #empty>
						<div class="empty-text">No records available</div>
					</template>
				</el-table>
			</div>

			<template #footer>
				<el-button @click="dialogVisible = false">Cancel</el-button>
				<el-button type="primary" :loading="processing" :disabled="displayedRecords.length === 0 || hasProcessed" @click="startProcessing">
					{{ hasProcessed ? 'Processing Complete' : 'Prepare FTP Upload' }}
				</el-button>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts">
import { defineComponent, reactive, computed, toRefs } from 'vue';
import { ElMessage } from 'element-plus';
import invoiceApi from '/@/api/WWE_Invoice';

export default defineComponent({
	name: 'UploadToFTPDialog',
	props: {
		tableRef: {
			type: Object,
			required: true,
		},
		queryParams: {
			type: Object,
			required: false,
		},
	},
	emits: ['complete'],
	setup(props, { emit }) {
		const state = reactive({
			dialogVisible: false,
			generateType: 'selected',
			allRecords: [] as any[],
			processing: false,
			hasProcessed: false,
		});

		const displayedRecords = computed(() => state.allRecords);

		const openDialog = async () => {
			state.dialogVisible = true;
			state.generateType = 'selected';
			state.hasProcessed = false;
			await loadSelectedRecords();
		};

		const handleGenerateTypeChange = async () => {
			state.hasProcessed = false;
			if (state.generateType === 'selected') {
				await loadSelectedRecords();
			} else {
				await loadQueryRecords();
			}
		};

		const loadSelectedRecords = () => {
			const selected = props.tableRef.getCheckboxRecords?.() || [];
			console.log('Selected Records:', selected);
			// 过滤掉 FtpUploaded 为 true 的记录
			const filteredRecords = selected.filter((record) => !record.ftpUploaded && record.combineFile);

			state.allRecords = [
				{
					total: filteredRecords.length,
					ready: 0,
				},
			];
		};

		const loadQueryRecords = async () => {
			state.allRecords = [];
			const response = await invoiceApi.GetFTPUploadSummary(props.queryParams);
			state.allRecords = [
				{
					total: response.data.total,
					ready: 0,
				},
			];
		};

		const startProcessing = async () => {
			if (state.allRecords.length === 0) return;
			state.processing = true;

			try {
				let requestPayload: any;

				if (state.generateType === 'selected') {
					const selected = props.tableRef.getCheckboxRecords?.() || [];
					const invoiceIds = selected.map((r: any) => r.invoiceId);
					requestPayload = {
						uploadType: 'selected',
						invoiceIds,
					};
				} else {
					requestPayload = {
						uploadType: 'query',
						...props.queryParams,
					};
				}

				const res = await invoiceApi.PrepareFTPUpload(requestPayload);

				if (res.resultCode === 200 && res.succeeded && res.data) {
					state.allRecords = [
						{
							total: res.data.total,
							ready: res.data.ready,
						},
					];
					state.hasProcessed = true;
					ElMessage.success('Files prepared for FTP upload successfully');
					emit('complete', res.data);
				} else {
					state.allRecords = [
						{
							total: res.data?.total || 0,
							ready: 0,
						},
					];
					ElMessage.error('Failed to prepare files for FTP upload');
				}
			} catch (e) {
				console.error(e);
				state.allRecords = [
					{
						total: 0,
						ready: 0,
					},
				];
				ElMessage.error('Error occurred while preparing FTP upload');
			} finally {
				state.processing = false;
			}
		};

		const handleDialogClosed = () => {
			state.allRecords = [];
			state.processing = false;
			state.hasProcessed = false;
		};

		return {
			...toRefs(state),
			openDialog,
			handleDialogClosed,
			displayedRecords,
			handleGenerateTypeChange,
			startProcessing,
		};
	},
});
</script>

<style lang="scss" scoped>
.upload-ftp-dialog {
	.tip-text {
		margin-bottom: 20px;
	}
	.content-section {
		margin-top: 12px;
		height: 150px;
		overflow-y: auto;
	}
	.empty-text {
		text-align: center;
		padding: 12px 0;
		color: var(--el-text-color-secondary);
		font-size: 14px;
	}
}
</style>
