
export default {
    wWE_Carrier_LoginSearch: {
        //Search Area
    },
    wWE_Carrier_LoginButtons: {
        createWWE_Carrier_Login: 'New',
        editWWE_Carrier_Login: 'Edit',
        importWWE_Carrier_Login: 'Import',
    },
    wWE_Carrier_LoginLabels: {
		Id: 'Id',
		AccountId: 'Account',
		AccountName: 'Company Account',
		CarrierId: 'Carrier',
		CarrierName: 'Carrier Name',
		CarrierCode: 'Carrier#',
		SCAC:'SCAC',
		ContactEmail: 'Contact Email',
		ContactPhone: 'Contact Phone',
		Login: 'Login',
		Password: 'Password',
		RequiredFileTypes: 'Custom File Types',
		CreatedById: 'Created By Id',
		CreatedByName: 'Created By',
		CreatedAt: 'Created At',
		ModifiedById: 'Modified By Id',
		ModifiedByName: 'Modified By',
		ModifiedAt: 'Modified At',
		IsDeleted: 'Is Deleted'
    },
    wWE_Carrier_LoginFields: {
		Id: 'Id',
		AccountId: 'Account Id',
		AccountName: 'Company Account',
		CarrierId: 'Carrier Id',
		CarrierName: 'Carrier Name',
		CarrierCode: 'Carrier#',
		SCAC:'SCAC',
		ContactEmail: 'Contact Email',
		ContactPhone: 'Contact Phone',
		Login: 'Login',
		Password: 'Password',
		RequiredFileTypes: 'Custom File Types',
		CreatedById: 'Created By Id',
		CreatedByName: 'Created By',
		CreatedAt: 'Created At',
		ModifiedById: 'Modified By Id',
		ModifiedByName: 'Modified By',
		ModifiedAt: 'Modified At',
		IsDeleted: 'Is Deleted'
    },
    wWE_Carrier_LoginDlgTips: {
        deleteError: 'There is already a configuration relationship, deletion is not allowed!',
    },
    wWE_Carrier_LoginCommon: {
        ImportData: 'Import Data',
		UploadExcelFile: 'Upload Excel File',
        UploadCsvFile: 'Upload Csv File',
		Note: '(Note: This action will overwrite the data of the selected month)'
	},
};
            