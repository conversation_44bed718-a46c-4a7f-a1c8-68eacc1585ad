﻿<template>
	<div class="permission-table-container">
		<div class="foot-btns">
			<el-button v-auth="'systemRole.CreatePermission'" @click="onOpenMenuButtons" type="primary" size="small">
				{{ $t('message.menuCommonFields.createPermission') }}
			</el-button>
			<el-button v-auth="'system.SavePermissions'" @click="onSave" type="primary" size="small" :loading="saveLoading">
				{{ $t('message.page.buttonSavePermissions') }}
			</el-button>
		</div>
		<el-table ref="tableRef" :data="treePermissionData" v-loading="tableLoading" row-key="id" height="calc(100vh - 350px)" :default-expand-all="true" :tree-props="{ children: 'children' }" @select="onSelect" @select-all="onSelectAll" :row-style="{ height: '40px' }" :cell-style="{ padding: '0px' }">
			<template #empty>
				<el-empty :description="$t('message.page.emptyDescription')" :image-size="100" />
			</template>

			<el-table-column type="selection" />

			<el-table-column :label="$t('message.permissionTableFields.name')" width="200" show-overflow-tooltip>
				<template #default="{ row }">
					{{ $t(row.title) }}
				</template>
			</el-table-column>

			<el-table-column :label="$t('message.permissionTableFields.permissions')">
				<template #default="{ row }">
					<el-checkbox-group v-model="checkList">
						<el-checkbox v-for="item in row.permissions" :key="item.id" :label="item.id" :disabled="disableCheckBox(row.title, item.id)" @change="(e) => checkOne(e, item.id)">
							<el-dropdown @command="handleCommand($event, item)">
								<span class="el-dropdown-link">{{ $t(item.title) }}</span>
								<template #dropdown>
									<Auth :value="'systemRole.EditPermission'">
										<el-dropdown-menu>
											<el-dropdown-item command="editPermissions">{{ $t('message.menuCommonFields.editPermission') }}</el-dropdown-item>
										</el-dropdown-menu>
									</Auth>
								</template>
							</el-dropdown>
						</el-checkbox>
					</el-checkbox-group>
				</template>
			</el-table-column>
		</el-table>
		<MenuButtons ref="menuButtonsRef" @fetchData="refreshData(roleObj)" />
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, getCurrentInstance, ref, defineComponent } from 'vue';
import { ElMessage } from 'element-plus';
import MenuButtons from '/@/views/bpts/menu/component/buttonCreateOrEdit.vue';
import roleModulePermissionApi from '/@/api/roleModulePermission';
import menuApi from '/@/api/menu/list';
import { Consts } from '/@/constants';
import Auth from '/@/components/auth/auth.vue';

const LOCKED_PERMISSION_IDS = [1025, 1377, 1036,1037,1177]; // Base Module - Visit 等权限ID

export default defineComponent({
	name: 'roleModulePermissionCreateOrEdit',
	components: { MenuButtons, Auth },
	setup(_, context) {
		const { proxy } = getCurrentInstance() as any;
		const menuButtonsRef = ref();
		const tableRef = ref();
		const state = reactive({
			tableLoading: false,
			saveLoading: false,
			roleObj: { id: 0 },
			treePermissionData: [],
			checkList: [],
		});

		const refreshData = (params: any) => {
			state.roleObj = params;
			state.checkList = [];
			state.tableLoading = true;

			menuApi
				.PermissionTree()
				.then((res) => {
					state.treePermissionData = res.data;
				})
				.finally(() => {
					state.tableLoading = false;
				});

			roleModulePermissionApi.GetPermissionList(state.roleObj.id, 3).then((res) => {
				state.checkList = Array.from(new Set([...res.data, ...LOCKED_PERMISSION_IDS]));
			});
		};

		const onSave = async () => {
			if (state.checkList.length === 0) {
				ElMessage.error('Select at least one');
				return;
			}
			state.saveLoading = true;
			roleModulePermissionApi
				.Assign({ menus: state.checkList, roleId: state.roleObj.id })
				.then(() => ElMessage.success('Succeed'))
				.finally(() => {
					state.saveLoading = false;
					context.emit('fetchData');
				});
		};

		const onSelect = (selection, row) => {
			const isSelect = selection.some((el) => row.id === el.id);
			toggleTree(row, isSelect);
		};

		const onSelectAll = (selection) => {
			const selectedIds = selection.map((item) => item.id);
			const isSelect = state.treePermissionData.some((item) => selectedIds.includes(item.id));
			state.treePermissionData.forEach((row) => toggleTree(row, isSelect));
		};

		const toggleTree = (row, isChecked) => {
			refreshCheckList(row, isChecked);
			if (row.children && row.children.length > 0) {
				row.children.forEach((child) => toggleTree(child, isChecked));
			}
		};

		const refreshCheckList = (row, isChecked) => {
			const ids = row.permissions?.map((p) => p.id) || [];
			ids.forEach((id) => {
				const idx = state.checkList.indexOf(id);
				if (isChecked) {
					if (idx === -1) state.checkList.push(id);
				} else {
					if (idx !== -1 && !LOCKED_PERMISSION_IDS.includes(id)) {
						state.checkList.splice(idx, 1);
					}
				}
			});
		};

		const onOpenMenuButtons = () => {
			menuButtonsRef.value.openDialog(null);
		};

		const handleCommand = (command: string, row: any) => {
			if (command === 'editPermissions') {
				menuButtonsRef.value.openDialog(row, Consts.Action_GetById);
			}
		};

		const checkOne = (_checked: boolean, id: number) => {
			// 个别权限变动已由 v-model 处理
		};

		const disableCheckBox = (title: string, id: number) => {
			return LOCKED_PERMISSION_IDS.includes(id);
		};

		return {
			...toRefs(state),
			menuButtonsRef,
			tableRef,
			onSave,
			refreshData,
			onOpenMenuButtons,
			handleCommand,
			onSelect,
			onSelectAll,
			checkOne,
			disableCheckBox,
		};
	},
});
</script>

<style scoped>
.permission-table-container {
	width: 100%;
}
.foot-btns {
	background-color: #fff;
	padding: 0 0 10px;
	height: 40px;
	display: flex;
	justify-content: flex-end;
	border-bottom: 1px solid #e6e6e6;
}
</style>
