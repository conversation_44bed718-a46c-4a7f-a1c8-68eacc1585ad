<template>
	<el-dialog v-model="dialogVisible" title="Send Shipment Email" width="550px" :before-close="handleClose" destroy-on-close>
		<div class="dialog-header">
			<p class="tip-text">Records are grouped by carrier, but will be processed together</p>
		</div>

		<el-form :inline="true" class="filter-form" v-if="actionType === 'export'">
			<el-form-item label="Data Source:">
				<el-radio-group v-model="dataSource" @change="loadData">
					<el-radio label="selected">Selected Items</el-radio>
					<el-radio label="query">Current Query</el-radio>
				</el-radio-group>
			</el-form-item>
		</el-form>

		<vxe-table border :data="groupedRows" :loading="loading" class="data-table" show-overflow :span-method="mergeResultColumn">
			<vxe-column field="carrier" title="Carrier" min-width="150" />
			<vxe-column field="total" title="Total" width="100" />
			<vxe-column field="status" title="Result" width="120">
				<template #default="{ row, rowIndex }">
					<template v-if="rowIndex === 0">
						<template v-if="status === 'success' && fileId">
							<el-button type="primary" size="small" @click="onDownload">Download All</el-button>
						</template>
						<template v-else-if="status === 'success'">
							<span style="color: green">Sent</span>
						</template>
						<template v-else-if="status === 'error'">
							<span style="color: red">Failed</span>
						</template>
						<template v-else-if="status === 'pending'">
							<el-icon class="is-loading"><Loading /></el-icon>
							<span style="margin-left: 5px">Processing...</span>
						</template>
						<template v-else>
							<span style="color: #999">-</span>
						</template>
					</template>
				</template>
			</vxe-column>
		</vxe-table>

		<template #footer>
			<el-button @click="handleClose">Cancel</el-button>
			<el-button type="primary" v-if="actionType === 'export'" :loading="processing" @click="() => processGroups('export')">Export All</el-button>
			<el-button type="success" v-if="actionType === 'email'" :loading="emailLoading" @click="() => processGroups('email')">Send Email</el-button>
		</template>
	</el-dialog>
</template>

<script lang="ts">
import { defineComponent, ref, computed, type PropType } from 'vue';
import { ElMessage } from 'element-plus';
import { Loading } from '@element-plus/icons-vue';
import type { VxeGridInstance } from 'vxe-table';
import invoiceApi from '/@/api/WWE_Invoice';
import invoiceEmailApi from '/@/api/WWE_Invoice_Email';
import fileApi from '/@/api/Files';

interface GroupRow {
	carrier: string;
	carrierId: string;
	total: number;
	items: any[];
	status?: string; // 保留但不使用
}

export default defineComponent({
	name: 'ExportEmailDialog',
	props: {
		tableRef: {
			type: Object as PropType<VxeGridInstance>,
			required: true,
		},
		queryParams: {
			type: Object,
			required: true,
		},
	},
	emits: ['complete'],
	setup(props, { emit }) {
		const dialogVisible = ref(false);
		const loading = ref(false);
		const emailLoading = ref(false);
		const processing = ref(false);
		const dataSource = ref('selected');
		const rawShipments = ref<any[]>([]);
		const groupedRows = ref<GroupRow[]>([]);
		const status = ref<'pending' | 'success' | 'error' | ''>('');
		const fileId = ref('');
		const actionType = ref<string | undefined>(undefined);
		const commandValue = ref<string | undefined>(undefined);

		const totalItems = computed(() => {
			return groupedRows.value.reduce((sum, group) => sum + group.total, 0);
		});

		// 合并结果列的单元格
		const mergeResultColumn = ({ row, $rowIndex, column, data }: any) => {
			if (column.property === 'status') {
				return {
					rowspan: $rowIndex === 0 ? groupedRows.value.length : 0,
					colspan: 1,
				};
			}
		};

		const openDialog = async (action: string, command?: string) => {
			actionType.value = action;
			console.log('Opening dialog with action:', action, 'and command:', command);
			commandValue.value = command;
			dialogVisible.value = true;
			dataSource.value = 'selected';
			await loadData();
		};

		const handleClose = () => {
			dialogVisible.value = false;
		};

		const loadData = async () => {
			rawShipments.value = [];
			loading.value = true;
			status.value = '';
			fileId.value = '';

			if (actionType.value && commandValue.value) {
				dataSource.value = '';
			}

			try {
				if (dataSource.value === 'selected') {
					rawShipments.value = props.tableRef?.getCheckboxRecords() || [];
				} else {
					let queryParams = { ...props.queryParams };
					if (actionType.value === 'email' && commandValue.value) {
						queryParams = {
							emailType: commandValue.value,
						};
					}
					const response = await invoiceApi.GetShipmentEmail(queryParams);
					if (response.resultCode !== 200) {
						ElMessage.warning(response.resultMsg || 'Failed to load shipments');
						return;
					}
					rawShipments.value = response.data || [];
				}

				// Group data by carrier and account
				const groups = new Map<string, GroupRow>();
				for (const item of rawShipments.value) {
					const key = `${item.carrierId}`;
					if (!groups.has(key)) {
						groups.set(key, {
							carrier: item.carrierName,
							carrierId: item.carrierId,
							total: 0,
							items: [],
						});
					}
					const group = groups.get(key)!;
					group.items.push(item);
					group.total++;
				}
				groupedRows.value = Array.from(groups.values());
			} catch (err: any) {
				ElMessage.error(err.message);
			} finally {
				loading.value = false;
			}
		};

		const processGroups = async (mode: 'export' | 'email') => {
			if (totalItems.value === 0) {
				ElMessage.warning('No items to process');
				return;
			}
			if (mode === 'email') {
				emailLoading.value = true;
			} else {
				processing.value = true;
			}

			status.value = 'pending';

			try {
				// Collect all invoice IDs from all groups
				const allInvoiceIds = groupedRows.value.flatMap((group) => group.items.map((item) => item.invoiceId));

				const response = await invoiceEmailApi.SendShipmentEmail({
					invoiceIds: allInvoiceIds,
					emailType: commandValue.value,
					sendType: mode,
				});

				if (response.resultCode === 200) {
					status.value = 'success';
					fileId.value = response.data?.fileId || '';
					ElMessage.success(mode === 'email' ? 'Email sent successfully' : 'Export completed');
					emit('complete', { success: true });
				} else {
					status.value = 'error';
					ElMessage.error(response.resultMsg || 'Operation failed');
				}
			} catch (err: any) {
				status.value = 'error';
				ElMessage.error(err.message || 'Operation failed');
			} finally {
				processing.value = false;
				emailLoading.value = false;
			}
		};

		const onDownload = () => {
			if (fileId.value) {
				downloadResource(fileId.value, `shipment_export_${new Date().toISOString().replace(/[:.]/g, '-')}.xlsx`, fileApi.Download);
			}
		};

		const downloadResource = async (id: string, originalFileName: string, apiMethod: (id: string) => Promise<any>) => {
			try {
				const response = await apiMethod(id);
				const contentDisposition = response.headers['content-disposition'];
				const fileExtension = originalFileName?.split('.').pop() || '';
				let fileName = `default${fileExtension ? '.' + fileExtension : ''}`;

				if (contentDisposition) {
					const fileNameMatch = contentDisposition.match(/filename\*?=["']?(?:UTF-\d['"]*)?([^;\r\n"']*)["']?/i);
					if (fileNameMatch && fileNameMatch[1]) {
						fileName = decodeURIComponent(fileNameMatch[1]);
					}
				}

				const url = window.URL.createObjectURL(new Blob([response.data]));
				const a = document.createElement('a');
				a.href = url;
				a.download = fileName;
				document.body.appendChild(a);
				a.click();
				document.body.removeChild(a);
				window.URL.revokeObjectURL(url);
			} catch (error) {
				console.error('Export failed:', error);
				ElMessage.error('Export failed. Please try again later.');
			}
		};

		return {
			actionType,
			commandValue,
			dialogVisible,
			loading,
			processing,
			emailLoading,
			dataSource,
			groupedRows,
			totalItems,
			status,
			fileId,
			mergeResultColumn,
			openDialog,
			handleClose,
			loadData,
			processGroups,
			onDownload,
		};
	},
});
</script>

<style scoped lang="scss">
.dialog-header {
	margin-bottom: 15px;
}
.tip-text {
	font-size: 14px;
	font-weight: 500;
	color: #666;
}
.filter-form {
	margin-bottom: 15px;
}
.data-table {
	margin-bottom: 15px;
}
.summary-section {
	margin-top: 15px;
}
</style>
