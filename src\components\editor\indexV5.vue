<template>
	<div style="border: 1px solid #ccc">
		<Toolbar style="border-bottom: 1px solid #ccc" :editor="editorRef" :defaultConfig="toolbarConfig" :mode="mode" />
		<Editor
			style="height: 150px; overflow-y: hidden"
			v-model="valueHtml"
			:defaultConfig="editorConfig"
			:mode="mode"
			@onCreated="handleCreated"
			@onChange="handleChange"
		/>
	</div>
</template>

<script lang="ts" scoped>
import '@wangeditor/editor/dist/css/style.css'; // 引入 css

import { onBeforeUnmount, ref, shallowRef, onMounted, watch } from 'vue';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import { i18nChangeLanguage } from '@wangeditor/editor';

export default {
	components: { Editor, Toolbar },
	props: {
		placeholder: {
			type: String,
			default: () => 'Please input',
		},
		modelValue: String,
	},
	setup(props, { emit }) {
		i18nChangeLanguage('en');

		// 编辑器实例，必须用 shallowRef
		const editorRef = shallowRef();

		// 内容 HTML
		const valueHtml = ref(props.modelValue);

		const toolbarConfig = {
			excludeKeys: ['fontFamily'],
		};

		const editorConfig = { placeholder: 'Please input' };

		// 组件销毁时，也及时销毁编辑器
		onBeforeUnmount(() => {
			const editor = editorRef.value;
			if (editor == null) return;
			editor.destroy();
		});

		const handleCreated = (editor) => {
			editorRef.value = editor; // 记录 editor 实例，重要！
		};

		const handleChange = (editor) => {
			emit('update:modelValue', editor.getHtml());
		};

		watch(
			() => props.modelValue,
			(value) => {
				const editor = editorRef.value;
				if (value == undefined) {
					editor.clear();
					return;
				}
				valueHtml.value = value;
			}
		);

		return {
			editorRef,
			valueHtml,
			mode: 'default', // 或 'simple'
			toolbarConfig,
			editorConfig,
			handleCreated,
			handleChange,
		};
	},
};
</script>
