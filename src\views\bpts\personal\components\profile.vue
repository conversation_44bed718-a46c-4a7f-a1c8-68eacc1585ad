<template>
	<div class="profile-content">
		<el-card shadow="never">
			<template #header>User Profile</template>
			<el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-position="top" label-width="130px">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item :label="$t('message.userFields.userName')" prop="title">
							<el-input v-model="ruleForm.userName" :readonly="true" class="readonly" size="middle" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item :label="$t('message.userFields.firstName')" prop="firstName">
							<el-input v-model="ruleForm.firstName" clearable size="middle" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item :label="$t('message.userFields.mobile')" prop="mobile">
							<el-input v-model="ruleForm.mobile" clearable size="middle" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item :label="$t('message.userFields.lastName')" prop="lastName">
							<el-input v-model="ruleForm.lastName" clearable size="middle" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item :label="$t('message.userFields.email')" prop="email">
							<el-input v-model="ruleForm.email" :readonly="true" class="readonly" size="middle" />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item :label="$t('message.userFields.roleAssign')">
							<el-select v-model="ruleForm.roles" clearable multiple :disabled="true"
								:placeholder="$t('message.page.selectKeyPlaceholder')" style="width: 100%"
								size="middle">
								<el-option v-for="item in roleOptions" :key="item.id" :label="item.name"
									:value="item.id" />
							</el-select>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item :label="$t('message.userFields.timeZone')">
							<el-select v-model="ruleForm.timeZone"
								:placeholder="$t('message.page.selectKeyPlaceholder')" clearable style="width: 100%"
								size="middle">
								<el-option v-for="item in timeZoneOptions" :key="item.itemName" :label="item.itemName"
									:value="item.itemName" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item :label="$t('message.userFields.jobTitle')" clearable prop="jobTitle">
							<el-input v-model="ruleForm.jobTitle" size="middle" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb10">
						<el-form-item :label="$t('message.userFields.location')" clearable prop="location">
							<el-input v-model="ruleForm.location" size="middle" />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb10">
						<el-form-item class="el-form-item_msg">
							<ul class="message-list">
								<li v-for="message in messages" :key="message.msg"
									:class="`message-item ${message.type === 'error' ? 'error' : ''}`">
									<span class="dot"></span> {{ message.msg }}
								</li>
							</ul>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item style="float: right;">
							<el-button :loading="saveLoading" type="primary" @click="onSubmit">{{
								$t('message.profileButtons.SaveProfileChanges')
							}}</el-button>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
		</el-card>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, onMounted, defineComponent, watch, getCurrentInstance, ref } from 'vue';
import { ElMessageBox, ElMessage, ElTreeSelect } from 'element-plus';

import userApi from '/@/api/user';
import roleApi from '/@/api/role';
import dictItemApi from '/@/api/dictItem/index';
import { isNotEmptyOrNull } from '/@/utils';
import { IApiResultMessage } from '/@/models';

export default defineComponent({
	name: 'Profile',
	props: {
		info: Object,
	},
	setup(props, context) {
		const { proxy } = getCurrentInstance() as any;
		const treeRef = ref<InstanceType<typeof ElTreeSelect>>();
		const state = reactive({
			messages: [] as IApiResultMessage[], // 初始为空数组
			saveLoading: false,
			roleOptions: [] as any,
			remitToOptions: [],
			projectsOptions: [],
			timeZoneOptions: [] as any,
			selectedRemitToProject: [],
			remitToProjectData: [],
			ruleForm: {
				userName: '',
				firstName: '',
				lastName: '',
				mobile: '',
				jobTitle: '.NET Developer',
				email: '',
				location: '',
			} as any,
			rules: {
				firstName: [{ required: true, message: 'Please enter your first name.', trigger: 'blur' }],
				lastName: [{ required: true, message: 'Please enter your last name.', trigger: 'blur' }],
				mobile: [
					{ required: false, message: 'Please input', trigger: 'blur' },
					{
						validator: (rule: any, value: any, callback: any) => {
							if (/^(?:[0-9]\d*)$/.test(value) == false && isNotEmptyOrNull(value)) {
								callback(new Error('Phone number must be numeric and may not contain special characters or letters.'));
							} else {
								callback();
							}
						},
						trigger: 'blur',
					},
				],
			},
		});

		const changeRemitTo = (wwE_RemitTo_Id: any) => {

		};

		const addMessage = (msg: string, type: 'success' | 'error' | 'info' = 'error') => {
			state.messages.push({ msg, type });
		};

		const onSubmit = () => {
			state.messages = [];
			proxy.$refs.ruleFormRef.validate((valid: any, invalidFields: any) => {
				if (!valid) {
					Object.keys(invalidFields).forEach((field) => {
						console.log('invalidFields', invalidFields);
						invalidFields[field].forEach((error: any) => {
							let msg = error.message;
							const errorMessage = msg || `${field} is invalid`;
							addMessage(errorMessage, 'error');
						});
					});
					return;
				}

				var obj = {
					...Object.assign({}, state.ruleForm),
				};

				state.saveLoading = true;

				userApi
					.SaveProfileChanges(obj)
					.then((rs) => {
						ElMessage.success('Succeed');
						context.emit('fetchData', obj.id);
					})
					.catch((error: HandledError) => {
						if (!error.isHandled) {
							const errorCode = error.code;
							const errorMessage = error.message;
							ElMessage.error(errorMessage);
						}
					})
					.finally(() => {
						state.saveLoading = false;
					});
			});
		};

		const loadNode = () => {

		};

		const handleCheckChange = () => {
			const data = treeRef.value!.getCheckedNodes();
		};

		watch(
			() => props.info,
			(newVal, oldVal) => {
				state.ruleForm = newVal;
			}
		);
		watch(
			() => state.ruleForm,
			(newRuleForm, oldRuleForm) => {
				state.messages = [];
			},
			{ deep: true }
		);

		// 页面加载时
		onMounted(() => {
			if (props.info) {
				state.ruleForm = props.info;
			}

			roleApi.GetList().then((rs) => {
				state.roleOptions = rs.data;
			});

			var dictArr = ['Time Zone'];
			dictItemApi.Many(dictArr).then((rs: any) => {
				state.timeZoneOptions = rs.data?.find((a: any) => {
					return a.dictValue === 'Time Zone';
				})?.items;
			});
		});

		return {
			treeRef,
			handleCheckChange,
			onSubmit,
			loadNode,
			changeRemitTo,
			...toRefs(state),
		};
	},
});
</script>


<style scoped>
.lpx-content {
	max-width: 1280px !important;
	padding: 1.25em 2em 3em;
	margin: 0 auto;
}

.lpx-avatar-img-lg {
	height: 144px;
	width: 144px;
	border-radius: 144px;
}

.mb-0 {
	margin-bottom: 0 !important;
}

.mb-3 {
	margin-bottom: 1.5rem !important;
}

.mt-2 {
	margin-top: 0.75rem !important;
}

.pb-3 {
	padding-bottom: 1.5rem !important;
}

.card .card-body img {
	max-width: 100%;
}

.text-center {
	text-align: center !important;
}

.border-bottom {
	border-bottom: 1px solid #e8eef3;
}

.card .card-title {
	font-size: 1.25em;
	font-weight: 500;
	color: #062a44;
}

.text-muted {
	--bs-text-opacity: 1;
	color: #325168 !important;
	font-size: 12px;
}

.text-muted {
	opacity: 0.8;
}

.fw-normal {
	font-weight: 400 !important;
}

h5,
.h5 {
	font-size: 13px !important;
}

.menu_btn_wrapper {
	display: flex;
	flex-direction: column;
}

.menu_btn {
	width: 100% !important;
}

.button_container {
	margin-bottom: 10px;
}
</style>