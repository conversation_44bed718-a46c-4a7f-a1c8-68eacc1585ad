<template>
	<el-container class="system-tab-container" style="height: 100%">
		<el-header>
			<div class="left-panel">
				<el-radio-group v-model="dateType" style="margin-right: 15px" size="small" @change="onTimeChange">
					<el-radio-button label="0">今天</el-radio-button>
					<el-radio-button label="1">昨天</el-radio-button>
					<el-radio-button label="2">本周</el-radio-button>
					<el-radio-button label="3">上周</el-radio-button>
					<el-radio-button label="4">本月</el-radio-button>
				</el-radio-group>
				<el-date-picker v-model="logDate" type="datetimerange" range-separator="至" start-placeholder="开始日期"
					end-placeholder="结束日期" size="small"></el-date-picker>
			</div>
		</el-header>

		<el-main class="nopadding" ref="printMain">
			<div class="scTable" style="height: 100%" ref="scTableMain">
				<div class="scTable-table">
					<el-table :data="tableData.data" v-loading="tableData.loading" height="calc(100%)"
						@row-click="onDetail">
						<template #empty>
							<el-empty description="No Data" :image-size="100"></el-empty>
						</template>
						<el-table-column type="selection" />
						<el-table-column label="ID" width="100">
							<template #default="{ row }">
								<span>{{ row.id }}</span>
							</template>
						</el-table-column>
						<el-table-column label="级别" width="150">
							<template #default="{ row }">
								<span>{{ row.level }}</span>
							</template>
						</el-table-column>

						<el-table-column label="Message">
							<template #default="{ row }">
								<div class="log-msg">{{ row.message }}</div>
							</template>
						</el-table-column>

						<el-table-column label="TimeStamp" width="150">
							<template #default="{ row }">
								<span>{{ formatStrDate(row.timeStamp) }}</span>
							</template>
						</el-table-column>
					</el-table>
				</div>
				<div class="scTable-page">
					<el-pagination @size-change="onSizechange" @current-change="onCurrentChange" :pager-count="5"
						:page-sizes="[10, 20, 30]" v-model:current-page="tableData.param.pageIndex" background
						v-model:page-size="tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
						:total="tableData.total">
					</el-pagination>
				</div>
			</div>
			<el-drawer v-model="infoDrawer" title="信息详情" :size="600" destroy-on-close>
				<Detail ref="detailRef"></Detail>
			</el-drawer>
		</el-main>
	</el-container>
</template>

<script lang="ts">
import { toRefs, reactive, onMounted, defineComponent, ref } from 'vue';
import { formatStrDate, formatTypeTime } from '/@/utils/formatTime';
import Detail from './detail.vue';
import logEventApi from '/@/api/logEvent/index';

export default defineComponent({
	name: 'LogEventIndex',
	components: { Detail },
	setup() {
		const detailRef = ref();
		const state = reactive({
			tableData: {
				data: [],
				total: 0,
				loading: false,
				selection: [],
				param: {
					startTime: '',
					endTime: '',
					pageIndex: 1,
					pageSize: 20,
					searchKey: '',
					order: 'id',
					sort: 'desc', // asc or desc
				},
			},
			infoDrawer: false,
			detailObj: {},
			dateType: '0',
			logDate: [new Date(), new Date()], //Desc:更新时间
		});

		//初始化
		const onInit = () => {
			state.tableData.loading = true;
			logEventApi
				.Query(state.tableData.param)
				.then((rs) => {
					state.tableData.data = rs.data;
					state.tableData.total = rs.totalCount;
				})
				.catch((rs) => { })
				.finally(() => (state.tableData.loading = false));
		};
		//搜索
		const onSearch = () => {
			onInit();
		};

		// 添加
		const onTimeChange = (val: any) => {
			state.logDate = formatTypeTime(val);
			if (state.logDate) {
				state.tableData.param.startTime = state.logDate[0];
				state.tableData.param.endTime = state.logDate[1];
				onInit();
			}
		};
		//详细信息
		const onDetail = (row: any) => {
			state.infoDrawer = true;
			setTimeout((a) => {
				detailRef.value.onShow(row, 'System');
			}, 1000);
			//detailRef
		};
		// pageSize 改变时触发
		const onSizechange = (val: number) => {
			state.tableData.param.pageSize = val;
			onInit();
		};
		// current-change 改变时触发
		const onCurrentChange = (val: number) => {
			state.tableData.param.pageIndex = val;
			onInit();
		};
		// 页面加载时
		onMounted(() => {
			onInit();
		});
		return {
			onSizechange,
			onCurrentChange,
			detailRef,
			formatStrDate,
			onTimeChange,
			onInit,
			onDetail,
			onSearch,
			...toRefs(state),
		};
	},
});
</script>

<style>
.system-tab-container .log-msg {
	overflow: hidden;
	white-space: nowrap;
}
</style>
