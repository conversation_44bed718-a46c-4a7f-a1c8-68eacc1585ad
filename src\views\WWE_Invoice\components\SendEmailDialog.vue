<template>
	<el-dialog v-model="state.dialogVisible" :title="dialogTitle" width="80%" :before-close="handleClose" destroy-on-close>
		<div class="dialog-header">
			<div class="header-text">
				<p class="tip-text">You are about to send {{ emailTypeLabel }} emails</p>
				<p class="sub-text">This will send {{ emailTypeDescription }}</p>
			</div>
		</div>

		<el-form :inline="true" class="filter-form">
			<el-form-item label="Data Source:">
				<el-radio-group v-model="state.dataSource" @change="loadData">
					<el-radio-button label="selected">Selected Items</el-radio-button>
					<el-radio-button label="query">Current Query</el-radio-button>
				</el-radio-group>
			</el-form-item>
		</el-form>

		<div class="group-container">
			<div v-for="group in filteredGroups" :key="group.id" class="group-block">
				<div class="group-title">{{ group.carrier }} {{ group.account }}</div>
				<vxe-table border :data="group.items" class="data-table" show-overflow>
					<vxe-column field="proNumber" title="Pro#" width="120" />
					<vxe-column field="invoiceNumber" title="Invoice#" width="120" />
					<vxe-column field="pod" title="POD" width="100">
						<template #default="{ row }">
							<span class="missing" v-if="row.pod === 'Missing'">{{ row.pod }}</span>
							<span v-else>{{ row.pod || '--' }}</span>
						</template>
					</vxe-column>
					<vxe-column field="bol" title="BOL" width="100">
						<template #default="{ row }">
							<span class="missing" v-if="row.bol === 'Missing'">{{ row.bol }}</span>
							<span v-else>{{ row.bol || '--' }}</span>
						</template>
					</vxe-column>
					<vxe-column field="others" title="Others" width="100" />
					<vxe-column field="emailTimestamp" title="Email Timestamp" width="180" />
				</vxe-table>
			</div>
		</div>

		<template #footer>
			<el-button @click="handleClose">Cancel</el-button>
			<el-button type="primary" :loading="state.loading" @click="handleSend">Send</el-button>
		</template>
	</el-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, computed } from 'vue';
import type { PropType } from 'vue';
import type { VxeGridInstance } from 'vxe-table';
import { ElMessage } from 'element-plus';
import invoiceApi from '/@/api/WWE_Invoice';

interface ShipmentItem {
	proNumber: string;
	invoiceNumber: string;
	pod: string;
	bol: string;
	others: string;
	emailTimestamp?: string;
	status: string;
	sendCount: number;
	carrier: string;
	account: string;
}

interface CarrierGroup {
	id: string;
	carrier: string;
	account: string;
	items: ShipmentItem[];
}

export default defineComponent({
	name: 'SendEmailDialog',
	props: {
		emailType: {
			type: String as PropType<'initial' | 'followup' | 'elevate'>,
			required: true,
		},
		tableRef: {
			type: Object as PropType<VxeGridInstance>,
			required: true,
		},
		queryParams: {
			type: Object,
			required: true,
		},
		totalCount: {
			type: Number,
			required: true,
		},
	},
	emits: ['complete'],
	setup(props, { emit }) {
		const state = reactive({
			dialogVisible: false,
			loading: false,
			dataSource: 'selected',
			rawShipments: [] as ShipmentItem[],
		});

		const dialogTitle = computed(() => {
			switch (props.emailType) {
				case 'initial':
					return 'Send Initial Email';
				case 'followup':
					return 'Send Follow-Up Email';
				case 'elevate':
					return 'Elevate to Client Email';
				default:
					return 'Send Email';
			}
		});

		const emailTypeLabel = computed(() => dialogTitle.value.replace('Send ', ''));

		const emailTypeDescription = computed(() => {
			switch (props.emailType) {
				case 'initial':
					return 'all Exception status shipments that have never been emailed';
				case 'followup':
					return 'Exception status shipments already emailed once';
				case 'elevate':
					return 'EP status shipments emailed twice';
				default:
					return '';
			}
		});

		const openDialog = async () => {
			state.dialogVisible = true;
			state.dataSource = 'selected';
			await loadData();
		};

		const handleClose = () => {
			state.dialogVisible = false;
		};

		const loadData = () => {
			state.loading = true;
			if (state.dataSource === 'selected') {
				state.rawShipments = props.tableRef?.getCheckboxRecords() || [];
				state.loading = false;
			} else {
				invoiceApi
					.queryAllShipments({ ...props.queryParams })
					.then((response) => {
						if (response.resultCode !== 200) {
							ElMessage.warning(response.resultMsg || 'Failed to load shipments');
							return;
						}
						state.rawShipments = response.data || [];
					})
					.catch((error) => {
						ElMessage.error(error.message);
					})
					.finally(() => {
						state.loading = false;
					});
			}
		};

		const filteredGroups = computed(() => {
			const groups: Map<string, CarrierGroup> = new Map();
			const matched = state.rawShipments.filter((item) => {
				if (props.emailType === 'initial') {
					return item.status === 'Exception' && (!item.emailTimestamp || item.sendCount === 0);
				} else if (props.emailType === 'followup') {
					return item.status === 'Exception' && item.sendCount === 1;
				} else if (props.emailType === 'elevate') {
					return item.status === 'EP' && item.sendCount === 2;
				}
				return false;
			});

			matched.forEach((item) => {
				const key = `${item.carrier}_${item.account}`;
				if (!groups.has(key)) {
					groups.set(key, {
						id: key,
						carrier: item.carrier,
						account: item.account,
						items: [],
					});
				}
				groups.get(key)?.items.push(item);
			});

			return Array.from(groups.values());
		});

		const handleSend = () => {
			const allShipments = filteredGroups.value.flatMap((g) => g.items);
			if (!allShipments.length) {
				ElMessage.warning('No shipments to send');
				return;
			}

			state.loading = true;
			const tasks = filteredGroups.value.map((group) => {
				return invoiceApi.sendEmail(group.items, props.emailType);
			});

			Promise.all(tasks)
				.then(() => {
					ElMessage.success('Emails sent successfully');
					emit('complete');
					handleClose();
				})
				.catch((error) => {
					ElMessage.error(error.message || 'Failed to send emails');
					console.error(error);
				})
				.finally(() => {
					state.loading = false;
				});
		};

		return {
			...toRefs(state),
			dialogTitle,
			emailTypeLabel,
			emailTypeDescription,
			filteredGroups,
			openDialog,
			handleClose,
			handleSend,
			loadData,
		};
	},
});
</script>

<style scoped>
.dialog-header {
	margin-bottom: 10px;
}
.tip-text {
	font-size: 16px;
	font-weight: bold;
}
.sub-text {
	color: #888;
	margin-top: 5px;
}
.group-container {
	margin-top: 15px;
}
.group-block {
	margin-bottom: 20px;
}
.group-title {
	font-weight: bold;
	font-size: 16px;
	margin-bottom: 10px;
}
.missing {
	color: red;
	font-weight: bold;
}
</style>
