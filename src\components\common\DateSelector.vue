<template>
	<el-date-picker ref="myDate" v-model="propValue" :type="dateType" :placeholder="placeholder"
		:format="dynamic_format" :value-format="dynamic_format" @keydown="keyup" @focus="focus" @blur="inputValue"
		@change="select" :clearable="clearable" @clear="select" :editable="editable" size="default">
	</el-date-picker>
</template>

<script lang="ts" setup>
import { watch, ref, getCurrentInstance } from 'vue';

import { ElMessage } from 'element-plus'

const props = defineProps(['input', 'placeholder', 'dateType', 'editable', 'clearable']);

const emit = defineEmits(['update:input', 'customChange', 'customClear']);

const { proxy } = <any>getCurrentInstance();

const propValue = ref(props.input);

const placeholder = ref('Please input');

import { formatDate, getBeginMonth, getEndMonthV2 } from '/@/utils/formatTime';

if (props.placeholder) {
	placeholder.value = props.placeholder;
}

const currentVal = ref('');

const dynamic_format = ref(props.dateType == "month" ? 'MMM YYYY' : 'MM/DD/YYYY');
// const dynamic_format = ref(props.dateType == "month" ? 'MM/YYYY' : 'MM/DD/YYYY');

const select = () => {
	if (propValue.value != null) {
		emit('customChange');
		currentVal.value = propValue.value.replace('/', '').replace('/', '');
	} else {
		currentVal.value = '';
	}
};

const keyup = (v: any) => {
	if (currentVal.value == '' || (propValue.value != null && propValue.value.length > 0)) {
		propValue.value = '';
		currentVal.value = '';
	}
	if (v.key == 'Backspace') {
		if (currentVal.value.length <= 1) {
			currentVal.value = '';
		} else {
			currentVal.value = currentVal.value.substring(0, currentVal.value.length - 1);
		}
	} else if ('0123456789'.includes(v.key)) {
		currentVal.value = currentVal.value + v.key;
	}
};

const inputValue = () => {
	if (currentVal.value != null && currentVal.value.indexOf('/') == -1) {
		if (currentVal.value.length == 6) {
			var tempDate = currentVal.value.substring(0, 2) + '/' + currentVal.value.substring(2, 4) + '/' + '20' + currentVal.value.substring(4);

			const dateValue = new Date(tempDate)

			var formatDateValue = formatDate(dateValue, 'mm/dd/YYYY')

			if (isNaN(dateValue.getTime()) || (tempDate != formatDateValue)) {
				ElMessage.error("Please enter a valid date.");
			} else {
				propValue.value = tempDate
			}
		} else if (currentVal.value.length == 8) {
			propValue.value = currentVal.value.substring(0, 2) + '/' + currentVal.value.substring(2, 4) + '/' + currentVal.value.substring(4);
		} else {
			if (propValue.value == '') {
				propValue.value = '';
			}
		}
	} else {
		dynamic_format.value = 'MM/DD/YYYY';
		propValue.value = currentVal.value;
	}

	emit('update:input', propValue.value);
	currentVal.value = '';
};

watch(() => props.input, (v) => {
	if (v == '' || v == null) {
		propValue.value = '';
		currentVal.value = '';
	} else {
		currentVal.value = v;
		inputValue();
	}
})

const focus = () => {

};

</script>
