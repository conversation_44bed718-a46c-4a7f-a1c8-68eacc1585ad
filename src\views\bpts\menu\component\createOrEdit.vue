﻿<template>
	<div class="system-edit-menu-container">
		<el-drawer :title="title" v-model="isShowDialog" destroy-on-close draggable :close-on-click-modal="false"
			:close-on-press-escape="false" :size="600">
			<template #header>
				<h3>{{ title }}</h3>
			</template>
			<div class="drawer-content">
				<el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="130px">
					<el-row :gutter="24">
						<el-col :span="24" class="mb6">
							<el-form-item :label="$t('message.menuEditFields.parentMenu')">
								<el-cascader ref="parantRef" :options="menuData"
									:props="{ checkStrictly: true, value: 'id', label: 'title' }"
									:placeholder="$t('message.page.selectKeyPlaceholder')" clearable class="w100"
									v-model="parentData" size="middle">
									<template #default="{ node, data }">
										<span>{{ data.title }}</span>
										<span v-if="!node.isLeaf"> ({{ data.children.length }}) </span>
									</template>
								</el-cascader>
							</el-form-item>
						</el-col>
						<el-col :span="24" class="mb6">
							<el-form-item :label="$t('message.menuFields.name')" prop="title">
								<el-input v-model="ruleForm.title" size="middle" />
							</el-form-item>
						</el-col>
						<el-col :span="24" class="mb6">
							<el-form-item :label="$t('message.menuEditFields.routerName')" prop="name">
								<el-input v-model="ruleForm.name" @keyup="onNameChange" size="middle" />
							</el-form-item>
						</el-col>
						<el-col :span="24" class="mb6">
							<el-form-item :label="$t('message.menuFields.path')" prop="path">
								<el-input v-model="ruleForm.path" size="middle" />
							</el-form-item>
						</el-col>
						<el-col :span="24" class="mb6">
							<el-form-item :label="$t('message.menuFields.redirect')">
								<el-input v-model="ruleForm.redirect" size="middle" />
							</el-form-item>
						</el-col>
						<el-col :span="24" class="mb6">
							<el-form-item :label="$t('message.menuFields.icon')">
								<IconSelector :placeholder="$t('message.page.selectKeyPlaceholder')"
									v-model="ruleForm.icon" type="all" size="middle" />
							</el-form-item>
						</el-col>
						<el-col :span="24" class="mb6">
							<el-form-item :label="$t('message.menuFields.component')" prop="component">
								<el-input v-model="ruleForm.component" size="middle" />
							</el-form-item>
						</el-col>

						<el-col :span="24" class="mb6">
							<el-form-item :label="$t('message.menuFields.identification')">
								<el-select v-model="ruleForm.roles" multiple
									:placeholder="$t('message.page.selectKeyPlaceholder')" clearable class="w100"
									size="middle">
									<el-option label="admin" value="admin"></el-option>
									<el-option label="common" value="common"></el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="24" class="mb6">
							<el-form-item :label="$t('message.menuFields.sort')">
								<el-input-number v-model="ruleForm.sort" controls-position="right" class="w100"
									size="middle" />
							</el-form-item>
						</el-col>
						<el-col :span="24" class="mb6">
							<el-form-item :label="$t('message.menuFields.isLink')">
								<el-radio-group v-model="ruleForm.isOutlink">
									<el-radio :label="true">{{ $t('message.page.yes') }}</el-radio>
									<el-radio :label="false">{{ $t('message.page.no') }}</el-radio>
								</el-radio-group>
							</el-form-item>
						</el-col>
						<el-col :span="24" class="mb6">
							<el-form-item :label="$t('message.menuFields.linkUrl')">
								<el-input v-model="ruleForm.isLink" clearable :disabled="!ruleForm.isOutlink"
									size="middle">
								</el-input>
							</el-form-item>
						</el-col>
						<el-col :span="24" class="mb6">
							<el-form-item :label="$t('message.menuFields.isHide')">
								<el-radio-group v-model="ruleForm.isHide">
									<el-radio :label="true">{{ $t('message.page.hidden') }}</el-radio>
									<el-radio :label="false">{{ $t('message.page.nonhidden') }}</el-radio>
								</el-radio-group>
							</el-form-item>
						</el-col>
						<el-col :span="24" class="mb6">
							<el-form-item :label="$t('message.menuFields.pageCache')">
								<el-radio-group v-model="ruleForm.isKeepAlive">
									<el-radio :label="true">{{ $t('message.page.cache') }}</el-radio>
									<el-radio :label="false">{{ $t('message.page.noCache') }}</el-radio>
								</el-radio-group>
							</el-form-item>
						</el-col>
						<el-col :span="24" class="mb6">
							<el-form-item :label="$t('message.menuFields.isAffix')">
								<el-radio-group v-model="ruleForm.isAffix">
									<el-radio :label="true">{{ $t('message.page.affix') }}</el-radio>
									<el-radio :label="false">{{ $t('message.page.noAffix') }}</el-radio>
								</el-radio-group>
							</el-form-item>
						</el-col>
						<el-col :span="24" class="mb6">
							<el-form-item :label="$t('message.menuFields.isIframe')">
								<el-radio-group v-model="ruleForm.isIframe">
									<el-radio :label="true">{{ $t('message.page.iframe') }}</el-radio>
									<el-radio :label="false">{{ $t('message.page.noIframe') }}</el-radio>
								</el-radio-group>
							</el-form-item>
						</el-col>
						<el-col :span="24" class="mb6">
							<el-form-item :label="$t('message.menuFields.bindApi')">
								<el-select v-model="apiSelectData" multiple ref="apiSelectRef"
									:placeholder="$t('message.page.selectKeyPlaceholder')" style="width: 100%"
									@visible-change="onVisible" size="middle">
									<el-option v-for="item in apiData" :key="item.id" :label="item.name"
										:value="item.id" />
								</el-select>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>
				<div class="drawer-footer">
					<span class="dialog-footer">
						<el-button @click="onCancel" size="small">{{ $t('message.page.buttonCancel') }}</el-button>
						<el-button v-if="ruleForm.action === 'Create'" @click="onInitForm" size="small" type="danger">
							{{ $t('message.page.buttonReset') }}</el-button>
						<el-button v-if="ruleForm.action === 'Edit'" @click="onDelete" type="danger" size="small">{{
							$t('message.page.buttonDelete') }} </el-button>
						<el-button :loading="loading" type="primary" @click="onSubmit" size="small">{{
							$t('message.page.buttonSave') }}</el-button>
					</span>
				</div>
			</div>
		</el-drawer>
		<ApiTree ref="apiTreeRef" @fetchData="setSelectItem" />
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, getCurrentInstance, ref } from 'vue';
import { getElcascaderSingle } from '/@/utils';
import { ElMessageBox, ElMessage } from 'element-plus';
import IconSelector from '/@/components/iconSelector/index.vue';
import { i18n } from '/@/i18n/index';
import ApiTree from '/@/views/bpts/api/components/tree.vue';
import { useI18n } from 'vue-i18n';

import menuApi from '/@/api/menu/list';

interface DialogParams {
	action: string;
	id: number;
}

export default {
	name: 'menuCreateOrEdit',
	components: { IconSelector, ApiTree },
	setup(props, context) {
		const { t } = useI18n();
		const { proxy } = getCurrentInstance() as any;
		const apiTreeRef = ref();
		const apiSelectRef = ref();
		const parantRef = ref();

		const state = reactive({
			title: 'Create Menu',
			isShowDialog: false,
			saveLoading: false,
			deleteLoading: false,
			menuData: [], //上级菜单
			parentData: [], //已选择
			apiData: [], //api 数据
			apiSelectData: [], //已选择的api项
			DialogParams: {
				action: '',
				id: -1,
			},
			ruleForm: {
				id: 0, //
				parentId: 0, //
				path: '', //
				name: '', //
				component: 'layout/routerView/parent', //
				redirect: '', //
				sort: 0, //
				menuType: 0, //
				title: '', //
				link: '',
				isLink: '', //
				isHide: false, //
				isKeepAlive: false, //
				isAffix: false, //
				isIframe: false, //
				icon: '', //
				identification: '',
				defaultBtn: true,
			},
			rules: {
				id: [{ required: true, message: 'Please input', trigger: 'blur' }],
				parentId: [{ required: true, message: 'Please input', trigger: 'blur' }],
				path: [{ required: true, message: 'Please input', trigger: 'blur' }],
				name: [{ required: true, message: 'Please input', trigger: 'blur' }],
				component: [{ required: true, message: 'Please input', trigger: 'blur' }],
				redirect: [{ required: true, message: 'Please input', trigger: 'blur' }],
				sort: [{ required: true, message: 'Please input', trigger: 'blur' }],
				menuType: [{ required: true, message: 'Please input', trigger: 'blur' }],
				title: [{ required: true, message: 'Please input', trigger: 'blur' }],
				isLink: [{ required: true, message: 'Please input', trigger: 'blur' }],
				isHide: [{ required: true, message: 'Please input', trigger: 'blur' }],
				isKeepAlive: [{ required: true, message: 'Please input', trigger: 'blur' }],
				isAffix: [{ required: true, message: 'Please input', trigger: 'blur' }],
				isIframe: [{ required: true, message: 'Please input', trigger: 'blur' }],
				icon: [{ required: true, message: 'Please input', trigger: 'blur' }],
				createAt: [{ type: 'date', required: true, message: 'Pick date', trigger: 'change' }],

				//type1: [{ type: 'array', required: true, message: '', trigger: 'change' }],
			},
		});
		// 打开弹窗
		const openDialog = (params: DialogParams) => {
			onInitForm();
			state.DialogParams = params;
			if (params.action == 'Create') {
				state.title = 'Create Menu';
			} else if (params.action == 'Edit') {
				state.title = 'Edit Menu';

				//获取详细信息
				getData(params.id);

				//获取菜单绑定的Api
				menuApi.GetMenuApi(params.id).then((rs) => {
					//这里需要手动 造一下数据，目前用不到，所以注释先。
					if (rs.data && rs.data.length > 0) {
						rs.data.forEach((item: any) => {
							state.apiData.push({
								id: item.apiId,
								name: item.apiName,
							});
							state.apiSelectData.push(item.apiId);
						});
					}
				});
			} else {
				ElMessage.error('Parameter action cannot be empty.');
			}

			//读取上级菜单
			menuApi.Tree().then((rs) => {
				state.menuData = getMenuData(rs.data);
			});
		};

		// 获取菜单
		const getMenuData = (routes: any) => {
			const arr: any = [];
			routes.map((val: any) => {
				val['title'] = i18n.global.t(val.title);
				arr.push({ ...val });
				if (val.children) getMenuData(val.children);
			});
			return arr;
		};

		// 关闭弹窗
		const closeDialog = () => {
			onInitForm();
			state.isShowDialog = false;
		};
		// 取消
		const onCancel = () => {
			closeDialog();
		};
		// 保存
		const onSubmit = () => {
			proxy.$refs.ruleFormRef.validate((valid: any) => {
				if (!valid) {
					return;
				}
				var obj = state.ruleForm;
				obj.parentId = getElcascaderSingle(state.parentData, 0);
				obj.apis = state.apiSelectData;

				//如果创建菜单，是顶级菜单，一般不用权限按钮
				if (state.DialogParams.action == 'Create' && obj.parentId == 0) {
					state.ruleForm.defaultBtn = false;
				}

				state.saveLoading = true;
				menuApi
					.Save(obj)
					.then(() => {
						ElMessage.success('Succeed');
						context.emit('fetchData');
						closeDialog();
					})
					.catch((error: HandledError) => {
						if (!error.isHandled) {
							const errorCode = error.code;
							const errorMessage = error.message;
							ElMessage.error(errorMessage);
						}
					})
					.finally(() => {
						state.saveLoading = false;
					});
			});
		};
		//根据id获取完整的信息
		const getData = async (id: any) => {
			await menuApi.Detail(id).then((rs) => {
				state.ruleForm = Object.assign({}, rs.data);
				if (rs.data) {
					state.parentData.push(rs.data.parentId);
				}
			});
		};
		//删除纪录
		const onDelete = () => {
			ElMessageBox.confirm(t('message.page.dlgDeleteText'), t('message.page.dlgTip'), {
				confirmButtonText: t('message.page.confirm'),
				cancelButtonText: t('message.page.cancel'),
				type: 'warning',
			}).then(() => {
				state.deleteLoading = true;
				menuApi
					.DeleteByKey(state.ruleForm.id)
					.then(() => {
						ElMessage.success('Succeed');
						context.emit('fetchData');
						closeDialog();
					})
					.catch((error: HandledError) => {
						if (!error.isHandled) {
							const errorCode = error.code;
							const errorMessage = error.message;
							ElMessage.error(errorMessage);
						}
					})
					.finally(() => {
						state.deleteLoading = false;
					});
			});
		};
		// 重置表单
		const onInitForm = () => {
			state.isShowDialog = true;
			if (proxy.$refs.ruleFormRef) {
				proxy.$refs.ruleFormRef.resetFields();
			}
			state.ruleForm = {
				id: 0, //
				parentId: 0, //
				path: '', //
				name: '', //
				component: 'layout/routerView/parent', //
				redirect: '', //
				sort: 0, //
				menuType: 0, //
				title: '', //
				link: '',
				isLink: '', //
				isHide: false, //
				isKeepAlive: false, //
				isAffix: false, //
				isIframe: false, //
				icon: '', //
				identification: '', //
				defaultBtn: true,
			};

			state.apiData = [];
			state.apiSelectData = [];
			state.parentData = [];
		};

		const onNameChange = (e: any) => {
			const cmp = state.ruleForm.name;
			//获取上级
			var parentObj = parantRef.value.getCheckedNodes();
			if (parentObj && state.DialogParams.action == 'Create') {
				state.ruleForm.component = parentObj[0].data.name + '/' + state.ruleForm.name + '.vue';
			}
		};

		const onVisible = () => {
			const data=state.apiData.map((a) => {
					return a.id;
				});
			apiSelectRef.value.blur(); //使选择器的输入框失去焦点，并隐藏下拉框
			apiTreeRef.value.openDialog(data);
		};
		const setSelectItem = (items: any) => {
			if (items) {
				const newItem = [...items]; //复制数组
				state.apiData = items;
				state.apiSelectData = newItem.map((a) => {
					return a.id;
				});
			} else {
				state.isShowDialog = false;
			}
		};
		return {
			openDialog,
			onVisible,
			setSelectItem,
			apiTreeRef,
			apiSelectRef,
			parantRef,
			closeDialog,
			onNameChange,
			onCancel,
			onSubmit,
			onDelete,
			onInitForm,
			...toRefs(state),
		};
	},
};
</script>


<style scoped>
.drawer-content {
	padding: 20px;
}

.drawer-footer {
	text-align: right;
	margin-top: 10px
}
</style>