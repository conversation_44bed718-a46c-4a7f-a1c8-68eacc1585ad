// API 错误代码
export const API_ERROR_CODES = {
  /** 请求成功，业务处理成功 */
  SUCCESS: 200,
  
  /** 请求参数错误，客户端请求有误 */
  INVALID_PARAMS: 400,
  
  /** 未授权，用户需要登录 */
  UNAUTHORIZED_401: 401,
  
  /** 无权限，用户没有访问权限 */
  FORBIDDEN_403: 403,
  
  /** 请求资源不存在 */
  NOT_FOUND: 404,
  
  /** 服务器内部错误 */
  SERVER_ERROR: 500,

  LOGIN_ERROR: 501,
  
  /** 无效的 session，需要重新登录 */
  INVALID_SESSION_4005: 4005,
  
  /** 无访问权限，操作被拒绝 */
  ACCESS_DENIED: 4030,
  
  /** 数据冲突 */
  DATA_CONFLICT: 4090,
  
  /** 服务器内部自定义错误 */
  CUSTOM_SERVER_ERROR: 5000,
};

// HTTP 错误代码
export const HTTP_ERROR_CODES = {
  /** 请求无效 */
  BAD_REQUEST: 400,
  
  /** 未授权 */
  UNAUTHORIZED: 401,
  
  /** 禁止访问 */
  FORBIDDEN: 403,
  
  /** 资源未找到 */
  NOT_FOUND: 404,
  
  /** 服务器内部错误 */
  INTERNAL_SERVER_ERROR: 500,
  
  /** 网关错误 */
  BAD_GATEWAY: 502,
  
  /** 服务不可用 */
  SERVICE_UNAVAILABLE: 503,
  
  /** 网关超时 */
  GATEWAY_TIMEOUT: 504,
};

export default { API_ERROR_CODES, HTTP_ERROR_CODES };
