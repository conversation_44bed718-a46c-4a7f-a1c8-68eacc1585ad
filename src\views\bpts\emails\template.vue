﻿<template>
	<div class="top-container">
		<div class="list-search">
			<el-card class="search-form-card" shadow="never">
				<el-form label-width="120px" @keyup.enter="onSearch" @submit.prevent>
					<el-row :gutter="24">
						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="5" class="mb1 mt1">
							<el-form-item label="Template Name">
								<el-input v-model="tableData.param.searchKey" clearable @keyup.enter="onSearch"
									size="middle" :placeholder="$t('message.page.searchKeyPlaceholder')"
									class="w-20"></el-input>
							</el-form-item>
						</el-col>
						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="5" class="mb1 mt1">
							<el-form-item class="nowidth" label=" ">
								<div class="searchBtn">
									<el-button type="primary" @click="onSearch" size="small" icon="search">
										{{ $t('message.page.buttonSearch') }}
									</el-button>
								</div>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>
			</el-card>
		</div>

		<div class="list-table">
			<el-card shadow="never">
				<el-header class="table_header">
					<div class="left-panel">
						<el-button v-auth="'emailTemplate.Create'" type="primary" @click="onAdd" size="small">
							<el-icon>
								<ele-Plus />
							</el-icon>
							{{ $t('message.page.buttonCreate') }}
						</el-button>
						<el-button v-auth="'emailTemplate.BitchDelete'" type="danger" size="small" icon="ele-Delete"
							:disabled="tableData.selection.length == 0" @click="onDeleteByList">
							{{ $t('message.page.buttonDelete') }}
						</el-button>
					</div>
					<div class="right-panel"> </div>
				</el-header>

				<div class="scTable">
					<div class="scTable-table">
						<el-table ref="tableRef" :data="tableData.data" v-loading="tableData.loading"
							height="calc(100%)" @selection-change="selectionChange" :row-style="{ height: '33px' }"
							:cell-style="{ padding: '0px' }" highlight-current-row @row-click="rowClick" border>
							<template #empty>
								<el-empty :description="$t('message.page.emptyDescription')"
									:image-size="100"></el-empty>
							</template>
							<el-table-column type="selection" />

							<el-table-column :label="$t('message.emailFields.templateName')" show-overflow-tooltip>
								<template #default="{ row }">
									<el-link @click="onEdit(row)" type="primary">
										{{ row.name }}
									</el-link>
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.emailFields.templateDescription')"
								show-overflow-tooltip>
								<template #default="{ row }">
									<span>{{ row.description }}</span>
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.emailFields.templateStatus')" show-overflow-tooltip>
								<template #default="{ row }">
									<el-icon v-if="row.status == 0" style="color: #67c23a">
										<ele-SuccessFilled />
									</el-icon>
									<el-icon v-if="row.status == 1" style="color: red">
										<ele-SuccessFilled />
									</el-icon>
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.page.createBy')" show-overflow-tooltip>
								<template #default="{ row }">
									<span>{{ row.createdByName }}</span>
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.page.createAt')" width="200">
								<template #default="{ row }">
									<span>{{ row.createdAt }}</span>
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.page.modifiedAt')" width="200">
								<template #default="{ row }">
									<span>{{ row.modifiedAt }}</span>
								</template>
							</el-table-column>

							<el-table-column fixed="right" align="left" :label="$t('message.page.Action')" width="90">
								<template #default="{ row }">

									<el-tooltip v-if="false" class="box-item" effect="dark" content="View"
										placement="bottom">
										<el-button size="mini" type="text" @click="onDetail(row)" icon="View">
										</el-button>
									</el-tooltip>

									<!-- <el-tooltip class="box-item" effect="dark" content="Edit" placement="bottom">
										<el-button v-auth="'emailTemplate.Edit'" size="mini" icon="Edit" type="text"
											@click="onEdit(row)">
										</el-button>
									</el-tooltip>

									<el-tooltip class="box-item" effect="dark" content="Delete" placement="bottom">
										<el-button v-auth="'emailTemplate.Delete'" size="mini" icon="Delete" type="text"
											style="color: #ff3a3a" @click="onDelete(row)">
										</el-button>
									</el-tooltip> -->

									<el-tooltip class="box-item" effect="dark" :content="$t('message.limits.Edit')"
										placement="bottom">
										<vxe-button mode="text" status="primary" icon="vxe-icon-edit"
											v-auth="'emailTemplate.Edit'" @click="onEdit(row)"></vxe-button>
									</el-tooltip>

									<el-tooltip class="box-item" effect="dark" :content="$t('message.limits.Delete')"
										placement="bottom">
										<vxe-button mode="text" status="error" icon="vxe-icon-delete"
											v-auth="'emailTemplate.Delete'" @click="onDelete(row)"></vxe-button>
									</el-tooltip>

								</template>
							</el-table-column>
						</el-table>
					</div>

					<div class="scTable-page">
						<el-pagination @size-change="onSizechange" @current-change="onCurrentChange" :pager-count="5"
							:page-sizes="[20, 30, 50, 100]" v-model:current-page="tableData.param.pageIndex" background
							v-model:page-size="tableData.param.pageSize"
							layout="total, sizes, prev, pager, next, jumper" :total="tableData.total"
							small></el-pagination>
					</div>
				</div>
			</el-card>
		</div>

		<CreateOrEdit ref="createOrEditRef" @fetchData="onInit"></CreateOrEdit>
		<el-drawer v-model="infoDrawer" :title="$t('message.page.detailTitle')" :size="600" destroy-on-close>
			<Detail ref="detailRef" :info="detailObj"></Detail>
		</el-drawer>
	</div>
</template>

<script lang="ts">
//import { useRouter } from 'vue-router';
import { toRefs, reactive, onMounted, ref, defineComponent } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { formatStrDate } from '/@/utils/formatTime';
import print from '/@/utils/print.js';

import emailTemplateApi from '/@/api/emailTemplate/index';
import Detail from './components/detail.vue';
import CreateOrEdit from './components/createOrEditTemp.vue';
import { useI18n } from 'vue-i18n';
import type { TableInstance } from 'element-plus';

export default defineComponent({
	name: 'emailTemplate',
	components: { Detail, CreateOrEdit },
	setup() {
		const { t } = useI18n();
		//const router = useRouter();
		const createOrEditRef = ref();
		const printMain = ref(null);
		const state = reactive({
			tableData: {
				data: [],
				total: 0,
				loading: false,
				selection: [],
				param: {
					pageIndex: 1,
					pageSize: 20,
					searchKey: '',
					order: 'templateId',
					sort: 'desc', // asc or desc
				},
			},
			infoDrawer: false,
			detailObj: {},
		});

		//初始化
		const onInit = () => {
			state.tableData.loading = true;
			emailTemplateApi
				.Query(state.tableData.param)
				.then((rs) => {
					state.tableData.data = rs.data;
					state.tableData.total = rs.totalCount;
				})
				.catch((rs) => { })
				.finally(() => (state.tableData.loading = false));
		};
		//搜索
		const onSearch = () => {
			onInit();
		};

		// 添加
		const onAdd = () => {
			createOrEditRef.value.openDialog({ action: 'Create' });
			//router.push('/emailTemplate/Create');
		};
		// 修改
		const onEdit = (row: Object) => {
			createOrEditRef.value.openDialog({ action: 'Edit', templateId: row.templateId });
			//router.push('/emailTemplate/edit/' + row.templateId);
		};
		// 删除
		const onDelete = (row: Object) => {
			ElMessageBox.confirm(t('message.page.dlgDeleteText'), t('message.page.dlgTip'), {
				confirmButtonText: t('message.page.confirm'),
				cancelButtonText: t('message.page.cancel'),
				type: 'warning',
			}).then(() => {
				emailTemplateApi
					.DeleteByKey(row.templateId)
					.then((rs) => {
						if (!rs.data) {
							ElMessage.error(rs.resultMsg);
							return;
						}
						onInit();
					})
					.catch((rs) => { });
			});
		};
		//批量删除
		const onDeleteByList = () => {
			ElMessageBox.confirm(
				t('message.page.dlgDeleteSelectText1') + ` ${state.tableData.selection.length} ` + t('message.page.dlgDeleteSelectText2'),
				t('message.page.dlgTip'),
				{
					type: 'warning',
				}
			).then(() => {
				emailTemplateApi.DeleteMany(state.tableData.selection).then((rs) => {
					if (rs.data == 0) {
						ElMessage.error(rs.resultMsg);
						return;
					}
					onInit();
				});
			});
		};
		//详细信息
		const onDetail = (row: any) => {
			state.detailObj = {};

			emailTemplateApi.Detail(row.templateId).then((rs) => {
				state.detailObj = rs.data;
				state.infoDrawer = true;
			});
		};
		//表格选择后回调事件
		const selectionChange = (selection: any) => {
			state.tableData.selection = selection.map((a) => {
				return a.templateId;
			});
		};
		//打印
		const onPrint = () => {
			print(printMain.value);
		};
		// pageSize 改变时触发
		const onSizechange = (val: number) => {
			state.tableData.param.pageSize = val;
			onInit();
		};
		// current-change 改变时触发
		const onCurrentChange = (val: number) => {
			state.tableData.param.pageIndex = val;
			onInit();
		};
		// 页面加载时
		onMounted(() => {
			onInit();
		});

		const tableRef = ref<TableInstance>()
		const selection = ref<[]>([])

		const rowClick = (row: { id: any; }, column: any, event: any) => {
			tableRef.value!.clearSelection()

			tableRef.value!.toggleRowSelection(
				row,
				true
			)
		}

		return {
			printMain,
			formatStrDate,
			onPrint,
			createOrEditRef,
			selectionChange,
			onInit,
			onAdd,
			onEdit,
			onDetail,
			onDelete,
			onDeleteByList,
			onSizechange,
			onCurrentChange,
			onSearch,
			...toRefs(state),
			tableRef,
			rowClick
		};
	},
});
</script>
