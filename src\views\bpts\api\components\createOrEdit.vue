﻿<template>
	<div class="api-edit-container">
		<el-dialog :title="title" v-model="isShowDialog" width="769px">
			<el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="90px" label-position="top">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="IsDeleted" prop="IsDeleted">
							<el-radio-group v-model="ruleForm.IsDeleted">
								<el-radio :label="true">Enable</el-radio>
								<el-radio :label="false">Disable</el-radio>
							</el-radio-group>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="name" prop="name">
							<el-input v-model="ruleForm.name" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="linkUrl" prop="linkUrl">
							<el-input v-model="ruleForm.linkUrl" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="area" prop="area">
							<el-input v-model="ruleForm.area" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="controller" prop="controller">
							<el-input v-model="ruleForm.controller" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="action" prop="action">
							<el-input v-model="ruleForm.action" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="icon" prop="icon">
							<el-input v-model="ruleForm.icon" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="code" prop="code">
							<el-input v-model="ruleForm.code" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="orderSort" prop="orderSort">
							<el-input-number v-model="ruleForm.orderSort" :min="0" class="w100" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="description" prop="description">
							<el-input v-model="ruleForm.Msg" type="textarea" placeholder="请输入备注" maxlength="150"> </el-input>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="IsMenu" prop="IsMenu">
							<el-radio-group v-model="ruleForm.IsMenu">
								<el-radio :label="true">Enable</el-radio>
								<el-radio :label="false">Disable</el-radio>
							</el-radio-group>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="Enabled" prop="Enabled">
							<el-radio-group v-model="ruleForm.Enabled">
								<el-radio :label="true">Enable</el-radio>
								<el-radio :label="false">Disable</el-radio>
							</el-radio-group>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="createId" prop="createId">
							<el-input-number v-model="ruleForm.createId" :min="0" class="w100" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="createBy" prop="createBy">
							<el-input v-model="ruleForm.createBy" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="createTime" prop="createTime">
							<el-date-picker v-model="ruleForm.createTime" type="date" placeholder="Pick a day" class="w100"> </el-date-picker>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="modifyId" prop="modifyId">
							<el-input-number v-model="ruleForm.modifyId" :min="0" class="w100" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="modifyBy" prop="modifyBy">
							<el-input v-model="ruleForm.modifyBy" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="modifyTime" prop="modifyTime">
							<el-date-picker v-model="ruleForm.modifyTime" type="date" placeholder="Pick a day" class="w100"> </el-date-picker>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="parentId" prop="parentId">
							<el-input-number v-model="ruleForm.parentId" :min="0" class="w100" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="select">
							<el-cascader
								:options="deptData"
								:props="{ checkStrictly: true, value: 'deptName', label: 'deptName' }"
								:placeholder="$t('message.page.selectKeyPlaceholder')"
								clearable
								class="w100"
								v-model="ruleForm.department">
								<template #default="{ node, data }">
									<span>{{ data.deptName }}</span>
									<span v-if="!node.isLeaf"> ({{ data.children.length }}) </span>
								</template>
							</el-cascader>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel" size="small">Cancel</el-button>
					<el-button @click="onInitForm" size="small" type="danger">Reset</el-button>
					<el-button v-if="ruleForm.action === 'Edit'" @click="onDelete" type="danger" size="small">Delete </el-button>
					<el-button :loading="loading" type="primary" @click="onSubmit" size="small">Save</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, getCurrentInstance } from 'vue';
import { isNotEmptyOrNull, parseThanZero } from '/@/utils';
import { ElMessageBox, ElMessage } from 'element-plus';

import apiApi from '/@/api/api';

interface DialogParams {
	action: string;
	id: number;
}

export default {
	name: 'apiCreateOrEdit',
	components: {},
	setup(props, context) {
		const { proxy } = getCurrentInstance() as any;
		const state = reactive({
			title: 'Create Api',
			isShowDialog: false,
			saveLoading: false,
			deleteLoading: false,
			dialogParams: {
				action: '',
				id: -1,
			},
			ruleForm: {
				id: 0, //
				isDeleted: false, //
				name: '', //
				linkUrl: '', //
				area: '', //
				controller: '', //
				action: '', //
				icon: '', //
				code: '', //
				orderSort: 0, //
				description: '', //
				isMenu: false, //
				enabled: false, //
				createId: 0, //
				createBy: '', //
				createTime: new Date(), //
				modifyId: 0, //
				modifyBy: '', //
				modifyTime: new Date(), //
				parentId: 0, //
			},
			rules: {
				id: [{ required: true, message: 'Please input', trigger: 'blur' }],
				linkUrl: [{ required: true, message: 'Please input', trigger: 'blur' }],
				orderSort: [{ required: true, message: 'Please input', trigger: 'blur' }],
				isMenu: [{ required: true, message: 'Please input', trigger: 'blur' }],
				enabled: [{ required: true, message: 'Please input', trigger: 'blur' }],

				//type1: [{ type: 'array', required: true, message: '', trigger: 'change' }],
			},
		});
		// 打开弹窗
		const openDialog = (parmas: DialogParams) => {
			state.dialogParams = parmas;

			onInitForm();

			if (parmas.action == 'Create') {
				state.title = 'Create Menu';
			} else if (parmas.action == 'Edit') {
				state.title = 'Edit Api';
				getData(parmas.id);
			} else {
				ElMessage.error('Parameter action cannot be empty.');
			}
		};
		// 关闭弹窗
		const closeDialog = () => {
			onInitForm();
			state.isShowDialog = false;
		};
		// 取消
		const onCancel = () => {
			closeDialog();
		};
		// 保存
		const onSubmit = () => {
			proxy.$refs.ruleFormRef.validate((valid: any) => {
				if (!valid) {
					return;
				}
				var obj = state.ruleForm;
				state.saveLoading = true;
				apiApi
					.Save(obj)
					.then(() => {
						ElMessage.success('Succeed');
						context.emit('fetchData');
						closeDialog();
					})
					.catch((error: HandledError) => {
						if (!error.isHandled) {
							const errorCode = error.code;
							const errorMessage = error.message;
							ElMessage.error(errorMessage);
						}
					})
					.finally(() => {
						state.saveLoading = false;
					});
			});
		};
		//根据id获取完整的信息
		const getData = (id: any) => {
			apiApi.GetByKey(id).then((rs) => {
				state.ruleForm = Object.assign({}, rs.data);
			});
		};
		//删除纪录
		const onDelete = () => {
			ElMessageBox.confirm(`Are you sure to delete this?`, 'Tips', {
				confirmButtonText: 'OK',
				cancelButtonText: 'No，Thanks',
				type: 'warning',
			}).then(() => {
				state.deleteLoading = true;
				apiApi
					.DeleteByKey(state.ruleForm.id)
					.then(() => {
						ElMessage.success('Succeed');
						context.emit('fetchData');
						closeDialog();
					})
					.catch((error: HandledError) => {
						if (!error.isHandled) {
							const errorCode = error.code;
							const errorMessage = error.message;
							ElMessage.error(errorMessage);
						}
					})
					.finally(() => {
						state.deleteLoading = false;
					});
			});
		};
		// 重置表单
		const onInitForm = () => {
			state.isShowDialog = true;
			if (proxy.$refs.ruleFormRef) {
				proxy.$refs.ruleFormRef.resetFields();
			}
			state.ruleForm = {
				id: 0, //
				isDeleted: false, //
				name: '', //
				linkUrl: '', //
				area: '', //
				controller: '', //
				action: '', //
				icon: '', //
				code: '', //
				orderSort: 0, //
				description: '', //
				isMenu: false, //
				enabled: false, //
				createId: 0, //
				createBy: '', //
				createTime: new Date(), //
				modifyId: 0, //
				modifyBy: '', //
				modifyTime: new Date(), //
				parentId: 0, //
			};
		};
		return {
			openDialog,
			closeDialog,
			onCancel,
			onSubmit,
			onDelete,
			onInitForm,
			...toRefs(state),
		};
	},
};
</script>
