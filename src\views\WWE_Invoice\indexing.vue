<template>
	<div class="indexing-container layout-padding">
		<div class="split-layout">
			<!-- 左侧 - PDF 预览（大屏幕显示） -->
			<div class="left-content pdf-viewer" v-if="!isMobile">
				<object type="application/pdf" :data="pdfUrl" style="height: 100vh; width: 100%" allowfullscreen></object>
			</div>

			<!-- 右侧 - 发票信息 -->
			<div class="right-content invoice-info">
				<div class="scTable" style="height: 100%" ref="scTableMain">
					<el-card shadow="never" class="mb10" v-if="!invoiceId">
						<template #header>
							<div style="display: flex; justify-content: space-between; width: 100%">
								<el-button-group>
									<el-button type="primary" @click="handlePrevImage" :disabled="imageData.param.pageIndex <= 1">
										<el-icon>
											<ele-ArrowLeft />
										</el-icon>
										Previous Image
									</el-button>
									<el-button type="primary" @click="handleNextImage" :disabled="imageData.param.pageIndex >= imageData.total">
										Next Image
										<el-icon>
											<ele-ArrowRight />
										</el-icon>
									</el-button>
								</el-button-group>
							</div>
						</template>

						<el-descriptions :column="1" border>
							<!-- 始终显示部分 -->
							<el-descriptions-item label="Priority">
								<el-radio-group v-model="imageData.param.priority" @change="onImageCurrentChange(1)" :disabled="invoiceId">
									<el-radio label="Standard">Standard</el-radio>
									<el-radio label="Urgent">Urgent</el-radio>
								</el-radio-group>
							</el-descriptions-item>

							<el-descriptions-item label="Queue">
								<el-radio-group v-model="imageData.param.queue" @change="onImageCurrentChange(1)" :disabled="invoiceId">
									<el-radio :label="1">Queue1</el-radio>
									<el-radio :label="2">Queue2</el-radio>
									<el-radio :label="3">Queue3</el-radio>
									<el-radio :label="4">Queue4</el-radio>
								</el-radio-group>
							</el-descriptions-item>

							<!-- 折叠内容 -->
							<el-descriptions-item v-show="isExpanded" label="Batch #">{{ apImageObj.batchNum }}</el-descriptions-item>
							<el-descriptions-item v-show="isExpanded" label="Application Name">{{ apImageObj.applicationName }}</el-descriptions-item>
							<el-descriptions-item v-show="isExpanded" label="Receiving Type">{{ apImageObj.receivingType }}</el-descriptions-item>
							<el-descriptions-item v-show="isExpanded" label="Image Name">
								{{ apImageObj.originalFileName }}
								<label style="margin-left: 5px" v-if="imageData.total > 0"> [{{ imageData.param.pageIndex }} / {{ imageData.total }}] </label>
							</el-descriptions-item>
						</el-descriptions>
						<!-- 展开按钮 -->
						<div class="expand-toggle" @click="isExpanded = !isExpanded">
							<el-icon :class="{ rotate: isExpanded }"><ArrowDown /></el-icon>
							<span style="margin-left: 5px">{{ isExpanded ? 'Hide Details' : 'Show Details' }}</span>
						</div>
					</el-card>
					<el-card shadow="never" class="mb10">
						<template #header>
							<div class="card-header">
								<span>INVOICE INFORMATION</span>
							</div>
						</template>
						<el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="150px" size="middle" v-loading="imageData.loading">
							<el-row :gutter="35">
								<!-- Account Name -->
								<el-col :span="24" class="mb10">
									<el-form-item label="Project" prop="accountId">
										<el-select v-model="ruleForm.accountId" @change="handleAccountChange" placeholder="Select" filterable clearable>
											<el-option v-for="item in accountOptions" :key="item.key" :label="item.label" :value="item.key" />
										</el-select>
									</el-form-item>
								</el-col>

								<!-- Account# -->
								<el-col :span="24" class="mb10">
									<el-form-item label="WWE Account#" prop="accountId">
										<el-select v-model="ruleForm.accountId" placeholder="Select" filterable clearable @change="handleAccountChange">
											<el-option v-for="item in accountOptions" :key="item.key" :label="item.value" :value="item.key" />
										</el-select>
									</el-form-item>
								</el-col>

								<!-- Carrier -->
								<el-col :span="24" class="mb10">
									<el-form-item label="Carrier" prop="carrierId">
										<el-select v-model="ruleForm.carrierId" placeholder="Select" filterable clearable @change="handleCarrierChange">
											<el-option v-for="item in carrierOptions" :key="item.key" :label="item.label" :value="item.key" />
										</el-select>
									</el-form-item>
								</el-col>

								<!-- Invoice# -->
								<el-col :span="24" class="mb10">
									<el-form-item label="Invoice#" prop="invoiceNumber">
										<el-input v-model="ruleForm.invoiceNumber" placeholder="" />
									</el-form-item>
								</el-col>

								<!-- Prof# -->
								<el-col :span="24" class="mb10">
									<el-form-item label="Pro#" prop="proNumber">
										<el-input v-model="ruleForm.proNumber" placeholder="" ref="proNumberInput" />
									</el-form-item>
								</el-col>

								<!-- Invoice Amount -->
								<el-col :span="24" class="mb10">
									<el-form-item label="Invoice Amount" prop="amount">
										<el-input v-model="ruleForm.amount" placeholder="" :formatter="(value) => formatCurrency(value)" :parser="(value) => parseCurrency(value)" />
									</el-form-item>
								</el-col>

								<!-- Doc Retrieval Method -->
								<el-col :span="24" class="mb10">
									<el-form-item label="Doc Retrieval Method" prop="docRetrievalMethod">
										<el-select v-model="ruleForm.docRetrievalMethod" placeholder="Select" filterable clearable>
											<el-option v-for="item in retrievalMethodOptions" :key="item.itemId" :label="item.itemName" :value="item.itemValue" />
										</el-select>
									</el-form-item>
								</el-col>

								<!-- Doc Retrieval Status -->
								<el-col :span="24" class="mb10">
									<el-form-item label="Doc Retrieval Status" prop="docRetrievedStatus">
										<el-select v-model="ruleForm.docRetrievedStatus" placeholder="Select" filterable clearable>
											<el-option v-for="item in retrievedStatusOptions" :key="item.itemId" :label="item.itemName" :value="item.itemValue" />
										</el-select>
									</el-form-item>
								</el-col>

								<!-- Doc Retrieval Type -->
								<el-col :span="24" class="mb10">
									<el-form-item label="Doc Retrieval Type" prop="docRetrievalType">
										<el-select v-model="ruleForm.docRetrievalType" placeholder="Select" disabled multiple>
											<el-option v-for="item in retrievalTypeOptions" :key="item" :label="item" :value="item" />
										</el-select>
									</el-form-item>
								</el-col>

								<!-- Invoice Status -->
								<el-col :span="24" class="mb10">
									<el-form-item label="Invoice Status" prop="status">
										<el-select v-model="ruleForm.status" placeholder="Select" filterable clearable>
											<el-option v-for="item in invoiceStatusOptions" :key="item.itemId" :label="item.itemName" :value="item.itemValue" />
										</el-select>
									</el-form-item>
								</el-col>

								<!-- Comment -->
								<el-col :span="24" class="mb10">
									<el-form-item label="Comment" prop="comment">
										<el-input v-model="ruleForm.comment" placeholder="" type="textarea" />
									</el-form-item>
								</el-col>

								<!-- Submit Button -->
								<el-col :span="24" class="mb10" :style="invoiceId ? { marginLeft: '150px' } : { textAlign: 'center' }">
									<el-button type="primary" v-show="!invoiceId" :loading="loadingAccount" @click="onSubmit('SubmitAccount')" v-auth="'WWE_Invoice.SubmitAccount'"> Submit Account </el-button>
									<el-button type="primary" v-show="!invoiceId" :loading="loadingInvoice" @click="onSubmit('SubmitInvoice')" v-auth="'WWE_Invoice.SubmitInvoice'"> Submit Invoice </el-button>
									<el-button type="primary" v-show="invoiceId" :loading="loadingInvoice" @click="onSubmit('SubmitInvoice')" v-auth="'WWE_Invoice.Save'"> Save </el-button>
									<el-button type="primary" v-show="invoiceId" @click="handleReplaceImage" v-auth="'WWE_Invoice.ReplaceImage'"> Replace Image </el-button>
									<el-button type="primary" v-show="!invoiceId && ruleForm.imageId" @click="DeleteImage" v-auth="'WWE_Invoice.DeleteImage'"> Delete Image </el-button>
								</el-col>
							</el-row>
						</el-form>
					</el-card>
					<el-card shadow="never" class="mb10" v-if="proData && proData.length > 1">
						<template #header>
							<div class="card-header">
								<span>Pro# Records</span>
							</div>
						</template>
						<el-table :data="proData" border style="width: 100%">
							<el-table-column prop="proNumber" label="Pro#" min-width="120" />
							<el-table-column prop="scac" label="Carrier SCAC" min-width="150" />
							<el-table-column label="Actions" width="120">
								<template #default="{ row }">
									<el-button size="small" type="text" @click.stop="handleViewPro(row)">View</el-button>
								</template>
							</el-table-column>
						</el-table>
					</el-card>
					<el-card shadow="never" class="mb10" v-if="invoiceId">
						<template #header>
							<div class="card-header">
								<span>Email Logs</span>
							</div>
						</template>
						<el-table :data="emailData" border style="width: 100%">
							<el-table-column label="Subject" show-overflow-tooltip>
								<template #default="{ row }">
									<el-link type="primary" @click="handleResendEmail(row)">
										{{ row.subject }}
									</el-link>
								</template>
							</el-table-column>
							<el-table-column label="Status" width="100">
								<template #default="{ row }">
									<el-tag type="info" v-if="row.status === 0">{{ $t('message.emailStatus.draft') }}</el-tag>
									<el-tag type="warning" v-if="row.status === 1">{{ $t('message.emailStatus.sending') }}</el-tag>
									<el-tag type="success" v-if="row.status === 2">{{ $t('message.emailStatus.sended') }}</el-tag>
									<el-tag type="danger" v-if="row.status === 3">{{ $t('message.emailStatus.failed') }}</el-tag>
									<span v-if="![0, 1, 2, 3].includes(row.status)">{{ row.status }}</span>
								</template>
							</el-table-column>
							<el-table-column label="Send Time" prop="sendTime" width="170" show-overflow-tooltip />
						</el-table>
					</el-card>
					<el-card shadow="never" class="mb10" v-if="invoiceId">
						<template #header>
							<div class="card-header">
								<span>Action History</span>
							</div>
						</template>
						<el-table :data="actionData" border style="width: 100%">
							<el-table-column fixed label="Action" show-overflow-tooltip width="120">
								<template #default="{ row }">
									{{ row.action }}
								</template>
							</el-table-column>
							<el-table-column fixed label="From" prop="fromStatus" width="80" show-overflow-tooltip />
							<el-table-column label="To" prop="toStatus" width="80" show-overflow-tooltip />
							<el-table-column label="Created by" prop="modifiedByName" width="100" show-overflow-tooltip />
							<el-table-column label="Created At" prop="createdAt" show-overflow-tooltip />
						</el-table>
					</el-card>
					<div class="footer-container"></div>
				</div>
			</div>
		</div>

		<!-- 小屏幕时用抽屉显示 PDF -->
		<el-drawer v-model="showPdfDrawer" title="发票 PDF" :size="'90%'" :append-to-body="true">
			<div class="drawer-pdf-container">
				<object type="application/pdf" :data="pdfUrl" style="height: 100vh; width: 100%"></object>
				<el-empty v-if="!pdfUrl" description="暂无 PDF 文件" />
			</div>
		</el-drawer>

		<ReplaceImage ref="replaceImageRef" @complete="completeReplaceImage" />
	</div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, onBeforeUnmount, reactive, toRefs, getCurrentInstance, onActivated, nextTick } from 'vue';
import VuePdfEmbed from 'vue-pdf-embed';
import { Document, ArrowLeft, ArrowRight } from '@element-plus/icons-vue';
import mittBus from '/@/utils/mitt';
import accountApi from '/@/api/WWE_Account';
import carrierApi from '/@/api/WWE_Carrier';
import dictItemApi from '/@/api/dictItem';
import invoiceApi from '/@/api/WWE_Invoice';
import invoiceHistoryApi from '/@/api/WWE_Invoice_Histories';
import invoiceEmailApi from '/@/api/WWE_Invoice_Email';
import imagesApi from '/@/api/WWE_Images';
import fileApi from '/@/api/Files';

import carrierLoginApi from '/@/api/WWE_Carrier_Login';

import { DictionaryOption, SelectOption } from '/@/models';
import { ElMessage } from 'element-plus';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';
import { useRouteParam } from '/@/utils/routeUtils';
import { Consts, URLs } from '/@/constants';
import ReplaceImage from './components/ReplaceImage.vue';

export default defineComponent({
	name: 'InvoicePdfViewer',
	components: { VuePdfEmbed, Document, ArrowLeft, ArrowRight, ReplaceImage },
	setup() {
		const { proxy } = getCurrentInstance() as any;
		const router = useRouter();
		const route = useRoute();
		const pdfUrl = ref('/assets/noimage-converted.pdf') as any;
		const currentPage = ref(1);
		const totalPages = ref(0);
		const showPdfDrawer = ref(false);
		const isMobile = ref(false);
		const ruleFormRef = ref();
		const replaceImageRef = ref();
		const { t } = useI18n();
		const state = reactive({
			invoiceId: undefined as string | undefined,
			loadingAccount: false,
			loadingInvoice: false,
			imageData: {
				data: [],
				total: 0,
				loading: false,
				selection: [],
				param: {
					pageIndex: 1,
					pageSize: 1,
					imageId: '',
					queue: 1 as number | null | undefined,
					priority: 'Standard' as string | null | undefined,
					indexed: false as boolean | null | undefined,
					order: 'CreatedAt',
					sort: 'asc',
				},
			},
			ruleForm: getDefaultInvoice(),
			rules: {
				accountId: [{ required: true, message: 'Please input Company Account', trigger: 'change' }],
				carrierId: [{ required: true, message: 'Please select Carrier', trigger: 'change' }],
				invoiceNumber: [{ required: true, message: 'Please input Invoice#', trigger: 'change' }],
				proNumber: [{ required: true, message: 'Please input Prof#', trigger: 'change' }],
				amount: [{ required: true, message: 'Please input Invoice Amount', trigger: 'change' }],
				docRetrievalMethod: [{ required: true, message: 'Please select Doc Retrieval Method', trigger: 'change' }],
				docRetrievedStatus: [{ required: true, message: 'Please select Doc Retrieval Status', trigger: 'change' }],
				docRetrievalType: [{ required: true, message: 'Please select Doc Retrieved Type', trigger: 'change' }],
				comment: [{ required: false, message: 'Please input Comment', trigger: 'change' }],
			},
			pdfFileUrl: '',
			apImageObj: {} as any,
			taskData: [] as any,
			vendorData: [] as any,
			tableData: [] as any,
			companyData: [] as any,
			paymentTermData: [] as any,
			paymentmethodData: [] as any,
			approverData: [] as any,
			invoiceStatusData: [] as any,
			workflowRegistryData: [] as any,
			workflowDesignData: [] as any,
			isWkDrawer: false,
			wkUrl: '',

			isLogDrawer: false,
			nodeLogs: [] as any,
			workflowCrmData: {} as any,
			permissions: [] as any[],
			lastActivity: {} as any,

			retrievalTypeOptions: [] as string[],
			retrievalMethodOptions: [] as DictionaryOption[],
			retrievedStatusOptions: [] as DictionaryOption[],
			invoiceStatusOptions: [] as DictionaryOption[],
			accountOptions: [] as SelectOption[],
			carrierOptions: [] as SelectOption[],

			proData: [],
			actionData: [],
			emailData: [],
			isExpanded: false,
			currentPdfUrl: null as string | null,
		});

		function getDefaultInvoice() {
			return {
				invoiceId: undefined,
				accountName: '',
				accountCode: '',
				accountId: null as any,
				carrierId: null,
				imageId: null,
				invoiceNumber: '',
				proNumber: '',
				amount: '',
				docRetrievalMethod: 'Web', // 默认值
				docRetrievedStatus: 'Normal', // 默认值
				docRetrievalType: [] as string[], // 默认值
				status: 'Draft', // 默认值
				comment: '',
			};
		}

		const loadData = async () => {
			const [dictItemsResponse, accountsResponse, carriersResponse] = await Promise.all([dictItemApi.Many(['Retrieval Method', 'Retrieved Status', 'Invoice Status']), accountApi.GetAccountOptions(), carrierApi.GetCarrierOptions()]);

			state.retrievalMethodOptions = [];
			state.retrievedStatusOptions = [];

			if (dictItemsResponse?.data) {
				const methodResult = dictItemsResponse.data.find((item) => item.dictValue === 'Retrieval Method');
				if (methodResult?.items) state.retrievalMethodOptions = methodResult.items;

				const statusResult = dictItemsResponse.data.find((item) => item.dictValue === 'Retrieved Status');
				if (statusResult?.items) state.retrievedStatusOptions = statusResult.items;

				const invoiceStatusResult = dictItemsResponse.data.find((item) => item.dictValue === 'Invoice Status');
				if (invoiceStatusResult?.items) state.invoiceStatusOptions = invoiceStatusResult.items;
			}

			state.accountOptions = accountsResponse?.data || [];
			state.carrierOptions = (carriersResponse?.data || []).map((carrier) => ({
				...carrier,
				label: `${carrier.value} - ${carrier.label}`,
			}));

			await loadInvoiceData();

			await loadImageData();
		};

		const loadInvoiceData = async () => {
			if (state.invoiceId) {
				const invoiceResponse = await invoiceApi.Detail(state.invoiceId);
				if (invoiceResponse?.data) {
					state.ruleForm = {
						...invoiceResponse.data,
						docRetrievalType: invoiceResponse.data.docRetrievalType ? invoiceResponse.data.docRetrievalType.split(',').map((item) => item.trim()) : [],
					};
					if (state.accountOptions.length > 0) {
						const account = state.accountOptions.find((a) => a.key === invoiceResponse.data.accountId);
						if (account) {
							state.ruleForm.accountName = account.label;
							state.ruleForm.accountCode = account.value;
						}
					}

					state.imageData.param.imageId = invoiceResponse.data.imageId;
					if (state.imageData.param.imageId) {
						state.imageData.param.indexed = null;
						state.imageData.param.pageIndex = 1;
						state.imageData.param.pageSize = 1;
						state.imageData.param.queue = null;
						state.imageData.param.priority = null;
					}
				}

				invoiceApi.GetProList({ imageId: invoiceResponse.data.imageId }).then((rs) => {
					state.proData = rs.data;
				});

				invoiceHistoryApi.GetList({ invoiceId: state.invoiceId }).then((rs) => {
					state.actionData = rs.data;
				});

				invoiceEmailApi.GetList({ invoiceId: state.invoiceId }).then((rs) => {
					state.emailData = rs.data;
				});
			}
		};

		const loadImageData = async () => {
			try {
				state.ruleForm.imageId = null;
				state.imageData.loading = true;
				
				// 清理之前的PDF URL
				if (state.currentPdfUrl) {
					URL.revokeObjectURL(state.currentPdfUrl);
					state.currentPdfUrl = null;
				}
				
				// 先设置默认PDF，但不要立即覆盖现有的有效URL
				if (!pdfUrl.value || pdfUrl.value === '/noimage-converted.pdf') {
					pdfUrl.value = '/noimage-converted.pdf';
				}
				
				if (state.imageData.param.pageIndex > state.imageData.total) {
					state.imageData.param.pageIndex = 1;
				}
				const response = await imagesApi.Query(state.imageData.param);
				state.imageData.data = response.data || [];
				state.imageData.total = response.totalCount || 0;

				state.apImageObj = {};
				if (response.data && response.data.length > 0) {
					state.apImageObj = response.data[0];
					state.ruleForm.imageId = state.apImageObj.id;
					state.imageData.param.priority = state.apImageObj.priority;
					state.imageData.param.queue = state.apImageObj.queue;
					const fileUrl = state.apImageObj.fileName || '';

					if (fileUrl) {
						try {
							const fileResponse = await fileApi.Invoice(fileUrl);

							if (fileResponse.data.type === 'text/plain') {
								const reader = new FileReader();
								reader.onload = () => {
									const errorObj = JSON.parse(reader.result as string);
									if (errorObj.resultMsg) {
										ElMessage.error(`${errorObj.resultMsg}`);
									}
								};
								reader.readAsText(fileResponse.data);
								pdfUrl.value = '/noimage-converted.pdf';
								return;
							}

							if (fileResponse && fileResponse.data) {
								// 创建新的URL对象并保存引用
								state.currentPdfUrl = URL.createObjectURL(fileResponse.data);
								pdfUrl.value = state.currentPdfUrl;
							}
						} catch (fileError) {
							console.error('Error loading PDF file:', fileError);
							pdfUrl.value = '/noimage-converted.pdf';
						}
					} else {
						pdfUrl.value = '/noimage-converted.pdf';
					}
				} else {
					pdfUrl.value = '/noimage-converted.pdf';
				}
			} catch (error) {
				console.error('Error loading image data:', error);
				pdfUrl.value = '/noimage-converted.pdf';
			} finally {
				state.imageData.loading = false;
			}
		};

		const handleAccountChange = (accountId: string) => {
			const selectedAccount = state.accountOptions.find((account) => account.key === accountId);

			handleCarrierChange();
		};

		const handleCarrierChange = () => {
			state.retrievalTypeOptions = [];
			state.ruleForm.docRetrievalType = [];
			const carrierId = state.ruleForm.carrierId;
			const accountId = state.ruleForm.accountId;
			if (carrierId && accountId) {
				const request = {
					accountId: accountId,
					carrierId: carrierId,
				};
				carrierLoginApi.CustomFileTypes(request).then((rs) => {
					if (rs.data && rs.data.length > 0) {
						state.retrievalTypeOptions = rs.data;
						state.ruleForm.docRetrievalType = rs.data;
					}
				});
			}
		};

		const onImageCurrentChange = (pageIndex: number) => {
			state.imageData.param.pageIndex = pageIndex;
			loadImageData();
		};

		const onSubmit = async (submitType: string) => {
			ruleFormRef.value.validate(async (valid: boolean) => {
				if (!valid) return;
				if (!state.invoiceId && !state.ruleForm.imageId) {
					ElMessage.error('Invoice image selection is required.');
					return;
				}

				if (submitType == Consts.SubmitType_Account) state.loadingAccount = true;
				else if (submitType == Consts.SubmitType_Invoice) state.loadingInvoice = true;

				const submitData = {
					...state.ruleForm,
					// 将数组转换为逗号分隔的字符串
					docRetrievalType: Array.isArray(state.ruleForm.docRetrievalType) ? state.ruleForm.docRetrievalType.join(',') : '',
					submitType,
				};
				await invoiceApi
					.Save(submitData)
					.then(async () => {
						ElMessage.success(t('message.page.Success'));

						if (state.invoiceId) {
							mittBus.emit('onCurrentContextmenuClick', Object.assign({}, { contextMenuClickId: 1, ...route }));
							router.go(-1); // 返回上一页
						} else {
							ruleFormRef.value.resetFields();
							state.ruleForm = getDefaultInvoice();

							if (submitType == Consts.SubmitType_Account) {
								state.ruleForm.imageId = state.apImageObj?.id;

								if (state.invoiceId) {
									invoiceApi.GetProList({ imageId: state.ruleForm.imageId }).then((rs) => {
										state.proData = rs.data;
									});
								}
							}

							if (submitType == Consts.SubmitType_Invoice) {
								state.apImageObj = {};
								if (state.imageData.total > 0) {
									state.imageData.total--;
								}

								loadImageData();
							}
						}
					})
					.catch((error: HandledError) => {
						if (!error.isHandled) {
							const errorCode = error.code;
							const errorMessage = error.message;
							ElMessage.error(errorMessage);
						}
					})
					.finally(() => {
						if (submitType == Consts.SubmitType_Account) state.loadingAccount = false;
						else if (submitType == Consts.SubmitType_Invoice) state.loadingInvoice = false;
					});
			});
		};

		const formatCurrency = (value: string): string => {
			if (!value) return '$';

			// 移除所有非数字、减号、小数点
			let raw = value.replace(/[^0-9.-]/g, '');

			// 判断是否为负数（仅检查减号）
			const isNegative = raw.startsWith('-');

			// 去掉减号以便格式化（后面会再加回来）
			if (isNegative) {
				raw = raw.substring(1);
			}

			// 分离整数和小数部分
			const parts = raw.split('.');
			let integerPart = parts[0];
			// 限制小数位数为2位
			const decimalPart = parts.length > 1 ? `.${parts[1].substring(0, 2)}` : '';

			// 添加千分位
			integerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

			// 格式化结果（使用减号表示负数）
			const formatted = `$ ${integerPart}${decimalPart}`;
			return isNegative ? `-$ ${integerPart}${decimalPart}` : formatted;
		};

		const parseCurrency = (value: string): string => {
			// 去掉$、空格、逗号
			let cleaned = value.replace(/[$\s,]/g, '');

			// 检查是否有减号（仅允许开头有减号）
			const isNegative = cleaned.startsWith('-');
			if (isNegative) {
				cleaned = cleaned.substring(1);
			}

			// 限制小数位数为2位
			const parts = cleaned.split('.');
			if (parts.length > 1) {
				cleaned = `${parts[0]}.${parts[1].substring(0, 2)}`;
			}

			// 如果是负数，加回减号
			return isNegative ? `-${cleaned}` : cleaned;
		};

		// 检测屏幕尺寸
		const checkScreenSize = () => {
			isMobile.value = window.innerWidth <= 768;
		};

		// PDF 渲染完成回调
		const onPdfRendered = (e: any) => {
			totalPages.value = e.totalPages || 0;
			console.log('PDF 渲染完成:', totalPages.value);
		};

		// 翻页控制
		const handlePrevImage = () => {
			if (state.imageData.param.pageIndex > 1) {
				state.imageData.param.pageIndex--;
				loadImageData();
			}
		};

		const handleNextImage = () => {
			if (state.imageData.param.pageIndex < state.imageData.total) {
				state.imageData.param.pageIndex++;
				loadImageData();
			}
		};

		const handleReplaceImage = () => {
			console.log('Open replace image dialog for invoiceId1:', state.invoiceId);
			replaceImageRef.value?.open(state.invoiceId);
		};

		const handleViewPro = async (row: any) => {
			state.invoiceId = row.invoiceId;
			await loadInvoiceData();
			nextTick(() => {
				proxy.$refs.proNumberInput?.focus();
			});
		};

		const DeleteImage = () => {
			proxy
				.$confirm('Are you sure you want to delete this image?', 'Delete Image', {
					confirmButtonText: 'Delete',
					cancelButtonText: 'Cancel',
					type: 'warning',
				})
				.then(() => {
					const request = {
						imageId: state.ruleForm.imageId,
					};
					invoiceApi
						.DeleteImage(request)
						.then(() => {
							ElMessage.success('Image deleted successfully');
							loadImageData();
						})
						.catch((error: HandledError) => {
							if (!error.isHandled) {
								const errorCode = error.code;
								const errorMessage = error.message;
								ElMessage.error(errorMessage);
							}
						});
				})
				.catch(() => {});
		};

		const completeReplaceImage = async ({ success, imageId }) => {
			if (success && imageId) {
				state.imageData.param = {};
				state.imageData.param.imageId = imageId;
				loadImageData();
			}
		};

		const handleResendEmail = (row: any) => {
			// Format date from row (assuming sendTime exists)
			const sendDate = new Date(row.sendTime).toLocaleDateString('en-US');

			// Map status codes to text
			const statusMap = {
				0: 'Draft',
				1: 'Sending',
				2: 'Sent',
				3: 'Failed',
			};
			const statusText = statusMap[row.status] || 'Unknown';

			proxy
				.$confirm(`This email was sent on ${sendDate} [Status: ${statusText}]. What would you like to do?`, 'Email Resend', {
					confirmButtonText: 'Edit & Resend',
					cancelButtonText: 'Resend Now',
					distinguishCancelAndClose: true,
					showClose: true,
					closeOnClickModal: false,
					type: 'info',
				})
				.then(() => {
					router.push(`${URLs.SendEmail}/${row.emailId}`);
				})
				.catch((action: string) => {
					if (action === 'cancel') {
						const request = {
							id: row.emailId,
						};
						invoiceEmailApi
							.ReSendMissingDocsEmail(request)
							.then(() => {
								ElMessage.success('Email has been queued for resending');
							})
							.catch((error: HandledError) => {
								if (!error.isHandled) {
									const errorCode = error.code;
									const errorMessage = error.message;
									ElMessage.error(errorMessage);
								}
							});
					}
				});
		};

		onMounted(() => {
			nextTick(async () => {
				state.invoiceId = route?.params.id as string | undefined;
				if (state.invoiceId) {
					state.rules.invoiceNumber = [];
				}
				await loadData();
				checkScreenSize();
				window.addEventListener('resize', checkScreenSize);
			});
		});
		//从缓存中重新显示时触发
		onActivated(async () => {
			// 检查当前是否有有效的PDF URL，如果没有则重新加载
			if (!pdfUrl.value || pdfUrl.value === '/noimage-converted.pdf') {
				await loadImageData();
			} else {
				// 如果已有有效的PDF URL，只刷新数据但不重新加载PDF
				await refreshImageData();
			}
		});

		// 新增：只刷新数据而不重新加载PDF的函数
		const refreshImageData = async () => {
			try {
				state.imageData.loading = true;
				
				if (state.imageData.param.pageIndex > state.imageData.total) {
					state.imageData.param.pageIndex = 1;
				}
				const response = await imagesApi.Query(state.imageData.param);
				state.imageData.data = response.data || [];
				state.imageData.total = response.totalCount || 0;

				if (response.data && response.data.length > 0) {
					state.apImageObj = response.data[0];
					state.ruleForm.imageId = state.apImageObj.id;
					state.imageData.param.priority = state.apImageObj.priority;
					state.imageData.param.queue = state.apImageObj.queue;
				}
			} catch (error) {
				console.error('Error refreshing image data:', error);
			} finally {
				state.imageData.loading = false;
			}
		};

		onBeforeUnmount(() => {
			window.removeEventListener('resize', checkScreenSize);
			
			// 清理PDF URL对象
			if (state.currentPdfUrl) {
				URL.revokeObjectURL(state.currentPdfUrl);
				state.currentPdfUrl = null;
			}
		});

		return {
			ruleFormRef,
			replaceImageRef,
			pdfUrl,
			currentPage,
			totalPages,
			showPdfDrawer,
			isMobile,
			formatCurrency,
			parseCurrency,
			onPdfRendered,
			handlePrevImage,
			handleNextImage,
			onImageCurrentChange,
			onSubmit,
			handleAccountChange,
			handleCarrierChange,
			handleReplaceImage,
			completeReplaceImage,
			handleResendEmail,
			handleViewPro,
			DeleteImage,
			refreshImageData,
			...toRefs(state),
		};
	},
});
</script>

<style scoped lang="scss">
.indexing-container {
	background-color: #ebeef5;

	.split-layout {
		display: flex;
		height: 100%;
		width: 100%;
		gap: 15px;

		/* 左侧 - PDF 预览 */
		.left-content {
			flex: 0 0 60%;
			padding: 10px;
			background: #f5f5f5;
			border-right: 1px solid var(--el-border-color-light);
			display: flex;
			flex-direction: column;

			.pdf-embed {
				flex: 1;
				border: 1px solid #ddd;
				background: white;
			}

			.pdf-controls {
				margin-top: 10px;
				display: flex;
				align-items: center;
				justify-content: center;
				gap: 10px;

				.page-info {
					font-size: 14px;
					color: var(--el-text-color-regular);
				}
			}
		}

		/* 右侧 - 发票信息 */
		.right-content {
			flex: 1;
			overflow-y: auto;
			display: flex;
			flex-direction: column;
			justify-content: flex-start;
			align-items: stretch;

			.mobile-view-pdf-btn {
				margin-bottom: 15px;
			}
		}

		/* 小屏幕适配 */
		@media (max-width: 768px) {
			flex-direction: column;

			.left-panel {
				display: none;
				/* 隐藏 PDF 区域 */
			}
		}
	}

	/* 抽屉内 PDF 样式 */
	.drawer-pdf-container {
		height: 100%;
		padding: 10px;
		display: flex;
		flex-direction: column;

		.pdf-embed {
			flex: 1;
			border: 1px solid #ddd;
		}

		.pdf-controls {
			margin-top: 10px;
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 10px;
		}
	}

	:deep(.el-card__header) {
		padding: 10px 15px;
	}
	:deep(.el-card__body) {
		padding: 10px 20px;
	}
}
</style>
<style scoped>
.expand-toggle {
	cursor: pointer;
	color: #409eff;
	margin-top: 10px;
	display: flex;
	align-items: center;
	font-size: 12px;
}
.expand-toggle .el-icon {
	transition: transform 0.3s ease;
}
.expand-toggle .rotate {
	transform: rotate(180deg);
}

:deep(.el-descriptions__label) {
	width: 150px;
}
:deep(.el-descriptions__cell) {
	padding: 7px !important;
}
</style>