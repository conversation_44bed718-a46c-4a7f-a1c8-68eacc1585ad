<template>
	<div class="top-container">
		<div class="list-search">
			<el-card class="list-search-card" shadow="never">
				<el-form label-width="100px" @keyup.enter="onSearch">
					<el-row :gutter="24">
						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="5" class="mb1 mt1">
							<el-form-item :label="$t('message.filesLabels.OriginalName')" prop="originalName">
								<el-input v-model="state.formTable.params.originalName" clearable
									:placeholder="$t('message.page.searchKeyPlaceholder')" class="w-20"
									size="default"></el-input>
							</el-form-item>
						</el-col>

						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="5" class="mb1 mt1">
							<el-form-item :label="$t('message.filesLabels.CreatedAt')" prop="createdAt">
								<DateRangeSelector v-model:startDate="state.formTable.params.createdAtStart"
									v-model:endDate="state.formTable.params.createdAtEnd" dateType="date"
									:editable="true" :clearable="false" />
							</el-form-item>
						</el-col>

						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="5" class="mb1 mt1">
							<el-form-item class="nowidth" label=" ">
								<div class="searchBtn">
									<el-button type="primary" icon="search" @click="onSearch">
										{{ $t('message.page.buttonSearch') }}
									</el-button>
									<el-button type="danger" icon="refresh-left" @click="onClear">
										{{ $t('message.page.buttonReset') }}
									</el-button>
								</div>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>
			</el-card>
		</div>

		<div class="list-table">
			<el-card shadow="never">
				<el-header class="table_header">
					<div class="left-panel">
						<el-button type="primary" icon="plus" @click="onAdd" v-auth="'Files.Create'">
							{{ $t('message.filesButtons.createFiles') }}
						</el-button>

						<el-button type="primary" icon="bottom-left" @click="onImport" v-auth="'WWE_Shipment.Import'">
							{{ $t('message.filesButtons.importFiles') }}
						</el-button>
					</div>

					<div class="right-panel" v-auth="'Files.Export'">
						<Export ref="exportRef" pageName="Files" :pageParams="state.formTable.params" />
					</div>
				</el-header>

				<div class="scTable">
					<div class="scTable-table">
						<vxe-table show-overflow border="full" :min-height="25" ref="tableRef"
							:header-cell-config="{ height: 23, padding: false }"
							:row-config="{ isHover: true, height: 33 }" :column-config="{ resizable: true }"
							:checkbox-config="{ highlight: true }" :loading="state.formTable.loading"
							:loading-config="{ text: $t('message.page.loading') }" :tree-config="{ transform: true }"
							:scroll-y="{ enabled: false, gt: 0, mode: 'wheel' }" :data="state.formTable.data"
							:header-cell-style="headerCellStyle" :sortConfig="sortConfig" @sort-change="onSortChange"
							@cell-click="cellClick">
							<template #empty>
								<el-empty :description="$t('message.page.emptyDescription')"
									:image-size="100"></el-empty>
							</template>

							<vxe-column type="checkbox" width="50"></vxe-column>
							<vxe-column :title="$t('message.filesFields.FileBath')" field="fileBath" min-width="150"
								sortable>
								<template #default="{ row }">
									<el-link type="primary" @click.stop="onFileBath(row)">{{ row.fileBath
									}}</el-link>
								</template>
							</vxe-column>
							<vxe-column :title="$t('message.filesFields.TotalCount')" field="totalCount" min-width="150"
								sortable></vxe-column>
							<vxe-column :title="$t('message.filesFields.OriginalName')" field="originalName"
								min-width="150"></vxe-column>

							<vxe-column :title="$t('message.filesFields.CreatedAt')" field="createdAt" min-width="180"
								sortable></vxe-column>
							<vxe-column :title="$t('message.filesFields.UploadBy')" field="uploadedBy" min-width="120"
								sortable></vxe-column>

							<vxe-column fixed="right" :title="$t('message.page.actions')" width="90">
								<template #default="scope">
									<el-tooltip class="box-item" effect="dark" :content="$t('message.limits.Export')"
										placement="bottom" v-if="scope.row.fileId !== '0'">
										<vxe-button mode="text" status="primary" icon="vxe-icon-download"
											@click="onExport(scope.row)"></vxe-button>
									</el-tooltip>
								</template>
							</vxe-column>
						</vxe-table>
					</div>
					<div class="scTable-page">
						<el-pagination @size-change="onSizeChange" @current-change="onCurrentChange" :pager-count="5"
							:page-sizes="[20, 30, 50, 100]" v-model:current-page="state.formTable.params.pageIndex"
							background v-model:page-size="state.formTable.params.pageSize"
							layout="total, sizes, prev, pager, next, jumper" :total="state.formTable.total" small>
						</el-pagination>
					</div>
				</div>
			</el-card>
		</div>

		<createOrEdit ref="createOrEditRef" :title="state.fromTitle" @refreshData="onSearch" />
		<importData ref="importDataRef" title="Import Data" @refreshData="onSearch" />
		<importCsvData ref="importCsvDataRef" title="Import Data" @refreshData="onSearch" />
	</div>
</template>

<script lang="ts" setup name="files/index">
import { ref, computed, reactive, onMounted, nextTick, defineAsyncComponent, onActivated, onDeactivated, getCurrentInstance, h } from 'vue';
import { ElMessageBox, ElMessage, ElSelectV2 } from 'element-plus';
import type { TableInstance } from 'element-plus';
import { useI18n } from 'vue-i18n';

import DateSelector from '/@/components/common/DateSelector.vue';
import DateRangeSelector from '/@/components/common/DateRangeSelector.vue';
import { verifyNumberComma, verifyNumberCommaV2, verifyNumberIntegerAndFloat } from '/@/utils/toolsValidate';

import { default as filesApi } from '/@/api/WWE_Shipment_File/index';

import Export from '/@/components/export/index.vue';
import { VxeTablePropTypes, VxeGridInstance } from 'vxe-table';
import commonFilesApi from '/@/api/Files';
import { useRouter } from 'vue-router';
import { URLs } from '/@/constants';

const createOrEdit = defineAsyncComponent(() => import('./components/createOrEdit.vue'));
const importData = defineAsyncComponent(() => import('./components/ImportData.vue'));
const importCsvData = defineAsyncComponent(() => import('./components/ImportCsvData.vue'));
const router = useRouter();
const { proxy } = getCurrentInstance() as any;
const { t } = useI18n();
const createOrEditRef = ref();
const importDataRef = ref();
const importCsvDataRef = ref();
const exportRef = ref();

interface RowVO {
	fileId: number;
	fileCode: string;
	path: string;
	thumbPath: string;
	name: string;
	originalName: string;
	ext: string;
	size: number;
	downloads: number;
	extra: string;
	fileUrl: string;
	fileType: string;
	description: string;
	uploadById: number;
	fileServer: string;
	content: any;
	createdAt: string;
}

const tableRef = ref<VxeGridInstance<RowVO>>();

const sortConfig = ref<VxeTablePropTypes.SortConfig<RowVO>>({
	defaultSort: {
		field: 'UploadedAt',
		order: 'desc',
	},
	trigger: 'cell',
});

const headerCellStyle = ({ column }: any) => {
	if (column.sortable) {
		return {
			cursor: 'pointer',
		};
	}
};

function getDefaultQueryParams() {
	return {
		pageIndex: 1,
		pageSize: 20,
		order: 'createdAt',
		sort: 'desc',
		fileId: 0,
		fileCode: '',
		path: '',
		thumbPath: '',
		name: '',
		originalName: '',
		ext: '',
		size: '',
		downloads: '',
		extra: '',
		fileUrl: '',
		fileType: 'Excel',
		createdAt: null,
		createdAtStart: null,
		createdAtEnd: null,
	};
}

const state = reactive({
	fromTitle: '',
	maxHeight: 0,
	formTable: {
		data: [],
		loading: false,
		total: 0,
		params: getDefaultQueryParams(),
	},
	options: [
		{ label: t('message.userFields.Active'), value: false },
		{ label: t('message.userFields.Inactive'), value: true },
	],
});

// 设置默认的最大高度
const tableMaxHeight = computed(() => (state.maxHeight > 0 ? state.maxHeight : 500));

var isMounted = false;

onMounted(async () => {
	isMounted = true;

	if (isMounted) {
		onSearch();
	}

	updateMaxHeight();
	window.addEventListener('resize', updateMaxHeight);
});

const updateMaxHeight = () => {
	nextTick(() => {
		setTimeout(() => {
			state.maxHeight = window.innerHeight - 420;
		}, 100);
	});
};

onActivated(async () => {
	if (!isMounted) {
		await onSearch();
	}
});

onDeactivated(() => {
	isMounted = false;
});

const onAdd = () => {
	state.fromTitle = t('message.filesButtons.createFiles');
	createOrEditRef.value.open(getDefaultQueryParams());
};

const onImport = () => {
	state.fromTitle = 'Import Files';
	importDataRef.value.open();
};

const onImportCsv = () => {
	state.fromTitle = 'Import CategoryTest';
	importCsvDataRef.value.open();
};

const onEdit = (row: any) => {
	state.fromTitle = t('message.filesButtons.editFiles');
	createOrEditRef.value.open(row);
};

const onDelete = async (row: any) => {
	ElMessageBox.confirm(t('message.page.dlgDeleteText'), t('message.page.dlgTip'), {
		confirmButtonText: t('message.page.confirm'),
		cancelButtonText: t('message.page.cancel'),
		type: 'warning',
	})
		.then(async () => {
			if (row.id > 0) {
				await filesApi
					.DeleteByKey(row.id)
					.then((rs: any) => {
						ElMessage.success(t('message.page.deleteSuccess'));
						onSearch();
					})
					.catch((error: HandledError) => {
						if (!error.isHandled) {
							const errorCode = error.code;
							const errorMessage = error.message;
							ElMessage.error(errorMessage);
						}
					});
			}
		})
		.catch((error: any) => {
			if (error !== 'cancel') {
				ElMessage.error(error.resultMsg);
			}
		});
};

const onSearch = async () => {
	state.formTable.loading = true;

	await filesApi
		.TabsPageQuery(state.formTable.params)
		.then((rs: any) => {
			state.formTable.data = rs.data;
			state.formTable.total = rs.totalCount;

			let obj = { entityName: 'Files', pageParams: state.formTable.params };
			exportRef?.value?.getColumns(obj);
		})
		.catch((error: HandledError) => {
			if (!error.isHandled) {
				const errorCode = error.code;
				const errorMessage = error.message;
				ElMessage.error(errorMessage);
			}
		})
		.finally(() => {
			state.formTable.loading = false;

			if (state.formTable.data.length <= 0) {
				//数据为空，清空表头的默认选中
				nextTick(() => {
					proxy.$refs.tableRef.clearCheckboxRow();
				});
			}
		});
};

const onSortChange = (column: any) => {
	if (!column.field) {
		if (column.column.sortable) {
			state.formTable.params.order = column.column.field;

			let field = column.column.field;

			// 默认降序
			let order: 'desc' | 'asc' = 'desc';

			const $table = tableRef.value;

			if (state.formTable.params.sort == 'desc') {
				state.formTable.params.sort = 'asc';
				order = 'asc';

				if ($table) {
					$table.setSort({ field, order });
				}
			} else {
				state.formTable.params.sort = 'desc';
				order = 'desc';

				if ($table) {
					$table.setSort({ field, order });
				}
			}

			onSearch();
		}
	} else {
		state.formTable.params.order = column.field;
		state.formTable.params.sort = column.order;

		onSearch();
	}
};

const onClear = () => {
	clearQueryParams(state.formTable.params);
	onSearch();
};

const onExport = async (row: any) => {
	const fileId = row.fileId;
	// console.log('fileId', fileId);

	try {
		const response = await commonFilesApi.Download(fileId);
		// 获取文件名（可以从响应头中提取）
		const contentDisposition = response.headers?.['content-disposition'];
		// let fileName = 'downloaded-file.xlsx'; // 默认文件名
		let fileName = row.originalName || 'downloaded-file.xlsx'; // 默认文件名
		if (contentDisposition) {
			const matches = contentDisposition.match(/filename=(.+)$/);
			if (matches && matches[1]) {
				fileName = decodeURIComponent(matches[1].replace(/"/g, '')); // 去掉引号
			}
		}
		// 创建 Blob 对象并生成下载链接
		const blob = response as unknown as Blob;
		const link = document.createElement('a');
		link.href = URL.createObjectURL(blob);
		link.download = fileName; // 设置文件名
		link.click();

		// 释放资源
		URL.revokeObjectURL(link.href);
	} catch (error) {
		console.error('Download Failed:', error);
		ElMessage.error(t('message.page.downloadFailed'));
	}
};

function clearQueryParams(params: any): void {
	Object.assign(params, getDefaultQueryParams());
}

const onSizeChange = (val: number) => {
	state.formTable.params.pageSize = val;

	onSearch();
};

const onCurrentChange = (val: number) => {
	state.formTable.params.pageIndex = val;

	onSearch();
};

const cellClick = ({ row, column }: any) => {
	const $table = tableRef.value;
	if ($table) {
		$table.toggleCheckboxRow(row);
	}
};

const onFileBath = (row: any) => {
	router.push({
		path: URLs.ShipmentWeb,
		query: {
			fileBath: row.fileBath,
		},
	});
};
</script>

<style scoped lang="scss">
@media (max-width: 800px) {
	::v-deep .el-drawer {
		width: 100% !important;
	}
}
</style>
