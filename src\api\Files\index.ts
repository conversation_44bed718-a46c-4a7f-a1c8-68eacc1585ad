import BaseApi from '/@/api/BaseApi';
import request from '/@/utils/request';

class filesApi extends BaseApi {
  QueryV2(data: any) {
    return request({
      url: this.baseurl + 'QueryV2',
      method: 'post',
      data,
    });
  }
  Download(fileId: string) {
    return request({
      url: "/file/download/" + fileId,
      method: 'get',
      responseType: 'blob'
    })
  }

  Document(documentId: string) {
    return request({
      url: "/file/document/" + documentId,
      method: 'get',
      responseType: 'blob'
    })
  }

  Invoice(name: string) {
    return request({
      url: "/file/Invoice/" + encodeURIComponent(name),
      method: 'get',
      responseType: 'blob'
    })
  }

  Combined(name: string) {
    return request({
      url: "/file/Combined/" +  encodeURIComponent(name),
      method: 'get',
      responseType: 'blob'
    })
  }

  VisitLogs(data: any) {
		return request({
			url: `VisitLogs`,
			method: 'post',
			responseType: 'blob',
			data,
		});
	}

}

export default new filesApi('/api/Files/', 'id');
