export interface TokenDto {
	/** 登录标识 */
	token: string;

	/** Token 过期时间（秒） */
	exp: number;

	/** 刷新 Token */
	refreshToken: string;

	/** 刷新 Token 过期时间 */
	refreshTokenExp: number;

	/** 登录消息 */
	loginMsg: string;

	/** 应用版本 */
	appVersion: string;
}

export interface LoginInput {
	/** 登陆模式
	 *  0 账号登录
	 *  1 验证码登录
	 *  2 微信登录
	 *  3 邮箱登录
	 */
	loginType: number;

	/** 登录名称 */
	loginName: string;

	/** 登录密码 */
	password: string;

	/** 手机号码 */
	mobile: string;

	/** 验证码
	 *  XX123456789 这是一个特殊的验证码，标识不用验证密码，用于无密码登录
	 */
	smsCode: string;

	/** 重置密码 */
	resetPwd: string;
}