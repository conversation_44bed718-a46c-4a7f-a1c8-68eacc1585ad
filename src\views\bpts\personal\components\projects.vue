<template>
	<div class="email-content">
		<el-card style="height: 540px">
			<template #header>My Projects</template>

			<el-tree ref="treeRef" :data="remitToProjectData" default-expand-all :show-checkbox="false" clearable
				multiple collapse-tags filterable node-key="id" :props="{ children: 'children', isLeaf: 'isLeaf' }"
				:placeholder="$t('message.page.selectKeyPlaceholder')"
				style="width: 100%; max-height: 450px; overflow: auto" />
		</el-card>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, onMounted, defineComponent, watch, getCurrentInstance, ref } from 'vue';
import { ElMessageBox, ElMessage, ElTreeSelect } from 'element-plus';
import { useI18n } from 'vue-i18n';

export default defineComponent({
	name: 'projects',
	props: {
		info: Object,
	},
	setup(props, context) {
		const { t } = useI18n();
		const { proxy } = getCurrentInstance() as any;
		const treeRef = ref<InstanceType<typeof ElTreeSelect>>();
		console.log('info', props.info);

		const state = reactive({
			remitToProjectData: [],
		});

		watch(
			() => props.info,
			(newVal: any, oldVal) => {

			}
		);

		const loadUserRemitToProject = () => {

		};

		// 页面加载时
		onMounted(() => {
			loadUserRemitToProject();
		});

		return {
			treeRef,
			...toRefs(state),
		};
	},
});
</script>

<style scoped></style>