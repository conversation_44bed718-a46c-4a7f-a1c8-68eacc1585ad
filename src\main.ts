import { createApp } from 'vue';
import pinia from '/@/stores/index';
import App from '/@/App.vue';
import router from '/@/router';
import { directive } from '/@/directive/index';
import { i18n } from '/@/i18n/index';
import other from '/@/utils/other';

import ElementPlus from 'element-plus';
import { ElMessage } from 'element-plus';
import '/@/theme/index.scss';
//图标库
import * as ElementPlusIcons from '@element-plus/icons-vue'
import * as Icons from '@ant-design/icons-vue'
//vxe-table
import VxeUI from 'vxe-pc-ui'
import 'vxe-pc-ui/lib/style.css'
import VxeUITable from 'vxe-table'
import 'vxe-table/lib/style.css'
import VxeUIPluginRenderElement from '@vxe-ui/plugin-render-element'
import '@vxe-ui/plugin-render-element/dist/style.css'

import VueGridLayout from 'vue-grid-layout';
import scUploadFile from '/@/components/scUpload/file.vue';
import scCron from '/@/components/scCron/index.vue';
import scUpload from '/@/components/scUpload/index.vue';

import { setupAutoLogout } from './utils/autoLogout';
import { startRefreshingToken } from './utils/tokenRefresher';
import { setupStorageListener } from './utils/storageListener';
import appSettings from './config/index.js';

import enUS from 'vxe-table/lib/locale/lang/en-US' 


const app = createApp(App);
app.component('scUpload', scUpload);
app.component('scUploadFile', scUploadFile);
app.component('scCron', scCron);
// app.component('icon', antDesignIcons);
for (const [key, component] of Object.entries(ElementPlusIcons)) {
	app.component(key, component)
}
const icons: any = Icons
for (const i in icons) {
	app.component(i, icons[i])
}

directive(app);
other.elSvg(app);
VxeUI.use(VxeUIPluginRenderElement)
VxeUI.setI18n('en-US', enUS)
VxeUI.setLanguage('en-US')

// 设置自动退出
setupAutoLogout(router);
setupStorageListener(router);
startRefreshingToken();
app.use(pinia).use(router).use(ElementPlus).use(VxeUI).use(VxeUITable).use(i18n).use(VueGridLayout).mount('#app');

let messageInstance: { close: () => void } | null = null;

const resetMessage = (msg: any, type: any) => {
	if (messageInstance != null) {
		messageInstance.close();
	}
	messageInstance = ElMessage({ message: msg, type: type });
};

app.config.errorHandler = (err: any, vm, info) => {
	if (appSettings.LOGGING_ENABLED) {
		console.log('全局错误处理:', err, vm, info);
	}
};

//解决Uncaught ResizeObserver loop completed with undelivered notifications.报错
const debounce = (fn:any, delay:any) => {
	let timer:any
	return (...args:any[]) => {
		if (timer) {
			clearTimeout(timer)
		}
		timer = setTimeout(() => {
			fn(...args)
		}, delay)
	}
}
   
const _ResizeObserver = window.ResizeObserver;
window.ResizeObserver = class ResizeObserver extends _ResizeObserver{
	constructor(callback:any) {
		callback = debounce(callback, 16);
		super(callback);
	}
}
app.config.globalProperties.$appSettings = appSettings;
app.config.globalProperties.$message = resetMessage;
