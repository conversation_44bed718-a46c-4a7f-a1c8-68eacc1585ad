<template>
	<div style="color: #f56c6c; padding: 5px">{{ state.loginMsg }}</div>
	<el-form ref="ruleFormRef" :model="state.ruleForm" :rules="state.rules" size="large" class="login-content-form">
		<el-form-item class="login-animation1" prop="loginName">
			<el-input ref="unameInput" text :placeholder="$t('message.login.Username')" v-model="state.ruleForm.loginName" clearable autocomplete="off" @keyup.enter="onLogin">
				<template #prefix>
					<el-icon class="el-input__icon"><ele-User /></el-icon>
				</template>
			</el-input>
		</el-form-item>
		<el-form-item class="login-animation2" prop="password">
			<el-input :type="state.isShowPassword ? 'text' : 'password'" :placeholder="$t('message.login.Password')" v-model="state.ruleForm.password" autocomplete="off" @keyup.enter="onLogin">
				<template #prefix>
					<el-icon class="el-input__icon"><ele-Unlock /></el-icon>
				</template>
				<template #suffix>
					<el-icon @click="state.isShowPassword = !state.isShowPassword">
						<template v-if="state.isShowPassword">
							<img src="/img/show.svg" />
						</template>
						<template v-else>
							<img src="/img/hide.svg" />
						</template>
					</el-icon>
				</template>
			</el-input>
		</el-form-item>
		<!-- <el-form-item class="login-animation3">
			<el-col :span="15">
				<el-input
					text
					maxlength="4"
					:placeholder="$t('message.account.accountPlaceholder3')"
					v-model="state.ruleForm.code"
					clearable
					autocomplete="off"
				>
					<template #prefix>
						<el-icon class="el-input__icon"><ele-Position /></el-icon>
					</template>
				</el-input>
			</el-col>
			<el-col :span="1"></el-col>
			<el-col :span="8">
				<el-button class="login-content-code" v-waves>1234</el-button>
			</el-col>
		</el-form-item> -->

		<el-form-item style="margin-bottom: 1px">
			<el-row style="text-align: right; width: 100%; display: block">
				<!-- <el-col :span="12" v-if="false">
					<el-checkbox :label="$t('message.login.rememberMe')" v-model="ruleForm.autologin"></el-checkbox>
				</el-col> -->
				<!-- <el-col :span="12" style="text-align: right"> -->
				<el-button type="text" @click="openForgetPwd">{{ $t('message.login.forgetPassword') }}</el-button>
				<!-- </el-col> -->
			</el-row>
		</el-form-item>

		<el-form-item class="login-animation4">
			<el-button type="primary" class="login-content-submit" round v-waves @click="onLogin" :loading="state.loading.signIn">
				<span>{{ $t('message.account.accountBtnText') }}</span>
			</el-button>
			<div class="box" style="margin-top: 20px">
				<span class="line"></span>
				<span class="text">BPTS Internal Log in</span>
				<span class="line"></span>
			</div>

			<el-button type="primary" class="login-content-submit" round v-waves @click="onSSOLogin" :loading="state.loading.signInSSO">
				<span>{{ $t('message.account.accountSSOBtnText') }}</span>
			</el-button>
		</el-form-item>

		<el-dialog v-model="dialogVisible" title="Forgot Password" width="30%" :append-to-body="true" :destroy-on-close="true" :close-on-click-modal="false" :modal-append-to-body="true">
			<el-row style="text-align: center">
				<div style="margin-bottom: 20px; width: 100%">
					<b>{{ $t('message.login.forgetTips') }}</b>
				</div>
				<el-form ref="forgetFormRef" :model="forgetForm" :rules="state.forgetRules" label-position="top">
					<el-form-item :label="$t('message.login.userName')" :label-width="140" prop="UserName" style="display: inline-block; font-weight: bold">
						<el-input v-model="forgetForm.UserName" style="width: 400px" size="middle" />
					</el-form-item>
					<el-form-item :label="$t('message.login.email')" :label-width="140" prop="Email" style="display: inline-block; font-weight: bold">
						<el-input v-model="forgetForm.Email" style="width: 400px" size="middle" />
					</el-form-item>
				</el-form>
				<div style="margin-top: 20px; width: 100%">{{ $t('message.login.additional') }}</div>
			</el-row>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="handleClose" size="middle">{{ $t('message.login.cancel') }} </el-button>
					<el-button type="primary" @click="submitForgetPwd" size="middle"> {{ $t('message.login.resetPwd') }} </el-button>
				</span>
			</template>
		</el-dialog>
	</el-form>
</template>

<script setup lang="ts" name="loginAccount">
import { reactive, computed, ref, nextTick, onMounted } from 'vue';
import { LocationQueryRaw, useRoute, useRouter } from 'vue-router';
import { ElLoading, ElMessage, FormInstance } from 'element-plus';
import { storeToRefs } from 'pinia';
import { useThemeConfig } from '/@/stores/themeConfig';
import { initFrontEndControlRoutes } from '/@/router/frontEnd';
import { initBackEndControlRoutes } from '/@/router/backEnd';
import { Local, Session } from '/@/utils/storage';
import { formatAxis } from '/@/utils/formatTime';
import { NextLoading } from '/@/utils/loading';
import { URLs, LocalStorageKeys, CookieKeys, SessionStorageKeys } from '/@/constants';
import { TokenDto } from '/@/models';
import { useI18n } from 'vue-i18n';
import userApi from '/@/api/user';
import loginApi from '/@/api/login';
import { useUserInfo } from '/@/stores/userInfo';
import pinia from '/@/stores';
import { dynamicRoutes } from '/@/router/route';

const ruleFormRef = ref<FormInstance>();

// 定义变量内容
const { t } = useI18n();
let loadingAutoLogin: any = null;
const dialogVisible = ref(false);
const forgetFormRef = ref<FormInstance>();
const unameInput = ref();
const forgetForm = reactive({
	UserName: '',
	Email: '',
});

const stores = useUserInfo();
const { userInfos } = storeToRefs(stores);

// 定义变量内容
const storesThemeConfig = useThemeConfig();
const { themeConfig } = storeToRefs(storesThemeConfig);
const route = useRoute();
const router = useRouter();

// 定义变量内容
let ssoCheckTimer: number | null = null;
const SSO_CHECK_INTERVAL = 3000; // 3秒检查一次
const MAX_SSO_CHECK_COUNT = 10; // 最大检查次数
let ssoCheckCount = 0;

const state = reactive({
	isShowPassword: false,
	ruleForm: {
		autologin: false,
		loginName: '',
		password: '',
		code: '',
		userType: 'Staff Member',
		loginType: 0,
	},
	rules: {
		loginName: [{ required: true, message: t('message.login.inputUserName'), trigger: 'blur' }],
		password: [{ required: true, message: t('message.login.inputPwd'), trigger: 'blur' }],
	},
	forgetRules: {
		UserName: [{ required: true, message: t('message.validates.required'), trigger: 'blur' }],
		Email: [
			{ required: true, message: t('message.validates.required'), trigger: 'blur' },
			{
				pattern: /^[a-zA-Z0-9._-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,
				message: t('message.login.invalidEmail'),
				trigger: ['blur', 'change'],
			},
		],
	},
	loading: {
		signIn: false,
		signInSSO: false,
	},
	loginMsg: '' as string,
});

// 时间获取
const currentTime = computed(() => {
	return formatAxis(new Date());
});

const onLogin = async () => {
	ruleFormRef.value?.validate((valid: boolean) => {
		if (!valid) {
			return;
		}
		onSignIn(null);
	});
};

const onSSOLogin = async () => {
	Local.clearToken();

	startLoadingAutoLogin();

	await loginApi
		.SSOLogin({ bptsSSOLogin: '8c303c46-79d1-44cd-ade6-fd0b2c7bec18' })
		.then((rs) => {
			window.location.href = rs.data.data;
		})
		.finally(() => {
			if (loadingAutoLogin != null) {
				loadingAutoLogin.close();
			}
		});
};

const onSignIn = async (resultJson: any) => {
	state.loading.signIn = true;
	state.loginMsg = '';
	try {
		//const response = await loginApi.Signin(state.ruleForm);
		//const rs = response.data as TokenDto;

		let rs: any;
		if (resultJson) {
			const resultObj = JSON.parse(resultJson);
			rs = {
				token: resultObj.Token,
				exp: resultObj.Exp,
				refreshToken: resultObj.RefreshToken,
			};
		} else {
			const response = await loginApi.Signin(state.ruleForm);
			console.log('Login response:', response);
			rs = response.data as TokenDto;
		}

		Local.clearSessionAndTokens();
		//Session.set(CookieKeys.UserName, state.ruleForm.loginName);
		Local.set(LocalStorageKeys.Token, rs.token);
		Local.set(LocalStorageKeys.TokenExp, rs.exp);
		Local.set(LocalStorageKeys.RefreshToken, rs.refreshToken);

		const userObj: UserInfos = await useUserInfo(pinia).getUserInfos();
		Local.remove(LocalStorageKeys.NeedToChangePwd);
		Local.set(LocalStorageKeys.SystemUrl, userObj.systemUrl);
		if (userObj.needToChangePwd) {
			Local.set(LocalStorageKeys.NeedToChangePwd, userObj.needToChangePwd);
		}

		const routeInitializer = themeConfig.value.isRequestRoutes ? initBackEndControlRoutes : initFrontEndControlRoutes;
		const isNoPower = await routeInitializer();
		signInSuccess(isNoPower);
	} catch (error) {
		if (typeof error === 'object' && error !== null && 'isHandled' in error && 'message' in error) {
			if (!error.isHandled) {
				const errorMessage = (error as { message: string }).message;
				state.loginMsg = errorMessage;
			}
		} else {
			ElMessage.error(t('message.page.LoginFailed'));
		}
	} finally {
		state.loading.signIn = false;
		if (loadingAutoLogin != null) {
			setTimeout(() => {
				loadingAutoLogin.close();
			}, 2000);
		}
	}
};

const signInSuccess = (isNoPower: boolean | undefined) => {
	if (isNoPower) {
		ElMessage.warning(t('message.login.noPermission'));
		Session.clear();
		return;
	}

	// 获取当前时间信息
	const currentTimeInfo = currentTime.value;
	let path = URLs.ROOT;
	let query: LocationQueryRaw = {};
	const queryParams = route.query?.params;
	const params = queryParams ? JSON.parse(<string>queryParams) : null;
	const crmAutoLogin = params ? params.crmAutoLogin : null;
	const needToChangePwd = Local.get(LocalStorageKeys.NeedToChangePwd);

	// 设置查询参数
	if (params && crmAutoLogin !== 1) {
		query = Object.keys(params).length > 0 ? params : '';
	}

	if (needToChangePwd) {
		// 判断是否需要修改密码
		path = URLs.ChangePassword;
	} else if (route.query?.redirect) {
		// 设置重定向路径 （这步还没有写完，可能导致出现404页面）
		path = <string>route.query?.redirect;
		//严格来说还要判断redirect是否包含在列表中，如果不在的话说明没有权限
	} else {
		// 获取初始路径
		path = getInitialPath();
	}
	// 执行路由跳转
	router.push({ path, query });
	// 显示登录成功提示信息
	const signInText = t('message.signInText');
	ElMessage.success(`${signInText}`);
	// 添加 loading，防止第一次进入界面时出现短暂空白
	NextLoading.start();
};

const getInitialPath = () => {
	let path = '/';
	//默认跳转到第一个菜单
	let menus = dynamicRoutes[0].children;
	if (menus && menus.length > 0) {
		let firstMenu = menus[0];
		let redirectPath = firstMenu.path;
		if (redirectPath) {
			path = redirectPath.toString();
		}
	}
	return path;
};

// 打开忘记密码对话框
const openForgetPwd = () => {
	dialogVisible.value = true;
	forgetForm.UserName = '';
	forgetForm.Email = '';

	// 确保弹窗挂载到 body
	nextTick(() => {
		const dialogWrapper = document.querySelector('.el-dialog__wrapper');
		if (dialogWrapper && dialogWrapper.parentElement !== document.body) {
			document.body.appendChild(dialogWrapper);
		}
	});
};

// 开始自动登录加载
const startLoadingAutoLogin = () => {
	loadingAutoLogin = ElLoading.service({
		lock: true,
		text: 'Loading',
		background: 'rgba(0, 0, 0, 0.7)',
	});
};

const handleClose = () => {
	forgetFormRef.value?.resetFields();
	dialogVisible.value = false;
};

// 提交忘记密码表单
const submitForgetPwd = () => {
	forgetFormRef.value?.validate((valid: boolean) => {
		if (!valid) {
			return;
		} else {
			userApi
				.ForgetPwd(forgetForm)
				.then((rs: any) => {
					if (rs.data == false) {
						ElMessage.error(t('message.login.error'));
					} else {
						ElMessage.success(t('message.login.success'));
						dialogVisible.value = false;
					}
				})
				.catch((error: HandledError) => {
					if (!error.isHandled) {
						const errorCode = error.code;
						const errorMessage = error.message;
						ElMessage.error(errorMessage);
					}
				});
		}
	});
};

const startSSOCheck = (messageType: string) => {
	stopSSOCheck(); // 先停止之前的检查
	ssoCheckCount = 0;
	startLoadingAutoLogin();

	ssoCheckTimer = window.setInterval(async () => {
		ssoCheckCount++;
		state.loading.signInSSO = true;
		if (ssoCheckCount > MAX_SSO_CHECK_COUNT) {
			stopSSOCheck();
			state.loginMsg = t('message.login.ssoTimeout');
			state.loading.signInSSO = false;
			return;
		}

		try {
			const response = await loginApi.GetSSOLoginMessage({ dataKey: messageType });
			const result = response.data;
			if (result.data === 'SSOLogin1' || result.data === 'SSOLogin2') {
				state.loginMsg = result.resultMsg;
			} else if (result.data === 'SSOLogin3') {
				stopSSOCheck();
				onSignIn(result.resultMsg);

				const queryParams = route.query;
				const bptsSSOLogin = queryParams.bptsSSOLogin as string | undefined;
				if (bptsSSOLogin) {
					Session.set(SessionStorageKeys.BptsSSOLogin, bptsSSOLogin);
				}
			} else {
				state.loginMsg = result.resultMsg;
			}
		} catch (error) {
			ElMessage.error(t('message.login.ssoError'));
			state.loading.signInSSO = false;
		} finally {
			state.loading.signInSSO = false;
			stopSSOCheck();
		}
	}, SSO_CHECK_INTERVAL);
};

const stopSSOCheck = () => {
	if (ssoCheckTimer) {
		clearInterval(ssoCheckTimer);
		ssoCheckTimer = null;
	}
	if (loadingAutoLogin != null) {
		loadingAutoLogin.close();
	}
};
const handleSSOLogout = async () => {
	const tokenStr = Local.get(LocalStorageKeys.Token);
	if (tokenStr) {
		startLoadingAutoLogin();
		await loginApi
			.SignOut({ token: tokenStr, userName: userInfos.value.userName })
			.then(async (rs) => {
				Local.clearToken();
				loadingAutoLogin.close();
			})
			.finally(() => {
				loadingAutoLogin.close();
			});
	}
};
onMounted(async () => {
	const queryParams = route.query;
	const messageType = queryParams.messageType as string | undefined;
	const bptsSSOLogout = queryParams.bptsSSOLogout as string | undefined;

	// 情况1：SSO 登录
	if (messageType) {
		startSSOCheck(messageType);
	}
	// 情况2：SSO 退出
	else if (bptsSSOLogout === '8c303c46-79d1-44cd-ade6-fd0b2c7bec18') {
		await handleSSOLogout();
	} else {
		if (loadingAutoLogin != null) {
			loadingAutoLogin.close();
		}
	}
});
</script>

<style scoped lang="scss">
.login-content-form {
	margin-top: 20px;

	@for $i from 1 through 4 {
		.login-animation#{$i} {
			opacity: 0;
			animation-name: error-num;
			animation-duration: 0.5s;
			animation-fill-mode: forwards;
			animation-delay: calc($i/10) + s;
		}
	}

	.login-content-password {
		display: inline-block;
		width: 20px;
		cursor: pointer;

		&:hover {
			color: #909399;
		}
	}

	.login-content-code {
		width: 100%;
		padding: 0;
		font-weight: bold;
		letter-spacing: 5px;
	}

	.login-content-submit {
		width: 100%;
		letter-spacing: 2px;
		font-weight: 300;
		margin-top: 15px;
	}

	.el-icon img {
		height: 1em;
		width: 1em;
		cursor: pointer;
	}
}

.el-form-item :deep(.el-form-item__error) {
	display: block;
}

:deep(.el-dialog__wrapper) {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	overflow: auto;
	margin: 0;
	z-index: 2000;
	display: flex;
	align-items: center;
	justify-content: center;
}

:deep(.el-dialog) {
	margin: 0 auto;
	max-width: 500px;
	width: 90%;
}

.box {
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 20px 0;
	width: 100%;
}
.line {
	height: 1px;
	flex-grow: 1;
	background-color: #bcbcbc;
}

.text {
	font-size: 14px;
	padding: 0 10px;
	white-space: nowrap;
}
</style>
