<template>
	<div class="sc-upload-file">
		<el-upload
			:disabled="disabled"
			:auto-upload="autoUpload"
			:action="action"
			:name="name"
			:data="data"
			:http-request="request"
			v-model:file-list="defaultFileList"
			:show-file-list="showFileList"
			:drag="drag"
			:accept="accept"
			:multiple="multiple"
			:limit="limit"
			:before-upload="before"
			:on-success="success"
			:on-error="error"
			:on-preview="handlePreview"
			:on-exceed="handleExceed"
			:before-remove="beforeRemove">
			<slot>
				<el-button type="primary" :disabled="disabled">Click to upload </el-button>
			</slot>
			<template #tip>
				<div v-if="tip" class="el-upload__tip">{{ tip }}</div>
			</template>
		</el-upload>
		<!-- Progress indicator when show-file-list is false -->
		<div v-if="!showFileList && uploadProgress > 0" class="upload-progress">
			<el-progress :percentage="uploadProgress" :status="uploadStatus" :stroke-width="16" :text-inside="true" />
			<div v-if="uploadFileName" class="file-name">{{ uploadFileName }}</div>
		</div>

		<span style="display: none !important"><el-input v-model="value"></el-input></span>
	</div>
</template>

<script>
import config from '../../config/upload.js';

export default {
	props: {
		modelValue: { type: [String, Array], default: '' },
		tip: { type: String, default: '' },
		action: { type: String, default: '' },
		apiObj: { type: Object, default: () => {} },
		name: { type: String, default: config.filename },
		data: { type: Object, default: () => {} },
		accept: { type: String, default: '' },
		maxSize: { type: Number, default: config.maxSizeFile },
		limit: { type: Number, default: 0 },
		autoUpload: { type: Boolean, default: true },
		showFileList: { type: Boolean, default: true },
		drag: { type: Boolean, default: false },
		multiple: { type: Boolean, default: true },
		disabled: { type: Boolean, default: false },
		onSuccess: {
			type: Function,
			default: () => {
				return true;
			},
		},
		onBefore: {
			type: Function,
			default: () => {
				return true;
			},
		},
	},
	data() {
		return {
			value: '',
			defaultFileList: [],
		};
	},
	watch: {
		modelValue(val) {
			if (Array.isArray(val)) {
				if (JSON.stringify(val) != JSON.stringify(this.formatArr(this.defaultFileList))) {
					this.defaultFileList = val;
					this.value = val;
				}
			} else {
				if (val != this.toStr(this.defaultFileList)) {
					this.defaultFileList = this.toArr(val);
					this.value = val;
				}
			}
		},
		defaultFileList: {
			handler(val) {
				this.$emit('update:modelValue', Array.isArray(this.modelValue) ? this.formatArr(val) : this.toStr(val));
				this.value = this.toStr(val);
			},
			deep: true,
		},
	},
	mounted() {
		this.defaultFileList = Array.isArray(this.modelValue) ? this.modelValue : this.toArr(this.modelValue);
		this.value = this.modelValue;
	},
	methods: {
		//默认值转换为数组
		toArr(str) {
			console.log('str', str);
			var _arr = [];
			if (str) {
				var arr = str.split(',');
				arr.forEach((item) => {
					if (item) {
						var urlArr = item.split('/');
						var fileName = urlArr[urlArr.length - 1];
						_arr.push({
							name: fileName,
							url: item,
						});
					}
				});
			}
			console.log('_arr', _arr);
			return _arr;
		},
		//数组转换为原始值
		toStr(arr) {
			return arr.map((v) => v.url).join(',');
		},
		//格式化数组值
		formatArr(arr) {
			var _arr = [];
			arr.forEach((item) => {
				if (item) {
					const responseData = item.response ? item.response.data[0] : {};
					_arr.push({
						id: item.id,
						name: item.name,
						url: item.url,
						fileId: responseData.fileId || item.fileId,
						path: responseData.path || item.path,
						originalName: responseData.originalName || item.originalName,
						size: responseData.size || item.size,
						fileType: responseData.fileType || item.fileType,
						ext: responseData.ext || item.ext,
					});
				}
			});

			return _arr;
		},
		before(file) {
			console.log('file', this.onBefore);
			if (this.onBefore && this.onBefore(file) === false) {
				return false;
			}

			// 1. 首先检查文件类型
			if (this.accept) {
				const acceptedTypes = this.accept.split(',').map((type) => type.trim().toLowerCase()); // 统一转为小写
				const fileExtension = file.name.split('.').pop().toLowerCase(); // 确保后缀名小写
				const fileType = file.type.toLowerCase();

				const isTypeAllowed = acceptedTypes.some((type) => {
					if (type.startsWith('.')) {
						return type === `.${fileExtension}`; // 直接比较小写值
					} else if (type.endsWith('/*')) {
						const mainType = type.split('/')[0];
						return fileType.startsWith(`${mainType}/`);
					} else {
						return fileType === type;
					}
				});

				if (!isTypeAllowed) {
					const allowedTypes = this.accept; // 显示原始配置的大小写
					this.$message(`File type not supported. Allowed formats: ${allowedTypes}`, 'error');
					return false;
				}
			}

			// 2. 然后检查文件大小
			const maxSize = file.size / 1024 / 1024 < this.maxSize;
			if (!maxSize) {
				this.$message(`Maximum allowed file size: ${this.maxSize}MB`, 'error');
				return Promise.reject();
			}

			return true;
		},
		success(res, file) {
			var response = config.parseData(res);

			file.id = response.fileId;
			file.url = response.src;

			if (response?.src) {
				file.url = response.src.replace('undefined', '/');
			}

			var os = this.onSuccess(res, file);
			if (os != undefined && os == false) {
				return false;
			}
		},
		error(err) {
			this.$notify.error({
				title: 'File upload unsuccessful',
				message: err,
			});
		},
		beforeRemove(uploadFile) {
			if (uploadFile.status === 'ready') return true;
			return this.$confirm(`Are you sure you want to delete this file?`, 'Tip', {
				type: 'warning',
			})
				.then(() => {
					return true;
				})
				.catch(() => {
					return false;
				});
		},
		handleExceed() {
			this.$message(`Upload limit: ${this.limit} file${this.limit !== 1 ? 's' : ''}`, 'error');
		},
		handlePreview(uploadFile) {
			// window.open(uploadFile.url);
		},
		request(param) {
			var apiObj = config.apiObjFile;

			if (this.apiObj) {
				apiObj = this.apiObj;
			}

			if (param.action) {
				// eslint-disable-next-line vue/no-mutating-props
				apiObj.url = param.action;
			}

			const data = new FormData();

			data.append(param.filename, param.file);

			for (const key in param.data) {
				data.append(key, param.data[key]);
			}

			apiObj
				.post(data, {
					onUploadProgress: (e) => {
						const complete = parseInt(((e.loaded / e.total) * 100) | 0, 10);
						param.onProgress({ percent: complete });
					},
				})
				.then((res) => {
					var response = config.parseData(res);

					if (response.code == config.successCode) {
						param.onSuccess(res);
					} else {
						param.onError(response.msg || 'unknown error');
					}
				})
				.catch((err) => {
					console.log('err', err);
					param.onError(err);
				});
		},
	},
};
</script>

<style scoped>
.el-form-item.is-error .sc-upload-file:deep(.el-upload-dragger) {
	border-color: var(--el-color-danger);
}
.sc-upload-file {
	width: 100%;
}
.sc-upload-file:deep(.el-upload-list__item) {
	transition: none !important;
}
</style>
