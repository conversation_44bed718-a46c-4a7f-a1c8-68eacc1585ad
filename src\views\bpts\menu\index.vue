<template>
	<div class="top-container">
		<div class="list-search">
			<el-card class="search-form-card" shadow="never">
				<el-form label-width="100px" @keyup.enter="onInit" @submit.prevent>
					<el-row :gutter="24">
						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="6" class="mb1 mt1">
							<el-form-item label="Menu Name">
								<el-input v-model="menuTable.param.searchKey" size="middle"
									:placeholder="$t('message.page.searchKeyPlaceholder')" clearable
									class="w-20"></el-input>
							</el-form-item>
						</el-col>

						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="6" class="mb1 mt1">
							<el-form-item label=" ">
								<div class="searchBtn">
									<el-button type="primary" @click="onInit" icon="search">{{
										$t('message.page.buttonSearch') }}</el-button>
									<el-button type="danger" @click="onSearchReSet" icon="refresh-left">{{
										$t('message.page.buttonReset') }}</el-button>
								</div>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>

			</el-card>
		</div>

		<div class="list-table">
			<el-card shadow="never">
				<el-header class="table_header">
					<div class="left-panel">
						<el-button v-auth="'systemMenu.Create'" type="primary" @click="onOpenAddMenu" size="small"
							icon="plus">
							{{ $t('message.menuButtons.createMenu') }}
						</el-button>

						<el-button v-auth="'systemMenu.Create'" type="primary" @click="onOpenMenuButtons(null)"
							size="small" icon="plus">
							{{ $t('message.menuButtons.createButton') }}
						</el-button>
					</div>
					<div class="right-panel">
						<el-button @click="onInit" size="small">
							<el-icon><ele-Refresh /></el-icon>
							Refresh
						</el-button>
						<el-button v-if="false" type="success" @click="onSyn" size="small">
							{{ $t('message.page.buttonSyn') }}
						</el-button>
					</div>
				</el-header>

				<div class="scTable">
					<div class="scTable-table">
						<el-table ref="tableRef" v-loading="menuTable.loading" :data="menuTable.data" row-key="id"
							:default-expand-all="false" border
							:tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
							:row-style="{ height: '33px' }" :cell-style="{ padding: '0px' }" highlight-current-row
							@row-click="rowClick">
							<el-table-column :label="$t('message.menuFields.name')" width="250" show-overflow-tooltip>
								<template #default="scope">
									<SvgIcon :name="scope.row.icon" />
									<el-link @click="onOpenEditMenu(scope.row)" type="primary" class="ml10"
										style="vertical-align:top;padding-top: 0px;">
										{{ $t(scope.row.title) }}
									</el-link>
								</template>
							</el-table-column>
							<el-table-column v-if="false" label="类型" show-overflow-tooltip width="80">
								<template #default="scope">
									<el-tag type="success" size="small">{{ scope.row.xx }}菜单</el-tag>
								</template>
							</el-table-column>
							<el-table-column prop="path" width="300" :label="$t('message.menuFields.path')"
								show-overflow-tooltip></el-table-column>

							<el-table-column :label="$t('message.menuFields.PrivilegeOperation')" show-overflow-tooltip>
								<template #default="scope">
									<el-button v-for="item in scope.row.permissions" :key="item.id" size="small"
										@click="onOpenMenuButtons(item)">
										{{ $t(item.title) }}
									</el-button>
								</template>
							</el-table-column>
							<el-table-column :label="$t('message.menuFields.sort')" show-overflow-tooltip width="80">
								<template #default="scope">
									{{ scope.row.sort }}
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.page.actions')" show-overflow-tooltip width="90">
								<template #default="scope">

									<!-- <el-tooltip class="box-item" effect="dark" content="Edit" placement="bottom">
										<el-button v-auth="'systemMenu.Edit'" size="mini" type="text"
											@click="onOpenEditMenu(scope.row)" icon="Edit" title="">
										</el-button>
									</el-tooltip>

									<el-tooltip class="box-item" effect="dark" content="Delete" placement="bottom">
										<el-button v-auth="'systemMenu.Delete'"
											v-if="scope.row.id != 112 && scope.row.id != 957 && scope.row.id != 975 && scope.row.id != 1176"
											size="mini" type="text" style="color: #ff3a3a"
											@click="onTabelRowDel(scope.row)" icon="Delete" title="Delete">
										</el-button>
									</el-tooltip> -->

									<el-tooltip class="box-item" effect="dark" :content="$t('message.limits.Edit')"
										placement="bottom">
										<vxe-button mode="text" status="primary" icon="vxe-icon-edit"
											v-auth="'systemMenu.Edit'" @click="onOpenEditMenu(scope.row)"></vxe-button>
									</el-tooltip>

									<el-tooltip class="box-item" effect="dark" :content="$t('message.limits.Delete')"
										placement="bottom">
										<vxe-button mode="text" status="error" icon="vxe-icon-delete"
											v-if="scope.row.id != 112 && scope.row.id != 957 && scope.row.id != 975 && scope.row.id != 1176"
											v-auth="'systemMenu.Delete'" @click="onTabelRowDel(scope.row)"></vxe-button>
									</el-tooltip>

								</template>
							</el-table-column>
						</el-table>
					</div>
				</div>

				<EditMenu ref="editMenuRef" @fetchData="onInit" />
				<MenuButtons ref="menuButtonsRef" @fetchData="onInit" />
			</el-card>
		</div>
	</div>
</template>

<script lang="ts">
import { ref, toRefs, reactive, onMounted } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import EditMenu from '/@/views/bpts/menu/component/createOrEdit.vue';
import MenuButtons from '/@/views/bpts/menu/component/buttonCreateOrEdit.vue';
import { storeToRefs } from 'pinia';
import { useRoutesList } from '/@/stores/routesList';
import { useI18n } from 'vue-i18n';
import menuApi from '/@/api/menu/list';
import Auth from '/@/components/auth/auth.vue';
import type { TableInstance } from 'element-plus';

export default {
	name: 'systemMenu',
	components: { EditMenu, MenuButtons, Auth },
	setup() {
		const { t } = useI18n();
		const menuButtonsRef = ref();
		const editMenuRef = ref();
		const stores = useRoutesList();
		const { routesList } = storeToRefs(stores);
		const state = reactive({
			menuTable: {
				data: [],
				loading: false,
				param: {
					searchKey: '',
					menuType: 0, //只查询菜单 不查询 按钮
				},
			},
		});
		//初始化
		const onInit = () => {
			state.menuTable.loading = true;
			menuApi
				.Tree()
				.then((rs) => {
					state.menuTable.data = rs.data;
				})
				.catch((error: HandledError) => {
					if (!error.isHandled) {
						const errorCode = error.code;
						const errorMessage = error.message;
						ElMessage.error(errorMessage);
					}
				})
				.finally(() => (state.menuTable.loading = false));
		};
		const onSyn = () => {
			var r = routesList.value;
			var items = [...r];
			//console.log('item', items);
			//return;
			items.forEach((item) => {
				item.component = '' + item.component;
			});

			menuApi.SynRoute(items).then((rs) => {
				ElMessage.success(rs.resultMsg);
			});
		};
		// 打开新增菜单弹窗
		const onOpenAddMenu = () => {
			editMenuRef.value.openDialog({ action: 'Create' });
		};
		// 打开编辑菜单弹窗
		const onOpenEditMenu = (row: any) => {
			editMenuRef.value.openDialog({ action: 'Edit', id: row.id });
		};
		// 打开编辑菜单弹窗
		const onOpenMenuButtons = (row: object) => {
			menuButtonsRef.value.openDialog(row);
		};
		// 删除当前行 1
		const onTabelRowDel = (row: any) => {
			ElMessageBox.confirm(t('message.page.dlgDeleteText'), t('message.page.dlgTip'), {
				confirmButtonText: t('message.page.confirm'),
				cancelButtonText: t('message.page.cancel'),
				type: 'warning',
			})
				.then(() => {
					menuApi.DeleteByKey(row.id).then((rs) => {
						ElMessage.success(t('message.page.deleteSuccess'));
						onInit();
					});
				})
				.catch(() => { });
		};

		// 页面加载时
		onMounted(() => {
			onInit();
		});

		const tableRef = ref<TableInstance>()
		const selection = ref<[]>([])

		const rowClick = (row: { id: any; }, column: any, event: any) => {
			tableRef.value!.clearSelection()

			tableRef.value!.toggleRowSelection(
				row,
				true
			)
		}

		return {
			menuButtonsRef,
			editMenuRef,
			onSyn,
			onOpenAddMenu,
			onOpenEditMenu,
			onOpenMenuButtons,
			onTabelRowDel,
			onInit,
			...toRefs(state),
			tableRef,
			rowClick
		};
	},
};
</script>
