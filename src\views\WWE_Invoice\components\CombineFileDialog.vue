<template>
	<div class="combine-files-container">
		<el-dialog v-model="dialogVisible" title="Combine Files" class="combine-files-dialog" width="550px" :close-on-click-modal="false" draggable @closed="handleDialogClosed">
			<div class="dialog-header">
				<p class="tip-text">Combine files by selected records or current query</p>

				<div>
					<el-radio-group v-model="generateType" size="default" @change="handleGenerateTypeChange">
						<el-radio label="selected">Selected Items</el-radio>
						<el-radio label="query">Current Query</el-radio>
					</el-radio-group>
				</div>
			</div>

			<div class="content-section">
				<el-table :data="displayedRecords" border style="width: 100%">
					<el-table-column prop="completedCount" label="Total Count" align="center" />
					<el-table-column prop="success" label="Success Count" align="center" />
					<el-table-column prop="failed" label="Failed Count" align="center" />
					<el-table-column label="Download">
						<template #default="{ row }">
							<el-button v-if="row.successed" type="primary" size="small" @click="downloadFile(row)" :loading="downloadLoading">Download</el-button>
							<span v-else>-</span>
						</template>
					</el-table-column>
					<template #empty>
						<div class="empty-text">No records available</div>
					</template>
				</el-table>
			</div>

			<template #footer>
				<el-button @click="dialogVisible = false">Cancel</el-button>
				<el-button type="primary" :loading="processing" :disabled="displayedRecords.length === 0 || processingComplete" @click="startProcessing">
					{{ processingComplete ? 'Completed' : 'Start Combining' }}
				</el-button>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts">
import { defineComponent, reactive, computed, toRefs } from 'vue';
import { ElMessage } from 'element-plus';
import invoiceApi from '/@/api/WWE_Invoice';
import fileApi from '/@/api/Files';

export default defineComponent({
	name: 'CombineFilesDialog',
	props: {
		tableRef: {
			type: Object,
			required: true,
		},
		queryParams: {
			type: Object,
			required: false,
		},
	},
	emits: ['complete'],
	setup(props, { emit }) {
		const state = reactive({
			dialogVisible: false,
			generateType: 'selected',
			allRecords: [] as any[],
			processing: false,
			processedCount: 0,
			downloadLoading: false,
		});

		const displayedRecords = computed(() => state.allRecords);
		const processingComplete = computed(() => state.processedCount > 0);

		const openDialog = async () => {
			state.dialogVisible = true;
			state.generateType = 'selected';
			state.processedCount = 0;
			await loadSelectedRecords();
		};

		const handleGenerateTypeChange = async () => {
			state.processedCount = 0;
			if (state.generateType === 'selected') {
				await loadSelectedRecords();
			} else {
				await loadQueryRecords();
			}
		};
		const loadSelectedRecords = () => {
			const selected = props.tableRef.getCheckboxRecords?.() || [];
			//const completedRecords = selected.filter((r: any) => r.docRetrievedStatus === 'Completed');
			const processableRecords = selected;
			state.allRecords = [
				{
					invoiceIds: processableRecords.map((r) => r.invoiceId),
					total: selected.length,
					completedCount: processableRecords.length,
				},
			];
		};

		const loadQueryRecords = async () => {
			state.allRecords = [];
			const response = await invoiceApi.GetCombineSummary(props.queryParams);
			state.allRecords = [
				{
					total: response.data.total,
					completedCount: response.data.completedCount,
				},
			];
		};

		const startProcessing = async () => {
			if (state.allRecords.length === 0) return;
			state.processing = true;

			try {
				let requestPayload: any;

				if (state.generateType === 'selected') {
					const invoiceIds = state.allRecords[0].invoiceIds || [];
					requestPayload = {
						combineType: 'selected',
						isAuto: false,
						invoiceIds,
					};
				} else {
					requestPayload = {
						combineType: 'query',
						isAuto: false,
						...props.queryParams,
					};
				}

				const res = await invoiceApi.CombineShipmentFile(requestPayload);

				if (res.resultCode === 200 && res.data) {
					state.allRecords = [
						{
							total: res.data.total,
							completedCount: res.data.completedCount,
							success: res.data.success,
							failed: res.data.failed,
							downloadPath: res.data.filePath,
							successed: res.data.success > 0,
						},
					];
					state.processedCount = 1;
					ElMessage.success('Files combined successfully');
					emit('complete', { success: true });
				} else {
					state.allRecords = [
						{
							total: 0,
							completedCount: 0,
							success: 0,
							failed: 0,
							successed: false,
						},
					];
					ElMessage.error('File merge failed');
				}
			} catch (e) {
				console.error(e);
				state.allRecords = [
					{
						total: 0,
						completedCount: 0,
						success: 0,
						failed: 0,
						successed: false,
					},
				];
				ElMessage.error('An error occurred during merge');
			} finally {
				state.processing = false;
				emit('complete');
			}
		};

		const downloadFile = async (record: any) => {
			if (!record.downloadPath) {
				ElMessage.warning('No download path');
				return;
			}
			state.downloadLoading = true;
			try {
				const res = await fileApi.Combined(record.downloadPath);
				const url = window.URL.createObjectURL(new Blob([res.data]));
				const a = document.createElement('a');
				a.href = url;
				a.download = record.downloadPath.split('/').pop() || 'merged.pdf';
				document.body.appendChild(a);
				a.click();
				document.body.removeChild(a);
				window.URL.revokeObjectURL(url);
			} catch (e) {
				ElMessage.error('Download failed');
			} finally {
				state.downloadLoading = false;
			}
		};

		const handleDialogClosed = () => {
			state.allRecords = [];
			state.processing = false;
			state.processedCount = 0;
		};

		return {
			...toRefs(state),
			openDialog,
			handleDialogClosed,
			displayedRecords,
			processingComplete,
			handleGenerateTypeChange,
			startProcessing,
			downloadFile,
		};
	},
});
</script>

<style lang="scss" scoped>
.combine-files-dialog {
	.tip-text {
		margin-bottom: 20px;
	}
	.content-section {
		margin-top: 12px;
		height: 150px;
		overflow-y: auto;
	}
	.empty-text {
		text-align: center;
		padding: 12px 0;
		color: var(--el-text-color-secondary);
		font-size: 14px;
	}
	.status-info {
		margin: 8px 0;
		color: var(--el-text-color-regular);
		font-size: 13px;
	}
}
</style>
