// 定义内容
export default {
	router: {
		home: '首页',
		system: '系统设置',
		systemMenu: '菜单管理',
		systemRole: '角色管理',
		systemUser: '用户管理',
		systemDept: '部门管理',
		systemDic: '字典管理',
		limits: '权限管理',
		limitsFrontEnd: '前端控制',
		limitsFrontEndPage: '页面权限',
		limitsFrontEndBtn: '按钮权限',
		limitsBackEnd: '后端控制',
		limitsBackEndEndPage: '页面权限',
		menu: '菜单嵌套',
		menu1: '菜单1',
		menu11: '菜单11',
		menu12: '菜单12',
		menu121: '菜单121',
		menu122: '菜单122',
		menu13: '菜单13',
		menu2: '菜单2',
		funIndex: '功能',
		funTagsView: 'tagsView 操作',
		funCountup: '数字滚动',
		funWangEditor: 'Editor 编辑器',
		funCropper: '图片裁剪',
		funQrcode: '二维码生成',
		funEchartsMap: '地理坐标/地图',
		funPrintJs: '页面打印',
		funClipboard: '复制剪切',
		funGridLayout: '拖拽布局',
		funSplitpanes: '窗格拆分器',
		funDragVerify: '验证器',
		pagesIndex: '页面',
		pagesFiltering: '过滤筛选组件',
		pagesFilteringDetails: '过滤筛选组件详情',
		pagesFilteringDetails1: '过滤筛选组件详情111',
		pagesIocnfont: 'ali 字体图标',
		pagesElement: 'ele 字体图标',
		pagesAwesome: 'awe 字体图标',
		pagesFormAdapt: '表单自适应',
		pagesTableRules: '表单表格验证',
		pagesFormI18n: '表单国际化',
		pagesFormRules: '多表单验证',
		pagesDynamicForm: '动态复杂表单',
		pagesWorkflow: '工作流',
		pagesListAdapt: '列表自适应',
		pagesWaterfall: '瀑布屏',
		pagesSteps: '步骤条',
		pagesPreview: '大图预览',
		pagesWaves: '波浪效果',
		pagesTree: '树形改表格',
		pagesDrag: '拖动指令',
		pagesLazyImg: '图片懒加载',
		makeIndex: '组件封装',
		makeSelector: '图标选择器',
		makeNoticeBar: '滚动通知栏',
		makeSvgDemo: 'svgIcon 演示',
		makeTableDemo: '表格封装演示',
		paramsIndex: '路由参数',
		paramsCommon: '普通路由',
		paramsDynamic: '动态路由',
		paramsCommonDetails: '普通路由详情',
		paramsDynamicDetails: '动态路由详情',
		chartIndex: '大数据图表',
		visualizingIndex: '数据可视化',
		visualizingLinkDemo1: '数据可视化演示1',
		visualizingLinkDemo2: '数据可视化演示2',
		personal: '个人中心',
		tools: '工具类集合',
		layoutLinkView: '外链',
		layoutIframeViewOne: '内嵌 iframe1',
		layoutIframeViewTwo: '内嵌 iframe2',

		Login: '登录',
		dashboard: '控制面板',
		invoice: '发票管理',
		indexing: '发票录入',
		invoiceList: '发票列表',
		permission: '权限管理',
		organization: '组织架构',
		createCompany: '创建公司',
		editCompany: '编辑公司',
		position: '岗位管理',
		role: '角色管理',
		user: '用户管理',
		systemConfiguration: '系统配置',
		systemWebApi: 'Api管理',
		systemIndustry: '行业管理',
		systemLog: '日志管理',
		systemTask: '任务计划',
		systemSchedule: '任务计划',
		workflow: '工作流程',
		workflowEngine: '流程设计',
		workflowDefinitions: '流程创建',
		workflowInstances: '流程日志',
		workflowRegistry: '流程注册',
		workflowLaunch: '我发起的',
		workflowTodo: '待办事项',
		workflowDone: '已办事项',
		workflowCirculate: '抄送事项',
		dataManagement: '数据管理',
		vendor: '供货商管理',
		createVendor: '创建供货商',
		emailManagement: '邮件管理',
		emailList: '邮件列表',
		sendEmail: '新建邮件',
		emailNew: '新建邮件',
		emailTemplate: '邮件模板',
		viewEmail: '查看邮件',
		ticketManagement: '工单管理',
		ticketList: '工单列表',
		scheduledList: '计划列表',
		createTicket: '创建工单',
		editTicket: '编辑工单',
		categoryList: '分类管理',
		CreateCategory: '创建分类',
		groups: '员工组',
		groupsCreate: '创建组',
		deleteUserList: '已删除用户列表',
		projectsList: '项目列表',
		importList: '导入',
		changepassword: '修改密码',
		delegateList: '代理人',
		createScheduleTicket: '创建计划工单',
		category: '分类列表',
		glcodeexpense: '报销科目列表',
		glcodeinvoice: '发票科目列表',
		categoryVxe: 'Vxe分类列表',
		receiptManagement: '凭证管理',
		receiptList: '凭证列表',
		codeGenerate: '代码生成',
		codeGenerateIndex: '代码生成',
	},
	staticRoutes: {
		signIn: '登录',
		notFound: '找不到此页面',
		noPower: '没有权限',
	},
	user: {
		title0: '组件大小',
		title1: '语言切换',
		title2: '菜单搜索',
		title3: '布局配置',
		title4: '消息',
		title5: '开全屏',
		title6: '关全屏',
		dropdownLarge: '大型',
		dropdownDefault: '默认',
		dropdownSmall: '小型',
		dropdown1: '首页',
		dropdown2: '个人中心',
		dropdown3: '404',
		dropdown4: '401',
		dropdown5: '退出登录',
		dropdown6: '代码仓库',
		searchPlaceholder: '菜单搜索：支持中文、路由路径',
		newTitle: '通知',
		newBtn: '全部已读',
		newGo: '前往通知中心',
		newDesc: '暂无通知',
		logOutTitle: '提示',
		logOutMessage: '此操作将退出登录, 是否继续?',
		logOutConfirm: '确定',
		logOutCancel: '取消',
		logOutExit: '退出中',
		welcome: '欢迎',
	},
	tagsView: {
		refresh: '刷新',
		close: '关闭',
		closeOther: '关闭其它',
		closeAll: '全部关闭',
		fullscreen: '当前页全屏',
		closeFullscreen: '关闭全屏',
	},
	notFound: {
		foundTitle: '地址输入错误，请重新输入地址~',
		foundMsg: '您可以先检查网址，然后重新输入或给我们反馈问题。',
		foundBtn: '返回首页',
	},
	noAccess: {
		accessTitle: '您未被授权，没有操作权限~',
		accessMsg: '联系方式：加QQ群探讨 665452019',
		accessBtn: '重新授权',
	},
	layout: {
		configTitle: '布局配置',
		oneTitle: '全局主题',
		twoTopTitle: '顶栏设置',
		twoMenuTitle: '菜单设置',
		twoColumnsTitle: '分栏设置',
		twoTopBar: '顶栏背景',
		twoTopBarColor: '顶栏默认字体颜色',
		twoIsTopBarColorGradual: '顶栏背景渐变',
		twoMenuBar: '菜单背景',
		twoMenuBarColor: '菜单默认字体颜色',
		twoMenuBarActiveColor: '菜单高亮背景色',
		twoIsMenuBarColorGradual: '菜单背景渐变',
		twoColumnsMenuBar: '分栏菜单背景',
		twoColumnsMenuBarColor: '分栏菜单默认字体颜色',
		twoIsColumnsMenuBarColorGradual: '分栏菜单背景渐变',
		twoIsColumnsMenuHoverPreload: '分栏菜单鼠标悬停预加载',
		threeTitle: '界面设置',
		threeIsCollapse: '菜单水平折叠',
		threeIsUniqueOpened: '菜单手风琴',
		threeIsFixedHeader: '固定 Header',
		threeIsClassicSplitMenu: '经典布局分割菜单',
		threeIsLockScreen: '开启锁屏',
		threeLockScreenTime: '自动锁屏(s/秒)',
		fourTitle: '界面显示',
		fourIsShowLogo: '侧边栏 Logo',
		fourIsBreadcrumb: '开启 Breadcrumb',
		fourIsBreadcrumbIcon: '开启 Breadcrumb 图标',
		fourIsTagsview: '开启 Tagsview',
		fourIsTagsviewIcon: '开启 Tagsview 图标',
		fourIsCacheTagsView: '开启 TagsView 缓存',
		fourIsSortableTagsView: '开启 TagsView 拖拽',
		fourIsShareTagsView: '开启 TagsView 共用',
		fourIsFooter: '开启 Footer',
		fourIsGrayscale: '灰色模式',
		fourIsInvert: '色弱模式',
		fourIsDark: '深色模式',
		fourIsWartermark: '开启水印',
		fourWartermarkText: '水印文案',
		fiveTitle: '其它设置',
		fiveTagsStyle: 'Tagsview 风格',
		fiveAnimation: '主页面切换动画',
		fiveColumnsAsideStyle: '分栏高亮风格',
		fiveColumnsAsideLayout: '分栏布局风格',
		sixTitle: '布局切换',
		sixDefaults: '默认',
		sixClassic: '经典',
		sixTransverse: '横向',
		sixColumns: '分栏',
		tipText: '点击下方按钮，复制布局配置去 `src/stores/themeConfig.ts` 中修改。',
		copyText: '一键复制配置',
		resetText: '一键恢复默认',
		copyTextSuccess: '复制成功！',
		copyTextError: '复制失败！',
	},
	upgrade: {
		title: '新版本升级',
		msg: '新版本来啦，马上更新尝鲜吧！不用担心，更新很快的哦！',
		desc: '提示：更新会还原默认配置',
		btnOne: '残忍拒绝',
		btnTwo: '马上更新',
		btnTwoLoading: '更新中',
	},
	page: {
		loading: '加载中',
		buttonOk: '确认',
		buttonContinue: '继续',
		buttonSearch: '查询',
		buttonSave: '保存',
		buttonSavePermissions: '保存权限',
		buttonUpdate: '更新',
		changePwd: '修改密码',
		buttonCancel: '取消',
		buttonUpload: '上传',
		buttonClose: '关闭',
		buttonEdit: '修改',
		buttonReset: '重置',
		buttonDeleteBatch: '批量删除',
		searchKeyPlaceholder: '请输入',
		selectKeyPlaceholder: '请选择',
		buttonVisit: '查看',
		buttonCreate: '创建',
		buttonDelete: '删除',
		buttonRefresh: '刷新',
		buttonPrint: '打印',
		buttonExport: '导出',
		filterColumn: '筛选列',
		buttonLog: '日志',
		buttonExportEntireList: '所有数据',
		buttonExportSelectedRecords: '选择导出',
		buttonExecute: '立即执行',
		buttonExportExcel: '导出Excel',
		buttonExportCsv: '导出CSV',
		emptyDescription: '暂无数据',
		columnSelection: '选择',
		columnIndex: '序号',
		columnExpand: '',
		actions: '操作',
		actionsView: '查看',
		actionsDetail: '详细',
		actionsEdit: '编辑',
		actionsDelete: '删除',
		actionsSet: '设置',
		actionsTest: '测试',
		actionsCheckAll: '全选',
		dlgTip: '信息提示',
		dlgDeleteText: '您确定要删除该记录吗？',
		dlgDeleteFile: '您确定要删除该文件吗？',
		dlgReserveText: '您确定要删除该记录吗?',
		dlgDeleteSelectText1: '您确定删除选中的',
		dlgDeleteSelectText2: '项吗？',
		dlgReSendText: '您确定要再次发送邮件?',
		dlgExecuteText: '您确定要$[action]吗？',
		dlgImmediatelyText: '您确定要立即执行吗？',
		cancel: '取消',
		confirm: '确定',
		reSend: '重新发送',
		buttonSend: '发送',
		detailTitle: '信息详情',
		buttonPasswordReset: '密码重置',
		buttonSyn: '菜单同步',
		createTime: '创建时间',
		createBy: '创建人',
		createAt: '创建时间',
		sort: '排序',
		modifiedAt: '修改时间',
		Action: '操作',
		uploadFile: '上传附件',
		executeSuccess: '执行成功',
		startJob: '启动',
		stopJob: '停止',
		saveSuccess: '保存成功',
		deleteSuccess: '删除成功',
		Success: '成功',
		yes: '是',
		no: '否',
		hidden: '隐藏',
		nonhidden: '不隐藏',
		cache: '缓存',
		noCache: '不缓存',
		affix: '固定',
		noAffix: '不固定',
		iframe: '内嵌',
		noIframe: '不内嵌',
		resetPwd: '重置密码',
		confirmPwd: '确认密码',
		selectLeastAlert: '请选择一条记录',
		selectMoreAlert: '只能选择一条记录',
		pwdMatch: '密码必须一致',
		existCanNotDelete: "无法删除已使用的数据, 请点击'关闭',取消该删除操作。",
		fullScreen: '暂不支持全屏',
		dlgEditListText: '*注意：单击列表可编辑行信息。',
		PleaseUploadExcelFile: '请上传Excel文件',
		PleaseUploadCsvFile: '请上传CSV文件',
		PleaseWaitFileUploading: '请稍等，文件正在上传中',

		ImportSuccess: '导入成功',
		ImportFailed: '导入失败',
		OnlyXlsxFileAllowed: '仅支持 xlsx 格式文件',
		OnlyCsvFileAllowed: '仅支持 csv 格式文件',
		Row: '行',
	},
	limits: {
		Visit: '查看',
		Save: '保存',
		Create: '创建',
		Edit: '编辑',
		Delete: '删除',
		BitchDelete: '批量删除',
		Print: '打印',
		Export: '导出',
		AddCompany: '添加公司',
		SetDelegate: '设置委托人',
		AddDelegate: '添加委托人',
		BatchDeleteDelegate: '批量删除委托人',
		DelegateActionHistory: '操作历史',
		Restore: '恢复',
		AddItem: '添加明细',
		Import: '导入',
		RemoveRoleUser:'解除',
	},
	codeGenerate: {
		dbType: '数据库类型',
		pleaseSelectDbType: '请选择数据库类型',
		host: '主机地址',
		pleaseInputHost: '请输入主机地址',
		port: '端口',
		pleaseInputPort: '请输入端口号',
		database: '数据库名',
		pleaseInputDatabase: '请输入数据库名',
		username: '用户名',
		pleaseInputUsername: '请输入用户名',
		password: '密码',
		pleaseInputPassword: '请输入密码',
		testConnection: '测试连接',
		dbConfig: '数据库配置',
		connectionSuccess: '连接成功',
		connectionFailed: '连接失败',
		generateCode: '生成所有代码',
		generateSuccess: '代码生成成功',
		generateFailed: '代码生成失败',
		saveConfig: '保存配置',
		saveConfigSuccess: '配置保存成功',
		saveConfigFailed: '配置保存失败',
		loadConfigSuccess: '配置加载成功',
		loadConfigFailed: '配置加载失败',
		tableSelect: '选择数据表',
		tableName: '表名',
		tableComment: '表说明',
		actions: '操作',
		generate: '生成',
		batchGenerate: '批量生成',
		selectTableTip: '请选择要生成的表',
		loadTablesFailed: '加载表列表失败',
		batchGenerateSuccess: '批量生成成功',
		batchGenerateFailed: '批量生成失败',
		tableList: '表格类型',
		normalList: '普通列表',
		treeList: '树形列表',
		configSubTable: '配置子表',
		subTableConfig: '子表配置',
		mainTable: '主表',
		subTable: '子表',
		subTableName: '子表',
		foreignKey: '子表关联字段',
		parentKey: '主表关联字段',
		addSubTable: '添加子表',
		removeSubTable: '移除子表',
		tableComponent: '表格组件',
		elementTable: 'Element表格',
		vxeTable: 'Vxe表格',
		relationType: '关系类型',
		oneToMany: '一对多关系',
		manyToMany: '多对多关系',
		relationTable: '中间关系表',
		sourceKey: '关联主表字段',
		targetKey: '关联子表字段',
		oneToManyConfig: '一对多配置',
		manyToManyConfig: '多对多配置',
		foreignKeyDesc: '子表中用于关联主表的字段（如：子表中的主表ID）',
		parentKeyDesc: '主表中被子表关联的字段（如：主表的ID字段）',
		relationTableDesc: '用于关联主表和子表的中间表（如：用户角色关系表）',
		sourceKeyDesc: '中间表中关联主表的字段（如：中间表中的主表ID）',
		targetKeyDesc: '中间表中关联子表的字段（如：中间表中的子表ID）',
		relationTypeDesc: '选择表之间的关系类型：一对多或多对多',
		oneToManyDesc: '一个主表记录关联多个子表记录（如：订单和订单明细）',
		manyToManyDesc: '主表和子表通过中间表相互关联（如：用户和角色）',
	},
};
