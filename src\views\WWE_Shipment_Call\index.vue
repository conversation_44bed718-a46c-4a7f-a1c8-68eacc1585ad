<template>
	<div class="top-container">
		<div class="list-search">
			<el-card class="list-search-card" shadow="never">
				<el-form label-width="140px" @keyup.enter="onSearch">
					<el-row :gutter="24">
						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="5" class="mb1 mt1">
							<el-form-item :label="$t('message.wWE_InvoiceLabels.AccountName')" prop="accountId">
								<EnhancedTreeSelect v-model="state.formTable.params.accountId" :data="accountOptions"
									id-field="key" show-checkbox enable-check-all />
							</el-form-item>
						</el-col>

						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="5" class="mb1 mt1">
							<el-form-item :label="$t('message.wWE_InvoiceLabels.CarrierName')" prop="carrierId">
								<EnhancedTreeSelect v-model="state.formTable.params.carrierId" :data="carrierOptions"
									id-field="key" show-checkbox enable-check-all />
							</el-form-item>
						</el-col>

						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="5" class="mb1 mt1">
							<el-form-item :label="$t('message.wWE_InvoiceLabels.InvoiceNumber')" prop="invoiceNumber">
								<el-input v-model="state.formTable.params.invoiceNumber" clearable
									:placeholder="$t('message.page.searchKeyPlaceholder')" class="w-20"
									size="default"></el-input>
							</el-form-item>
						</el-col>

						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="5" class="mb1 mt1">
							<el-form-item :label="$t('message.wWE_InvoiceLabels.ProNumber')" prop="proNumber">
								<el-input v-model="state.formTable.params.proNumber" clearable
									:placeholder="$t('message.page.searchKeyPlaceholder')" class="w-20"
									size="default"></el-input>
							</el-form-item>
						</el-col>

						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="5" class="mb1 mt1">
							<el-form-item :label="$t('message.wWE_InvoiceLabels.DocRetrievedStatus')"
								prop="docRetrievedStatus">
								<EnhancedTreeSelect v-model="state.formTable.params.docRetrievedStatus"
									:data="retrievedStatusOptions" id-field="itemValue" label-field="itemName"
									show-checkbox enable-check-all />
							</el-form-item>
						</el-col>

						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="5" class="mb1 mt1">
							<el-form-item :label="$t('message.wWE_InvoiceLabels.BatchNumber')" prop="batchNumber">
								<el-input v-model="state.formTable.params.batchNumber" clearable
									:placeholder="$t('message.page.searchKeyPlaceholder')" class="w-20"
									size="default"></el-input>
							</el-form-item>
						</el-col>

						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="5" class="mb1 mt1">
							<el-form-item :label="$t('message.wWE_InvoiceLabels.CreatedAt')" prop="createdAt">
								<DateRangeSelector v-model:startDate="state.formTable.params.createdAtStart"
									v-model:endDate="state.formTable.params.createdAtEnd" dateType="day"
									:editable="true" :clearable="false" />
							</el-form-item>
						</el-col>

						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="5" class="mb1 mt1">
							<el-form-item class="nowidth" label=" ">
								<div class="searchBtn">
									<el-button type="primary" icon="search" @click="onSearch">
										{{ $t('message.page.buttonSearch') }}
									</el-button>
									<el-button type="danger" icon="refresh-left" @click="onClear">
										{{ $t('message.page.buttonReset') }}
									</el-button>
								</div>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>
			</el-card>
		</div>

		<div class="list-table">
			<el-card shadow="never">
				<el-header class="table_header">
					<div class="left-panel">
						<el-button type="primary" icon="bottom-left" @click="onImport" v-auth="'WWE_Shipment.Import'"
							v-if="false">
							{{ $t('message.wWE_ShipmentButtons.importWWE_Shipment') }}
						</el-button>
					</div>

					<div class="right-panel">
						<el-button type="primary" icon="Edit" v-auth="'wWE_Shipment.GenerateFileBatch'"
							@click="openGenerateDialog">
							{{ $t('message.wWE_ShipmentButtons.GenerateFileBatch') }}
						</el-button>

						<el-button type="primary" icon="Edit" @click="onMassStatusUpdate" :disabled="!hasSelection"
							v-auth="'WWE_Invoice.MassStatusUpdate'">
							{{ $t('message.wWE_InvoiceButtons.MassStatusUpdate') }}
						</el-button>

						<el-button type="primary" icon="Edit" @click="onMassMethodUpdate" :disabled="!hasSelection"
							v-auth="'WWE_Invoice.MassMethodUpdate'">
							{{ $t('message.wWE_InvoiceButtons.MassMethodUpdate') }}
						</el-button>

						<Export ref="exportRef" :table-type="3" v-auth="'WWE_Invoice.Export'" />
					</div>
				</el-header>

				<div class="scTable">
					<div class="scTable-table">
						<vxe-table show-overflow border="full" :min-height="25" ref="tableRef"
							:header-cell-config="{ height: 23, padding: false }"
							:row-config="{ isHover: true, height: 33 }" :column-config="{ resizable: true }"
							:checkbox-config="{ highlight: true }" :loading="state.formTable.loading"
							:loading-config="{ text: $t('message.page.loading') }" :tree-config="{ transform: true }"
							:scroll-y="{ enabled: false, gt: 0, mode: 'wheel' }" :data="state.formTable.data"
							:header-cell-style="headerCellStyle" :sortConfig="sortConfig" @sort-change="onSortChange"
							@cell-click="cellClick">
							<template #empty>
								<el-empty :description="$t('message.page.emptyDescription')"
									:image-size="100"></el-empty>
							</template>

							<vxe-column type="checkbox" width="50" fixed="left"></vxe-column>
							<vxe-column fixed="left" width="60">
								<template #default="{ row }">
									<el-badge :value="row.commentCount" :show-zero="false" class="comment-count"
										:hidden="row.ticketContentsTotal == 0"
										@click="openCommentDrawer(row.invoiceId)">
										<vxe-button mode="text" status="primary" icon="vxe-icon-comment"></vxe-button>
									</el-badge>
								</template>
							</vxe-column>
							<vxe-column :title="$t('message.wWE_InvoiceFields.ProNumber')" field="proNumber" width="120"
								fixed="left"></vxe-column>
							<vxe-column :title="$t('message.wWE_InvoiceFields.InvoiceNumber')" field="invoiceNumber"
								width="120" sortable fixed="left">
								<template #default="scope">
									<el-link @click="onEdit(scope.row)" type="primary">
										{{ scope.row.invoiceNumber || 'N/A' }}
									</el-link>
								</template>
							</vxe-column>
							<vxe-column :title="$t('message.wWE_InvoiceFields.AccountName')" field="accountName"
								min-width="120" sortable></vxe-column>
							<vxe-column :title="$t('message.wWE_InvoiceFields.CarrierName')" field="carrierName"
								width="120" sortable></vxe-column>

							<vxe-column :title="$t('message.wWE_InvoiceFields.CarrierContact')" field="carrierContact"
								width="170" sortable></vxe-column>
							<vxe-column :title="$t('message.wWE_InvoiceFields.CarrierContactEmail')"
								field="contactEmail" width="170" sortable></vxe-column>
							<vxe-column :title="$t('message.wWE_InvoiceFields.CarrierContactPhone')"
								field="contactPhone" width="170" sortable></vxe-column>

							<vxe-column :title="$t('message.wWE_InvoiceFields.BOL')" width="70">
								<template #default="{ row }">
									<template v-if="getDocumentTypeStatus(row, 'BOL')">
										<vxe-button v-if="row.bol" mode="text" status="primary" icon="vxe-icon-download"
											@click="openPdfDialog(row, 'BOL')">
										</vxe-button>
										<el-link v-else type="danger" @click.stop="openPdfDialog(row, 'BOL')">
											Missing
										</el-link>
									</template>
									<el-link v-else @click.stop="openPdfDialog(row, 'BOL')"> -- </el-link>
								</template>
							</vxe-column>

							<vxe-column :title="$t('message.wWE_InvoiceFields.POD')" width="70">
								<template #default="{ row }">
									<template v-if="getDocumentTypeStatus(row, 'POD')">
										<vxe-button v-if="row.pod" mode="text" status="primary" icon="vxe-icon-download"
											@click="openPdfDialog(row, 'POD')">
										</vxe-button>
										<el-link v-else type="danger" @click.stop="openPdfDialog(row, 'POD')">
											Missing
										</el-link>
									</template>
									<el-link v-else @click.stop="openPdfDialog(row, 'POD')"> -- </el-link>
								</template>
							</vxe-column>

							<vxe-column :title="$t('message.wWE_InvoiceFields.Others')" width="70">
								<template #default="{ row }">
									<template v-if="getDocumentTypeStatus(row, 'Others')">
										<vxe-button v-if="row.others" mode="text" status="primary"
											icon="vxe-icon-download" @click="openPdfDialog(row, 'Others')">
										</vxe-button>
										<el-link v-else type="danger" @click.stop="openPdfDialog(row, 'Others')">
											Missing </el-link>
									</template>
									<el-link v-else @click.stop="openPdfDialog(row, 'Others')"> -- </el-link>
								</template>
							</vxe-column>
							<vxe-column :title="$t('message.wWE_InvoiceFields.CombineFile')" width="120">
								<template #default="{ row }">
									<vxe-button v-if="row.combineFile" mode="text" status="primary"
										icon="vxe-icon-download" @click.stop="openPdfDialog(row, 'CombineFile')">
									</vxe-button>
									<vxe-tooltip v-if="row.combineFile && row.ftpUploaded" content="Uploaded to FTP">
										<i class="vxe-icon-flag-fill" style="color: #67c23a; font-size: 14px"></i>
									</vxe-tooltip>
								</template>
							</vxe-column>
							<vxe-column :title="$t('message.wWE_InvoiceFields.BatchNumber')" field="batchNumber"
								width="100" sortable></vxe-column>

							<vxe-column :title="$t('message.wWE_InvoiceFields.DocRetrievedStatus')" width="150"
								sortable>
								<template #default="{ row }">
									<!-- Normal 状态 -->
									<el-tag v-if="row.docRetrievedStatus === 'Normal'" type="primary" size="small">
										{{ row.docRetrievedStatus }}
									</el-tag>

									<!-- Completed 状态 -->
									<el-tag v-else-if="row.docRetrievedStatus === 'Completed'" type="success"
										size="small">
										{{ row.docRetrievedStatus }}
									</el-tag>

									<!-- Exception 状态 -->
									<el-tag v-else-if="row.docRetrievedStatus === 'Exception'" type="danger"
										size="small">
										{{ row.docRetrievedStatus }}
									</el-tag>

									<!-- Cancelled 状态 -->
									<el-tag v-else-if="row.docRetrievedStatus === 'Cancelled'" type="info" size="small">
										{{ row.docRetrievedStatus }}
									</el-tag>

									<!-- 默认状态（兜底） -->
									<el-tag v-else size="small">
										{{ row.docRetrievedStatus }}
									</el-tag>
								</template>
							</vxe-column>
							<vxe-column :title="$t('message.wWE_InvoiceFields.Aging')" field="aging" width="120"
								sortable></vxe-column>
							<vxe-column :title="$t('message.wWE_InvoiceFields.UploadPDF')" width="120">
								<template #default="{ row }">
									<el-badge :value="row.pdfCount || 0" :max="99" :hidden="!row.pdfCount"
										class="pdf-badge">
										<el-button type="primary" size="small"
											@click.stop="openPdfDialog(row, 'Additional')">
											<el-icon>
												<Upload />
											</el-icon>
										</el-button>
									</el-badge>
								</template>
							</vxe-column>
							<vxe-column :title="$t('message.wWE_InvoiceFields.Source')" field="source" width="120"
								sortable></vxe-column>

							<vxe-column fixed="right" :title="$t('message.page.actions')" width="90">
								<template #default="scope">
									<el-tooltip class="box-item" effect="dark" :content="$t('message.limits.Edit')"
										placement="bottom">
										<vxe-button mode="text" status="primary" icon="vxe-icon-edit"
											v-auth="'WWE_Invoice.Edit'" @click="onEdit(scope.row)"></vxe-button>
									</el-tooltip>
									<el-tooltip class="box-item" effect="dark" :content="$t('message.limits.Delete')"
										placement="bottom">
										<vxe-button mode="text" status="error" icon="vxe-icon-delete"
											v-auth="'WWE_Invoice.Delete'" @click="onDelete(scope.row)"></vxe-button>
									</el-tooltip>
								</template>
							</vxe-column>
						</vxe-table>
					</div>
					<div class="scTable-page">
						<el-pagination @size-change="onSizeChange" @current-change="onCurrentChange" :pager-count="5"
							:page-sizes="[20, 30, 50, 100]" v-model:current-page="state.formTable.params.pageIndex"
							background v-model:page-size="state.formTable.params.pageSize"
							layout="total, sizes, prev, pager, next, jumper" :total="state.formTable.total" small>
						</el-pagination>
					</div>
				</div>
			</el-card>
		</div>

		<GenerateFileBatchDialog ref="generateDialog" :table-ref="tableRef" :query-params="state.formTable.params"
			@complete="handleUpdateComplete" />
		<MassUpdateStatusDialog ref="massUpdateStatusDialog" :table-ref="tableRef" @complete="handleUpdateComplete" />
		<MassUpdateMethodDialog ref="massUpdateDialog" :table-ref="tableRef" @complete="handleUpdateComplete" />
		<InvoiceCommentsDrawer ref="commentsDrawerRef" :entityId="currentInvoiceId" entityType="Invoice"
			@action="handleDrawerAction" />
		<PdfUploadDialog ref="pdfUploadDialog" @complete="handleComplete" />
	</div>
</template>

<script lang="ts" setup name="wWE_Invoice/index">
import { ref, computed, reactive, onMounted, nextTick, defineAsyncComponent, onActivated, onDeactivated, getCurrentInstance, h } from 'vue';
import { ElMessageBox, ElMessage, ElSelectV2 } from 'element-plus';
import { useI18n } from 'vue-i18n';
import { VxeTablePropTypes, VxeGridProps, VxeGridInstance } from 'vxe-table';
import { useRouter } from 'vue-router';

import DateRangeSelector from '/@/components/common/DateRangeSelector.vue';
import Export from '/@/views/WWE_Invoice/components/Export.vue';
import EnhancedTreeSelect from '/@/components/select/EnhancedTreeSelect.vue';

import { URLs } from '/@/constants';
import { DictionaryOption, SelectOption } from '/@/models';
import { getDocumentTypeStatus } from '/@/utils/ShipmentService';
import accountApi from '/@/api/WWE_Account';
import carrierApi from '/@/api/WWE_Carrier';
import dictItemApi from '/@/api/dictItem';
import invoiceApi from '/@/api/WWE_Invoice/index';

const GenerateFileBatchDialog = defineAsyncComponent(() => import('../WWE_Invoice/components/GenerateFileBatchDialog.vue'));
const InvoiceCommentsDrawer = defineAsyncComponent(() => import('../WWE_Invoice/components/commentsDrawer.vue'));
const MassUpdateMethodDialog = defineAsyncComponent(() => import('../WWE_Invoice/components/MassUpdateMethodDialog.vue'));
const MassUpdateStatusDialog = defineAsyncComponent(() => import('../WWE_Invoice/components/MassUpdateStatusDialog.vue'));
const PdfUploadDialog = defineAsyncComponent(() => import('../WWE_Invoice/components/PdfUploadDialog.vue'));

const { proxy } = getCurrentInstance() as any;
const { t } = useI18n();
const router = useRouter();
const exportRef = ref();

const retrievedStatusOptions = ref<DictionaryOption[]>([]);
const retrievalTypeOptions = ref<DictionaryOption[]>([]);
const retrievalMethodOptions = ref<DictionaryOption[]>([]);
const invoiceStatusOptions = ref<DictionaryOption[]>([]);
const accountOptions = ref<SelectOption[]>([]);
const carrierOptions = ref<SelectOption[]>([]);

const generateDialog = ref();
const massUpdateDialog = ref();
const massUpdateStatusDialog = ref();
const commentsDrawerRef = ref();
const pdfUploadDialog = ref();

const commentDrawerVisible = ref(false);
const currentInvoiceId = ref('');

interface RowVO {
	invoiceId: string;
	accountId: string;
	carrierId: string;
	invoiceNumber: string;
	proNumber: string;
	amount: number;
	docRetrievalMethod: string;
	docRetrievedStatus: string;
	docRetrievalType: string;
	status: string;
	comment: string;
	createdByName: string;
	modifiedByName: string;
	createdById: number;
	createdAt: string;
	modifiedById: number;
	modifiedAt: string;
}

const tableRef = ref<VxeGridInstance<RowVO>>();

const sortConfig = ref<VxeTablePropTypes.SortConfig<RowVO>>({
	defaultSort: {
		field: 'createdAt',
		order: 'desc',
	},
	trigger: 'cell',
});

const headerCellStyle = ({ column }: any) => {
	if (column.sortable) {
		return {
			cursor: 'pointer',
		};
	}
};

function getDefaultQueryParams() {
	return {
		pageIndex: 1,
		pageSize: 20,
		order: 'createdAt',
		sort: 'desc',
		accountId: '' as string,
		carrierId: '' as string,
		invoiceNumber: '',
		proNumber: '',
		batchNumber: '',
		ignoreStatus: ['Cancelled', 'Exception'] as string[],
		docRetrievalMethod: ['Call'],
		docRetrievedStatus: [],
		docRetrievalType: [],
		status: [],
		createdAt: null,
		createdAtStart: null,
		createdAtEnd: null,
	};
}

const state = reactive({
	fromTitle: '',
	maxHeight: 0,
	formTable: {
		data: [],
		loading: false,
		total: 0,
		params: getDefaultQueryParams(),
	},
	options: [
		{ label: t('message.userFields.Active'), value: false },
		{ label: t('message.userFields.Inactive'), value: true },
	],
});

// 设置默认的最大高度
const tableMaxHeight = computed(() => (state.maxHeight > 0 ? state.maxHeight : 500));

var isMounted = false;

onMounted(async () => {
	isMounted = true;

	if (isMounted) {
		loadData();
		onSearch();
	}

	updateMaxHeight();
	window.addEventListener('resize', updateMaxHeight);
});

const updateMaxHeight = () => {
	nextTick(() => {
		setTimeout(() => {
			state.maxHeight = window.innerHeight - 460;
		}, 100);
	});
};

onActivated(async () => {
	if (!isMounted) {
		await onSearch();
	}
});

onDeactivated(() => {
	isMounted = false;
});

const hasSelection = computed(() => {
	const $table = tableRef.value;
	return $table ? $table.getCheckboxRecords().length > 0 : false;
});

const loadData = async () => {
	const [dictItemsResponse, accountsResponse, carriersResponse] = await Promise.all([dictItemApi.Many(['DocumentType', 'Retrieval Method', 'Retrieved Status', 'Invoice Status']), accountApi.GetAccountOptions(), carrierApi.GetCarrierOptions()]);

	retrievalTypeOptions.value = [];
	retrievalMethodOptions.value = [];

	if (dictItemsResponse?.data) {
		const docTypeResult = dictItemsResponse.data.find((item) => item.dictValue === 'DocumentType');
		if (docTypeResult?.items) retrievalTypeOptions.value = docTypeResult.items;

		const methodResult = dictItemsResponse.data.find((item) => item.dictValue === 'Retrieval Method');
		if (methodResult?.items) retrievalMethodOptions.value = methodResult.items;

		const statusResult = dictItemsResponse.data.find((item) => item.dictValue === 'Retrieved Status');
		if (statusResult?.items) retrievedStatusOptions.value = statusResult.items;

		const invoiceStatusResult = dictItemsResponse.data.find((item) => item.dictValue === 'Invoice Status');
		if (invoiceStatusResult?.items) invoiceStatusOptions.value = invoiceStatusResult.items;
	}

	accountOptions.value = accountsResponse?.data || [];
	carrierOptions.value = carriersResponse?.data || [];
	carrierOptions.value = (carriersResponse?.data || []).map((carrier) => ({
		...carrier,
		label: `${carrier.value} - ${carrier.label}`,
	}));
};

const onMassMethodUpdate = () => {
	massUpdateDialog.value.openDialog();
};

const onMassStatusUpdate = () => {
	massUpdateStatusDialog.value.openDialog();
};

const openPdfDialog = (invoice: any, fileType: string) => {
	pdfUploadDialog.value.openDialog(invoice, fileType);
};

const handleComplete = async ({ success }) => {
	if (success) {
		onSearch();
	}
};

const openGenerateDialog = () => {
	generateDialog.value.openDialog();
};

const handleUpdateComplete = async ({ success, ids }) => {
	if (success) {
		onSearch();
	}
};

const openCommentDrawer = (invoiceId: string) => {
	currentInvoiceId.value = invoiceId;
	commentsDrawerRef.value?.open();
};

const handleDrawerAction = (action: string) => {
	switch (action) {
		case 'close':
			commentDrawerVisible.value = false;
			break;
		case 'refresh':
			onSearch();
			break;
		case 'close-and-refresh':
			commentDrawerVisible.value = false;
			onSearch();
			break;
	}
};

const onImport = () => { };

const onEdit = (row: any) => {
	router.push(URLs.InvoiceIndexing + '/' + row.invoiceId);
};

const onDelete = async (row: any) => {
	ElMessageBox.confirm(t('message.page.dlgDeleteText'), t('message.page.dlgTip'), {
		confirmButtonText: t('message.page.confirm'),
		cancelButtonText: t('message.page.cancel'),
		type: 'warning',
	})
		.then(async () => {
			if (row.invoiceId) {
				await invoiceApi
					.DeleteByKey(row.invoiceId)
					.then((rs: any) => {
						ElMessage.success(t('message.page.deleteSuccess'));
						onSearch();
					})
					.catch((error: HandledError) => {
						if (!error.isHandled) {
							const errorCode = error.code;
							const errorMessage = error.message;
							ElMessage.error(errorMessage);
						}
					});
			}
		})
		.catch((error: any) => {
			if (error !== 'cancel') {
				ElMessage.error(error.resultMsg);
			}
		});
};

const onSearch = async () => {
	state.formTable.loading = true;

	await invoiceApi
		.Query(state.formTable.params)
		.then((rs: any) => {
			state.formTable.data = rs.data;
			state.formTable.total = rs.totalCount;

			let obj = { entityName: 'WWE_Invoice', pageParams: state.formTable.params };
			exportRef?.value?.getColumns(obj);
		})
		.catch((error: HandledError) => {
			if (!error.isHandled) {
				const errorCode = error.code;
				const errorMessage = error.message;
				ElMessage.error(errorMessage);
			}
		})
		.finally(() => {
			state.formTable.loading = false;

			if (state.formTable.data.length <= 0) {
				//数据为空，清空表头的默认选中
				nextTick(() => {
					proxy.$refs.tableRef.clearCheckboxRow();
				});
			}
		});
};

const onSortChange = (column: any) => {
	if (!column.field) {
		if (column.column.sortable) {
			state.formTable.params.order = column.column.field;

			let field = column.column.field;

			// 默认降序
			let order: 'desc' | 'asc' = 'desc';

			const $table = tableRef.value;

			if (state.formTable.params.sort == 'desc') {
				state.formTable.params.sort = 'asc';
				order = 'asc';

				if ($table) {
					$table.setSort({ field, order });
				}
			} else {
				state.formTable.params.sort = 'desc';
				order = 'desc';

				if ($table) {
					$table.setSort({ field, order });
				}
			}

			onSearch();
		}
	} else {
		state.formTable.params.order = column.field;
		state.formTable.params.sort = column.order;

		onSearch();
	}
};

const onClear = () => {
	clearQueryParams(state.formTable.params);
	onSearch();
};

function clearQueryParams(params: any): void {
	Object.assign(params, getDefaultQueryParams());
}

const onSizeChange = (val: number) => {
	state.formTable.params.pageSize = val;

	onSearch();
};

const onCurrentChange = (val: number) => {
	state.formTable.params.pageIndex = val;

	onSearch();
};

const cellClick = ({ row, column }: any) => {
	const $table = tableRef.value;
	if ($table) {
		$table.toggleCheckboxRow(row);
	}
};

</script>

<style scoped lang="scss">
@media (max-width: 800px) {
	::v-deep .el-drawer {
		width: 100% !important;
	}
}

.comment-count {
	margin-top: 10px;
	margin-right: 40px;
	cursor: pointer;
}
</style>
