﻿<template>
	<el-container class="onlineUser-index-container tablelist" style="height: 100%">
		<el-header>
			<div class="modules-index-search">
				<el-input size="small" v-model="tableData.param.searchKey" placeholder="请输入关键字" clearable
					style="max-width: 180px"> </el-input>
				<el-button size="small" type="primary" class="ml10" @click="onSearch">
					<el-icon>
						<ele-Search />
					</el-icon>
					查询
				</el-button>
			</div>
		</el-header>
		<el-header>
			<div class="left-panel">
				<el-button size="small" type="primary" class="ml10" @click="onAdd">
					<el-icon>
						<ele-Plus />
					</el-icon>
					创建
				</el-button>

				<el-button type="danger" size="small" icon="ele-Delete" :disabled="tableData.selection.length == 0"
					@click="onDeleteByList">删除</el-button>
			</div>
			<div class="right-panel">
				<el-button size="small" class="ml10" @click="onAdd">
					<el-icon>
						<ele-Refresh />
					</el-icon>
					刷新
				</el-button>
				<el-button size="small" class="ml10" @click="onAdd">
					<el-icon>
						<ele-ArrowDownBold />
					</el-icon>
					导出
				</el-button>
				<el-button size="small" class="ml10" @click="onPrint">
					<el-icon>
						<ele-Printer />
					</el-icon>
					打印
				</el-button>
			</div>
		</el-header>
		<el-main class="nopadding" ref="printMain">
			<div class="scTable" style="height: 100%" ref="scTableMain">
				<div class="scTable-table">
					<el-table :data="tableData.data" v-loading="tableData.loading" height="calc(100%)"
						@row-dblclick="onDetail" @selection-change="selectionChange">
						<template #empty>
							<el-empty description="暂无数据" :image-size="100"></el-empty>
						</template>
						<el-table-column type="selection" />

						<el-table-column label="id" show-overflow-tooltip>
							<template #default="{ row }">
								<span>{{ row.id }}</span>
							</template>
						</el-table-column>

						<el-table-column label="userId" show-overflow-tooltip>
							<template #default="{ row }">
								<span>{{ row.userId }}</span>
							</template>
						</el-table-column>

						<el-table-column label="clientIP" show-overflow-tooltip>
							<template #default="{ row }">
								<span>{{ row.clientIP }}</span>
							</template>
						</el-table-column>

						<el-table-column label="loginTime" width="200">
							<template #default="{ row }">
								<span>{{ formatStrDate(row.loginTime) }}</span>
							</template>
						</el-table-column>

						<el-table-column label="device" show-overflow-tooltip>
							<template #default="{ row }">
								<span>{{ row.device }}</span>
							</template>
						</el-table-column>

						<el-table-column label="modifyTime" width="200">
							<template #default="{ row }">
								<span>{{ formatStrDate(row.modifyTime) }}</span>
							</template>
						</el-table-column>

						<el-table-column label="description" show-overflow-tooltip>
							<template #default="{ row }">
								<span>{{ row.description }}</span>
							</template>
						</el-table-column>

						<el-table-column fixed="right" align="left" label="操作" width="100">
							<template #default="{ row }">
								<el-button size="mini" type="text" @click="onDetail(row)">查看</el-button>
								<el-button size="mini" type="text" @click="onEdit(row)">编辑</el-button>
								<el-button size="mini" type="text" @click="onDelete(row)">删除</el-button>
							</template>
						</el-table-column>
					</el-table>
				</div>
				<div class="scTable-page">
					<el-pagination @size-change="onSizechange" @current-change="onCurrentChange" :pager-count="5"
						:page-sizes="[10, 20, 30]" v-model:current-page="tableData.param.pageIndex" background
						v-model:page-size="tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
						:total="tableData.total">
					</el-pagination>
				</div>
			</div>

			<CreateOrEdit ref="createOrEditRef" @fetchData="onInit"></CreateOrEdit>
			<el-drawer v-model="infoDrawer" title="信息详情" :size="600" destroy-on-close>
				<Detail ref="detailRef" :info="detailObj"></Detail>
			</el-drawer>
		</el-main>
	</el-container>
</template>

<script lang="ts">
//import { useRouter } from 'vue-router';
import { toRefs, reactive, onMounted, ref, defineComponent } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { formatStrDate } from '/@/utils/formatTime';
import print from '/@/utils/print.js';

import onlineUserApi from '/@/api/onlineUser/index';
import Detail from './components/detail.vue';
import CreateOrEdit from './components/createOrEdit.vue';

export default defineComponent({
	name: 'onlineUserIndex',
	components: { Detail, CreateOrEdit },
	setup() {
		//const router = useRouter();
		const createOrEditRef = ref();
		const printMain = ref(null);
		const state = reactive({
			tableData: {
				data: [],
				total: 0,
				loading: false,
				selection: [],
				param: {
					pageIndex: 1,
					pageSize: 20,
					searchKey: '',
					order: 'id',
					sort: 'desc', // asc or desc
				},
			},
			infoDrawer: false,
			detailObj: {},
		});

		//初始化
		const onInit = () => {
			state.tableData.loading = true;
			onlineUserApi
				.Query(state.tableData.param)
				.then((rs) => {
					state.tableData.data = rs.data;
					state.tableData.total = rs.totalCount;
				})
				.catch((rs) => { })
				.finally(() => (state.tableData.loading = false));
		};
		//搜索
		const onSearch = () => {
			onInit();
		};

		// 添加
		const onAdd = () => {
			createOrEditRef.value.openDialog({ action: 'Create' });
			//router.push('/onlineUser/Create');
		};
		// 修改
		const onEdit = (row: Object) => {
			createOrEditRef.value.openDialog({ action: 'Edit', id: row.id });
			//router.push('/onlineUser/edit/' + row.id);
		};
		// 删除
		const onDelete = (row: Object) => {
			ElMessageBox.confirm(`此操作将永久当前纪录，是否继续?`, '提示', {
				confirmButtonText: '确认',
				cancelButtonText: '取消',
				type: 'warning',
			}).then(() => {
				onlineUserApi
					.DeleteByKey(row.id)
					.then((rs) => {
						if (!rs.data) {
							ElMessage.error(rs.resultMsg);
							return;
						}
						onInit();
					})
					.catch((rs) => { });
			});
		};
		//批量删除
		const onDeleteByList = () => {
			ElMessageBox.confirm(`确定删除选中的 ${state.tableData.selection.length} 项吗？`, '提示', {
				type: 'warning',
			}).then(() => {
				onlineUserApi.Delete({ keys: state.tableData.selection.join(',') }).then((rs) => {
					if (rs.data == 0) {
						ElMessage.error(rs.resultMsg);
						return;
					}
					onInit();
				});
			});
		};
		//详细信息
		const onDetail = (row: any) => {
			state.detailObj = {};

			onlineUserApi.Detail(row.id).then((rs) => {
				state.detailObj = rs.data;
				state.infoDrawer = true;
			});
		};
		//表格选择后回调事件
		const selectionChange = (selection: any) => {
			state.tableData.selection = selection;
		};
		//打印
		const onPrint = () => {
			print(printMain.value);
		};
		// pageSize 改变时触发
		const onSizechange = (val: number) => {
			state.tableData.param.pageSize = val;
			onInit();
		};
		// current-change 改变时触发
		const onCurrentChange = (val: number) => {
			state.tableData.param.pageIndex = val;
			onInit();
		};
		// 页面加载时
		onMounted(() => {
			onInit();
		});
		return {
			printMain,
			formatStrDate,
			onPrint,
			createOrEditRef,
			selectionChange,
			onInit,
			onAdd,
			onEdit,
			onDetail,
			onDelete,
			onDeleteByList,
			onSizechange,
			onCurrentChange,
			onSearch,
			...toRefs(state),
		};
	},
});
</script>
