export interface TableGenerateConfig {
    tableName: string;
    tableList: string;
    tableComponent: string;
    subTables: SubTableConfig[];
}

export interface SubTableConfig {
    tableName: string;
    tableList: string;
    tableComponent: string;
    relationType: string;
    foreignKey: string;
    parentKey: string;
    manyToMany?: {
        relationTable: string;
        sourceKey: string;
        targetKey: string;
    };
}

export interface DbConnectionConfig {
    dbType: string;
    host: string;
    port: number;
    database: string;
    username: string;
    password: string;
}

export interface TableInfo {
    name: string;
    schema: string;
    description: string;
}

export interface ColumnInfo {
    name: string;
    dataType: string;
    maxLength: number;
    isNullable: boolean;
    isIdentity: boolean;
    isPrimaryKey: boolean;
    description: string;
    columnId: number;
    defaultValue: string;
}