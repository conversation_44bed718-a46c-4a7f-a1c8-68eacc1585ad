<template>
	<div class="top-container">
		<el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="120px" class="mt35 mb35">
			<el-row :gutter="35">
				<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb20">
					<el-form-item label="Left Logo">
						<sc-upload v-model="ruleForm.logoInLeft" title="click upload" icon="ele-Picture"></sc-upload>
					</el-form-item>
					<el-form-item label="Width">
						<el-input-number v-model="ruleForm.logoInLeftWidth" clearable
							style="width: 120px"></el-input-number>
					</el-form-item>
					<el-form-item label="Height">
						<el-input-number v-model="ruleForm.logoInLeftHeight" clearable
							style="width: 120px"></el-input-number>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb20">
					<el-form-item label="Left Mini Logo">
						<sc-upload v-model="ruleForm.logoInLeftMini" title="click upload"
							icon="ele-Picture"></sc-upload>
					</el-form-item>
					<el-form-item label="Width">
						<el-input-number v-model="ruleForm.logoInLeftMiniWidth" clearable
							style="width: 120px"></el-input-number>
					</el-form-item>
					<el-form-item label="Height">
						<el-input-number v-model="ruleForm.logoInLeftMiniHeight" clearable
							style="width: 120px"></el-input-number>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb20">
					<el-form-item label="right Logo">
						<sc-upload v-model="ruleForm.logoInRigth" title="click upload" icon="ele-Picture"></sc-upload>
					</el-form-item>
					<el-form-item label="Width">
						<el-input-number v-model="ruleForm.logoInRigthWidth" clearable
							style="width: 120px"></el-input-number>
					</el-form-item>
					<el-form-item label="Height">
						<el-input-number v-model="ruleForm.logoInRigthHeight" clearable
							style="width: 120px"></el-input-number>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb20">
					<el-form-item label="Footer Logo">
						<sc-upload v-model="ruleForm.logoInFooter" title="click upload" icon="ele-Picture"></sc-upload>
					</el-form-item>
					<el-form-item label="Left">
						<el-input-number v-model="ruleForm.logoInFooterWidth" clearable
							style="width: 120px"></el-input-number>
					</el-form-item>
					<el-form-item label="Top">
						<el-input-number v-model="ruleForm.logoInFooterHeight" clearable
							style="width: 120px"></el-input-number>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb20">
					<el-form-item label="Login left Logo">
						<sc-upload v-model="ruleForm.logoOutLeft" title="click upload" icon="ele-Picture"></sc-upload>
					</el-form-item>
					<el-form-item label="Width">
						<el-input-number v-model="ruleForm.logoOutLeftWidth" clearable
							style="width: 120px"></el-input-number>
					</el-form-item>
					<el-form-item label="Height">
						<el-input-number v-model="ruleForm.logoOutLeftHeight" clearable
							style="width: 120px"></el-input-number>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb20">
					<el-form-item label="Login right Logo">
						<sc-upload v-model="ruleForm.logoOutRigth" title="click upload" icon="ele-Picture"></sc-upload>
					</el-form-item>
					<el-form-item label="Width">
						<el-input-number v-model="ruleForm.logoOutRigthWidth" clearable
							style="width: 120px"></el-input-number>
					</el-form-item>
					<el-form-item label="Height">
						<el-input-number v-model="ruleForm.logoOutRigthHeight" clearable
							style="width: 120px"></el-input-number>
					</el-form-item>
				</el-col>

				<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
					<el-form-item label="wwwroot" prop="name">
						<el-input v-model="ruleForm.wwwroot" clearable size="middle"></el-input>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
					<el-form-item label="Site name" prop="name">
						<el-input v-model="ruleForm.name" clearable size="middle"></el-input>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
					<el-form-item label="version" prop="version">
						<el-input v-model="ruleForm.version" clearable size="middle"></el-input>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
					<el-form-item label="company name" prop="company">
						<el-input v-model="ruleForm.company" clearable size="middle"></el-input>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
					<el-form-item label="Copyright">
						<el-input v-model="ruleForm.copyright" clearable size="middle"></el-input>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
					<el-form-item label="Address">
						<el-input v-model="ruleForm.address" clearable size="middle"></el-input>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
					<el-form-item label="Telephone">
						<el-input v-model="ruleForm.tel" clearable size="middle"></el-input>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
					<el-form-item label="Email">
						<el-input v-model="ruleForm.email" clearable size="middle"></el-input>
					</el-form-item>
				</el-col>

				<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
					<el-form-item label="Website">
						<el-input v-model="ruleForm.webUrl" clearable size="middle"></el-input>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
					<el-form-item label="Fax">
						<el-input v-model="ruleForm.fax" clearable size="middle"></el-input>
					</el-form-item>
				</el-col>

				<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
					<el-form-item label="System Name">
						<el-input v-model="ruleForm.systemName" clearable size="middle"></el-input>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
					<el-form-item label="Show Demo">
						<el-input v-model="ruleForm.demo" clearable size="middle"></el-input>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20" v-if="false">
					<el-form-item label="Keyword">
						<el-input v-model="ruleForm.keyword" type="textarea" maxlength="150" size="middle"> </el-input>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20" v-if="false">
					<el-form-item label="Describe">
						<el-input v-model="ruleForm.describe" type="textarea" maxlength="150" size="middle"> </el-input>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
					<el-form-item>
						<el-button type="primary" @click="onSave" size="small">Save</el-button>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
	</div>
</template>

<script lang="ts">
import { toRefs, reactive, onMounted, defineComponent, getCurrentInstance, watch } from 'vue';
import { ElMessage } from 'element-plus';
import configApi from '/@/api/config/index';
import { Siteinfo } from '/@/types/siteConfig';
export default defineComponent({
	name: 'configIndex',
	components: {},
	props: {
		basicObj: {
			type: Object,
			default: {},
		},
	},
	setup(props: any) {
		const { proxy } = getCurrentInstance() as any;
		const state = reactive({
			loading: false,
			ruleForm: {} as Siteinfo,
			rules: {
				name: [{ required: true, message: 'Please input', trigger: 'blur' }],
				version: [{ required: true, message: 'Please input', trigger: 'blur' }],
				company: [{ required: true, message: 'Please input', trigger: 'blur' }],
			},
		});

		const onSave = () => {
			proxy.$refs.ruleFormRef.validate((valid: boolean) => {
				if (!valid) {
					return;
				}
				var obj = { Info: state.ruleForm };
				state.loading = true;
				configApi
					.SaveConfig(obj)
					.then((rs) => {
						ElMessage.success('Saved Successfully');
					})
					.finally(() => {
						state.loading = false;
					});
			});
		};
		watch(
			() => props.basicObj,
			() => {
				state.ruleForm = props.basicObj;
			}
		);
		// 页面加载时
		onMounted(() => { });
		return {
			onSave,
			...toRefs(state),
		};
	},
});
</script>
