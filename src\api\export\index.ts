import BaseApi from '/@/api/BaseApi';
import request from '/@/utils/request';

class exportApi extends BaseApi {
    GetEntityColumns(data: any) {
        return request({
            url: this.baseurl + 'GetEntityColumns',
            method: 'post',
            data,
        });
    }
    Export(data?: any){
        return request({
            url: this.baseurl + 'Export',
            method: 'post',
            data,
            responseType: 'blob'
        });
    }



}

export default new exportApi('/api/export/','id');
