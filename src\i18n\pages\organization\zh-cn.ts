export default {
	CompanySearch: {
		//查询区域
	},
	organizationButtons: {
		//非按钮
		createCompany: '创建公司',
		editCompany: '编辑公司',
		importCompany: '导入公司',
		createDepartment: '创建部门',
		delete: '删除',
		refresh: '刷新',
		print: '打印',
	},
	organizationLabels: {
		Basicinformation:'基本信息',
		ContactInformation:'联系信息',
		Code:'编码',
		Name:'名称',
		Status: '状态',
		Description:'描述',
		Company:'公司',
		ParentCompany: '上级公司',
		Email: '邮箱',
		Phone: '手机号',
		StreetAddress: '街道地址',
		Currency:'货币',
	},
	organizationFields: {
		//table 列名
		Code:'编码',
		Name:'名称',
		Status:'状态',
		Description:'描述',
		Sort: '排序',
		ParentCompany: '上级公司',
		Created_By:'创建人',
		Created_At:'创建日期',
		Modified_By:'修改人',
		Modified_At:'修改日期',
		//联系列表
		ContactName:'联系人',
		CustomerTitle:'客户名称',
		EmailAddress:'邮箱地址',
		ContactTel:'联系电话',
		Currency:'货币',
	},
	organizationDlgTips: {
		deleteError: '已经存在配置关系，不允许删除!',
		deleteParentError: '已经存在配置子行，不允许删除!',
	},
};
