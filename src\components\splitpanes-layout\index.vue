<template>
    <div class="my-layout h100" :style="`position: ${state.isMobile ? 'relative' : 'absolute'}`">
      <Splitpanes :horizontal="state.isMobile" class="default-theme">
        <slot></slot>
      </Splitpanes>
    </div>
  </template>
  
  <script lang="ts" setup name="splitpaneslayout">
  import { reactive, onBeforeMount } from 'vue'
  import mittBus from '/@/utils/mitt'
  import { Splitpanes } from 'splitpanes'
  import 'splitpanes/dist/splitpanes.css'
  
  const state = reactive({
    isMobile: document.body.clientWidth < 1000,
  })
  
  // 页面加载前
  onBeforeMount(() => {
    // 监听窗口大小改变时(适配移动端)
    mittBus.on('layoutMobileResize', (res: LayoutMobileResize) => {
      // 判断是否是手机端
      state.isMobile = res.clientWidth < 1000
    })
  })
  </script>
  
  <style scoped lang="scss">
  .my-layout {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  overflow: hidden;
  padding: 0px 8px 8px 8px;
  display: flex;
  flex-direction: column;
}
.my-fill {
  display: flex;
  flex-direction: column;
  flex: 1;

  .el-card__body {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: auto;
  }

  .el-table {
    flex: 1;
  }
}
  :deep(.splitpanes.default-theme .splitpanes__splitter) {
    background-color: transparent;
    border-left-color: transparent;
  }
  :deep(.splitpanes.default-theme .splitpanes__pane) {
    background-color: transparent;
  }
  
  :deep(.splitpanes__pane) {
    transition: none;
  }
  </style>
  