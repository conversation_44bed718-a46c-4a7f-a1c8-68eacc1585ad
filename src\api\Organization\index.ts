import BaseApi from '/@/api/BaseApi';
import request from '/@/utils/request';

class organizationApi extends BaseApi {
  QueryV2(data: any) {
    return request({
      url: this.baseurl + 'QueryV2',
      method: 'post',
      data,
    });
  };
  
	DetailV2(key: any, key1: any) {
		return request({
			url: this.baseurl + 'Detail/' + key + '/' + key1,
			method: 'get',
			params: {},
		});
	};
  Tree(params?: any) {
		return request({
			url: this.baseurl + 'Tree',
			method: 'get',
			params,
		});
	};
}

export default new organizationApi('/api/Organization/', 'id');
