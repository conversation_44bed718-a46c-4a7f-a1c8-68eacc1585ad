<template>
    <div class="date-range-container">
        <DateSelector :style="{ width: width }" :dateType="dateType" :editable="editable" :clearable="clearable"
            v-model:input="startDateValue" v-model="startDateValue" @update:input="updateStartDate" />
        <el-text class="date-separator">{{ $t('message.page.to') }}</el-text>
        <DateSelector :style="{ width: width }" :dateType="dateType" :editable="editable" :clearable="clearable"
            v-model:input="endDateValue" v-model="endDateValue" @update:input="updateEndDate" />
    </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import DateSelector from '/@/components/common/DateSelector.vue';

const props = defineProps<{
    startDate: string | null;
    endDate: string | null;
    dateType?: string;
    editable?: boolean;
    clearable?: boolean;
    width?: string;
}>();

const emit = defineEmits(['update:startDate', 'update:endDate']);

const startDateValue = ref(props.startDate);
const endDateValue = ref(props.endDate);

// 为新的 props 设置默认值
const dateType = props.dateType || 'day';
const editable = props.editable ?? true;
const clearable = props.clearable ?? false;
const width = props.width || '115px';

watch(() => props.startDate, (newValue) => {
    startDateValue.value = newValue;
});

watch(() => props.endDate, (newValue) => {
    endDateValue.value = newValue;
});

const updateStartDate = (value: string | null) => {
    if (endDateValue.value && value && new Date(value) > new Date(endDateValue.value)) {
        ElMessage.warning('Start date cannot be later than end date');
        // 保留原来的日期
        startDateValue.value = props.startDate;
    } else {
        startDateValue.value = value;
        emit('update:startDate', value);
    }
};

const updateEndDate = (value: string | null) => {
    if (startDateValue.value && value && new Date(value) < new Date(startDateValue.value)) {
        ElMessage.warning('End date cannot be earlier than start date');
        // 保留原来的日期
        endDateValue.value = props.endDate;
    } else {
        endDateValue.value = value;
        emit('update:endDate', value);
    }
};
</script>

<style scoped>
.date-range-container {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
}

.date-separator {
    margin: 0 8px;
    white-space: nowrap;
}
</style>