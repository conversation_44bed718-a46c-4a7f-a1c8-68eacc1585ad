<template>
	<el-tree-select v-model="innerValue" :data="mappedData" :multiple="multiple" :render-after-expand="false"
		:filter-node-method="filterNodeMethod" :clearable="clearable" :filterable="filterable"
		:show-checkbox="showCheckbox" :check-on-click-node="checkOnClickNode" :collapse-tags="collapseTags"
		:placeholder="placeholder || 'Please select'" class="w-20" size="default" @change="onChange">
		<template v-if="enableCheckAll" #header>
			<el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">
				{{ checkAllLabel || 'All' }}
			</el-checkbox>
		</template>
	</el-tree-select>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';

// ===== 类型定义 =====
interface TreeSelectOptionRaw {
	[key: string]: any;
	children?: TreeSelectOptionRaw[];
}

interface TreeSelectOption {
	label: string;
	value: string | number;
	children?: TreeSelectOption[];
}

// ===== props 和默认值 =====
const props = withDefaults(
	defineProps<{
		modelValue: string[] | number[] | string | number | null;
		data: TreeSelectOptionRaw[];
		multiple?: boolean;
		showCheckbox?: boolean;
		clearable?: boolean;
		filterable?: boolean;
		collapseTags?: boolean;
		checkOnClickNode?: boolean;
		placeholder?: string;
		enableCheckAll?: boolean;
		checkAllLabel?: string;
		idField?: string;
		labelField?: string;
	}>(),
	{
		multiple: true,
		clearable: true,
		filterable: true,
		collapseTags: true,
		checkOnClickNode: true,
		showCheckbox: false,
		enableCheckAll: false,
		idField: 'value',
		labelField: 'label',
	}
);

// ===== emits =====
const emits = defineEmits(['update:modelValue', 'change']);

// ===== v-model 双向绑定 =====
const innerValue = ref(props.modelValue);
watch(
	() => props.modelValue,
	(val) => {
		innerValue.value = val;
	}
);
watch(innerValue, (val) => {
	emits('update:modelValue', val);
});

// ===== 根据 idField 和 labelField 映射数据 =====
const mappedData = computed<TreeSelectOption[]>(() => {
	const mapNode = (node: TreeSelectOptionRaw): TreeSelectOption => {
		const children = node.children?.map(mapNode);
		return {
			label: node[props.labelField!],
			value: node[props.idField!],
			children,
		};
	};
	return props.data.map(mapNode);
});

// ===== filter 方法 =====
const filterNodeMethod = (value: string, data: any) => {
	if (!value) return true;
	return data.label?.toLowerCase?.().includes(value.toLowerCase());
};

// ===== 全选逻辑 =====
const checkAll = ref(false);
const isIndeterminate = ref(false);

watch(innerValue, (val) => {
	if (!props.multiple || !props.showCheckbox || !props.enableCheckAll) {
		checkAll.value = false;
		isIndeterminate.value = false;
		return;
	}

	const selected = Array.isArray(val) ? val : [];
	const allLeafValues: any[] = [];

	const collectLeafValues = (nodes: TreeSelectOption[]) => {
		nodes.forEach((node) => {
			if (node.children?.length) {
				collectLeafValues(node.children);
			} else {
				allLeafValues.push(node.value);
			}
		});
	};

	collectLeafValues(mappedData.value);

	const checkedCount = selected.filter((v) => allLeafValues.includes(v)).length;
	checkAll.value = checkedCount === allLeafValues.length && checkedCount > 0;
	isIndeterminate.value = checkedCount > 0 && checkedCount < allLeafValues.length;
}, { immediate: true });

const handleCheckAllChange = (val: boolean) => {
	if (!props.multiple || !props.showCheckbox || !props.enableCheckAll) return;

	const allLeafValues: any[] = [];

	const collectLeafValues = (nodes: TreeSelectOption[]) => {
		nodes.forEach((node) => {
			if (node.children?.length) {
				collectLeafValues(node.children);
			} else {
				allLeafValues.push(node.value);
			}
		});
	};

	collectLeafValues(mappedData.value);

	innerValue.value = val ? allLeafValues : [];
	isIndeterminate.value = false;
	emits('change', innerValue.value);
};

const onChange = (val: any) => {
	emits('change', val);
};
</script>

<style scoped>
.tag-select-input {
	width: 100%;
}
</style>
