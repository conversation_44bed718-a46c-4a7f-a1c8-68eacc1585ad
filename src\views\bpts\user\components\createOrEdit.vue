﻿<template>
	<div class="system-user-edit-container">
		<el-drawer :title="title" v-model="isShowDialog" destroy-on-close draggable :close-on-click-modal="false" :close-on-press-escape="false" :size="600">
			<template #header>
				<h3>{{ title }}</h3>
			</template>
			<div class="drawer-content">
				<el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="140px">
					<el-row :gutter="24">
						<el-col :span="24" class="mb6">
							<el-form-item :label="$t('message.userFields.userName')" prop="userName">
								<el-input v-model="ruleForm.userName" size="middle" />
							</el-form-item>
						</el-col>
						<el-col :span="24" class="mb6">
							<el-form-item :label="$t('message.userFields.mobile')" prop="mobile">
								<el-input v-model="ruleForm.mobile" size="middle" />
							</el-form-item>
						</el-col>
						<el-col :span="24" class="mb6">
							<el-form-item :label="$t('message.userFields.firstName')" prop="firstName">
								<el-input v-model="ruleForm.firstName" size="middle" />
							</el-form-item>
						</el-col>
						<el-col :span="24" class="mb6">
							<el-form-item :label="$t('message.userFields.lastName')" prop="lastName">
								<el-input v-model="ruleForm.lastName" size="middle" />
							</el-form-item>
						</el-col>

						<el-col :span="24" class="mb6">
							<el-form-item :label="$t('message.userFields.email')" prop="email">
								<el-input v-model="ruleForm.email" size="middle" />
							</el-form-item>
						</el-col>

						<el-col :span="24" class="mb6">
							<el-form-item :label="$t('message.userFields.roleAssign')" prop="roleArr">
								<EnhancedSelect v-model="ruleForm.roleArr" :placeholder="$t('message.page.selectKeyPlaceholder')" clearable multiple filterable collapse-tags :disabled="disableAssignRole" size="middle">
									<el-option v-for="item in roleData" :key="item.id" :label="item.name" :value="item.id" />
								</EnhancedSelect>
							</el-form-item>
						</el-col>

						<el-col :span="24" class="mb6">
							<el-form-item :label="$t('message.userFields.customerAssign')" prop="accountIds">
								<el-tree-select
									ref="accountTreeRef"
									v-model="ruleForm.accountIds"
									:default-expanded-keys="ruleForm.accountIds"
									:data="accountTreeData"
									node-key="id"
									show-checkbox
									clearable
									multiple
									collapse-tags
									collapse-tags-tooltip
									filterable
									style="width: 85%"
									@click="loadAccountTree"
									:auto-expand-parent="true"
									@keyup.enter="handleKeydown"
									@check-change="handleCheckChange"
									size="middle">
									<template #header>
										<el-checkbox v-model="checkAll" :indeterminate="indeterminate" @change="selectAll"> All </el-checkbox>
									</template>
								</el-tree-select>
							</el-form-item>
						</el-col>

						<el-col :span="24" class="mb6">
							<el-form-item :label="$t('message.userFields.timeZone')" prop="timeZone">
								<el-select v-model="ruleForm.timeZone" :placeholder="$t('message.page.selectKeyPlaceholder')" clearable class="w100" size="middle"> <el-option v-for="item in timeZoneData" :label="item.itemName" :value="item.itemName" :key="item.itemId"></el-option></el-select>
							</el-form-item>
						</el-col>

						<el-col :span="24" class="mb6">
							<el-form-item :label="$t('message.userFields.status')" prop="status">
								<el-select v-model="ruleForm.status" :placeholder="$t('message.page.selectKeyPlaceholder')" clearable class="w100" size="middle">
									<el-option :label="$t('message.userFields.Active')" :value="0"></el-option>
									<el-option :label="$t('message.userFields.Inactive')" :value="1"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="24" class="mb6">
							<el-form-item :label="$t('message.userFields.jobTitle')" prop="jobTitle">
								<el-input v-model="ruleForm.jobTitle" size="middle" />
							</el-form-item>
						</el-col>

						<el-col :span="24" class="mb6">
							<el-form-item :label="$t('message.userFields.location')" prop="location">
								<el-input v-model="ruleForm.location" size="middle" />
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>

				<div class="drawer-footer mt10">
					<span class="dialog-footer">
						<el-button @click="onCancel" size="small">
							{{ $t('message.page.buttonClose') }}
						</el-button>
						<el-button v-if="dialogParams.action === 'Edit'" type="info" size="small" @click="onChangePwd">
							{{ $t('message.page.changePwd') }}
						</el-button>
						<el-button v-if="dialogParams.action === 'Create'" @click="onInitForm" size="small" type="danger">
							{{ $t('message.page.buttonReset') }}
						</el-button>
						<Auth :value="dialogParams.action === 'Edit' ? 'systemUser.Delete' : ''">
							<el-button v-if="dialogParams.action === 'Edit'" @click="onDelete" type="danger" size="small">
								{{ $t('message.page.buttonDelete') }}
							</el-button>
						</Auth>
						<Auth :value="dialogParams.action === 'Edit' ? 'systemUser.UnLock' : ''">
							<el-button v-if="dialogParams.action === 'Edit' && ruleForm.hasLock" @click="onUnLock" type="warning" size="small">
								{{ $t('message.limits.UnLock') }}
							</el-button>
						</Auth>
						<el-button :loading="saveLoading" type="primary" @click="onSubmit" size="small">{{ $t('message.page.buttonSave') }}</el-button>
					</span>
				</div>
			</div>
		</el-drawer>

		<el-drawer v-model="changePwdRef" :title="$t('message.page.changePwd')" :size="600" :close-on-click-modal="false" :close-on-press-escape="false" destroy-on-close draggable @close="onPwdCancel(changePwdFormRef)">
			<!-- 使用 v-slot 自定义标题区域 -->
			<template v-slot:title>
				<div class="menu-item">
					<el-icon :size="18" @click="onReturn(changePwdFormRef)" class="icon-item"><ele-Back /></el-icon>
					<h3>{{ $t('message.page.changePwd') }}</h3>
				</div>
			</template>
			<div class="drawer-content">
				<el-form ref="changePwdFormRef" :rules="changeRules" :model="form" label-position="left" label-width="140px" style="height: 100px">
					<el-row>
						<el-col>
							<el-form-item :label="$t('message.page.resetPwd')" prop="pwd">
								<el-input v-model="form.pwd" :type="isShowPwd ? 'text' : 'password'" :placeholder="$t('message.page.searchKeyPlaceholder')" autocomplete="off" style="width: 500px">
									<template #suffix>
										<el-icon @click="isShowPwd = !isShowPwd">
											<template v-if="isShowPwd">
												<img src="/img/show.svg" />
											</template>
											<template v-else>
												<img src="/img/hide.svg" />
											</template>
										</el-icon>
									</template>
								</el-input>
							</el-form-item>
						</el-col>
						<el-col style="margin-top: 20px">
							<el-form-item :label="$t('message.page.confirmPwd')" prop="confirmPwd">
								<el-input v-model="form.confirmPwd" :type="isShowConfirmPwd ? 'text' : 'password'" :placeholder="$t('message.page.searchKeyPlaceholder')" style="width: 500px">
									<template #suffix>
										<el-icon @click="isShowConfirmPwd = !isShowConfirmPwd">
											<template v-if="isShowConfirmPwd">
												<img src="/img/show.svg" />
											</template>
											<template v-else>
												<img src="/img/hide.svg" />
											</template>
										</el-icon>
									</template>
								</el-input>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>

				<div style="color: red; size: 12; margin-top: 20px">
					<p>Must be a minimum of 8 characters in length and contain a combination of at least two of the following:</p>
					<p>1: A mix of uppercase and lowercase letters</p>
					<p>2: Special character (~ ! @ # $ % ^ & * ( ) _ +)</p>
					<p>3: Number (numeric value)</p>
				</div>

				<div class="drawer-footer">
					<span class="dialog-footer">
						<el-button @click="onReturn(changePwdFormRef)" size="small">{{ $t('message.page.buttonClose') }}</el-button>
						<el-button type="primary" @click="onSubmitChangePwd" size="small">
							{{ $t('message.page.changePwd') }}
						</el-button>
					</span>
				</div>
			</div>
		</el-drawer>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, getCurrentInstance, defineComponent, ref, watch, computed, nextTick } from 'vue';
import { ElMessageBox, ElMessage, FormInstance } from 'element-plus';
import EnhancedSelect from '/@/components/select/EnhancedSelect.vue';

import userApi from '/@/api/user';
import roleApi from '/@/api/role';
import accountApi from '/@/api/WWE_Account';

import dictItemApi from '/@/api/dictItem/index';
import { checkEmail } from '/@/utils/toolsValidate';
import { useI18n } from 'vue-i18n';
import MyDate from '/@/components/ticket/ticketDate.vue';
import { isNotEmptyOrNull } from '/@/utils';
import Auth from '/@/components/auth/auth.vue';
import { Roles } from '/@/constants';
import { AccountNode } from '/@/models/Account';

interface DialogParams {
	action: string;
	id: number;
}

export default defineComponent({
	name: 'userCreateOrEdit',
	components: { MyDate, Auth, EnhancedSelect },
	setup(props, context) {
		const { t } = useI18n();
		const { proxy } = getCurrentInstance() as any;
		const userRef = ref();
		const accountTreeRef = ref();
		const changePwdRef = ref(false);

		const state = reactive({
			title: 'New User',
			isShowPwd: false,
			isShowConfirmPwd: false,
			isShowDialog: false,
			isShowPrivateTicket: false,
			saveLoading: false,
			deleteLoading: false,
			isBlockCloseDialog: false,
			checkAll: false,
			indeterminate: false,
			roleData: [] as any,
			timeZoneData: [] as any,
			accountTreeData: [] as any,
			dialogParams: {
				action: '',
				id: -1,
			},
			ruleForm: {
				id: 0,
				mobile: '',
				email: '',
				userName: '',
				firstName: '',
				lastName: '',
				status: 0,
				timeZone: '',
				jobTitle: '',
				hasLock: false,
				accountIds: [],
				roleArr: [],
			} as any,
			rules: {
				userName: [{ required: true, message: 'Please input', trigger: 'blur' }],
				firstName: [{ required: true, message: 'Please input', trigger: 'blur' }],
				lastName: [{ required: true, message: 'Please input', trigger: 'blur' }],
				mobile: [
					{ required: false, message: 'Please input', trigger: 'blur' },
					{
						validator: (rule: any, value: any, callback: any) => {
							if (/^(?:[0-9]\d*)$/.test(value) == false && isNotEmptyOrNull(value)) {
								callback(new Error('Phone must be number'));
							} else {
								callback();
							}
						},
						trigger: 'blur',
					},
				],
				email: [
					{ required: true, message: 'Please input', trigger: 'blur' },
					{
						validator: checkEmail,
						min: 9,
						max: 18,
						message: t('message.formats.email'),
						trigger: 'blur',
					},
				],
				roleArr: [{ required: true, message: 'Please input', trigger: 'change' }],
				accountIds: [{ required: true, message: 'Please select', trigger: 'change' }],
			},
		});

		const changeRules = {
			pwd: [
				{ required: true, message: t('message.validates.required'), trigger: 'blur' },
				{
					pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*(\d|[~!@#$%^&*()_+,]))[A-Za-z\d~!@#$%^&*()_+,]{7,20}$|^(?=.*\d)(?=.*[~!@#$%^&*()_+,])[A-Za-z\d~!@#$%^&*()_+,]{7,20}$/,
					message: 'Password is Invalid.',
					trigger: ['blur'],
				},
			],
			confirmPwd: [
				{ required: true, message: t('message.validates.required'), trigger: 'blur' },
				{
					pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*(\d|[~!@#$%^&*()_+,]))[A-Za-z\d~!@#$%^&*()_+,]{7,20}$|^(?=.*\d)(?=.*[~!@#$%^&*()_+,])[A-Za-z\d~!@#$%^&*()_+,]{7,20}$/,
					message: 'Password is Invalid.',
					trigger: ['blur'],
				},
			],
		};

		const form = reactive({
			userId: '',
			pwd: '',
			confirmPwd: '',
		});

		const changePwdFormRef = ref<FormInstance>();

		const openDialog = async (parmas: DialogParams) => {
			state.dialogParams = parmas;

			onInitForm();

			await loadAccountTree();

			if (parmas.action == 'Create') {
				state.title = 'New User';

				roleApi.GetList().then((rs) => {
					state.roleData = rs.data.filter((item: any) => item.id !== Roles.SuperAdmin.id);
				});

				var dictArr = ['Time Zone'];
				dictItemApi.Many(dictArr).then((rs: any) => {
					state.timeZoneData = rs.data?.find((a: any) => {
						return a.dictValue === 'Time Zone';
					})?.items;
				});
			} else if (parmas.action == 'Edit') {
				state.title = 'Edit User';
				getData(parmas.id);
			} else {
				ElMessage.error('Parameter action cannot be empty.');
			}
		};

		const closeDialog = () => {
			accountTreeRef.value.setCheckedKeys([]);

			onInitForm();
			state.isShowDialog = false;
		};

		const onCancel = () => {
			closeDialog();
		};

		const onSubmit = () => {
			proxy.$refs.ruleFormRef.validate((valid: any) => {
				if (!valid) {
					return;
				}

				var obj = {
					...Object.assign({}, state.ruleForm), // 使用Object.assign来创建state.ruleForm的浅拷贝
					roles: state.ruleForm.roleArr,
				};

				delete obj.checkRemitToProjectData;

				state.saveLoading = true;

				userApi
					.Save(obj)
					.then(() => {
						ElMessage.success('Succeed');
						context.emit('fetchData');
						closeDialog();
					})
					.catch((error: HandledError) => {
						if (!error.isHandled) {
							const errorCode = error.code;
							const errorMessage = error.message;
							proxy.$message(errorMessage, 'error');
						}
					})
					.finally(() => {
						state.saveLoading = false;
					});
			});
		};

		const getData = (id: any) => {
			userApi.Detail(id).then(async (rs) => {
				state.ruleForm = Object.assign({}, rs.data);
				await roleApi.GetList().then((rs) => {
					state.roleData = rs.data;

					state.ruleForm.roleArr = [];

					if (state.ruleForm.id == Roles.SuperAdmin.id) {
						state.roleData = state.roleData.filter((item: any) => item.id == Roles.SuperAdmin.id);
					} else {
						state.roleData = state.roleData.filter((item: any) => item.id != Roles.SuperAdmin.id);
					}

					state.ruleForm.roleArr = state.ruleForm.roles;
				});

				var dictArr = ['Time Zone'];
				dictItemApi.Many(dictArr).then((rs: any) => {
					state.timeZoneData = rs.data?.find((a: any) => {
						return a.dictValue === 'Time Zone';
					})?.items;
				});
			});
		};

		const onDelete = () => {
			ElMessageBox.confirm(t('message.page.dlgDeleteText'), t('message.page.dlgTip'), {
				confirmButtonText: t('message.page.confirm'),
				cancelButtonText: t('message.page.cancel'),
				type: 'warning',
			}).then(() => {
				state.deleteLoading = true;
				userApi
					.DeleteByKey(state.ruleForm.id)
					.then(() => {
						ElMessage.success('Succeed');
						context.emit('fetchData');
						closeDialog();
					})
					.catch((error: HandledError) => {
						if (!error.isHandled) {
							const errorCode = error.code;
							const errorMessage = error.message;
							ElMessage.error(errorMessage);
						}
					})
					.finally(() => {
						state.deleteLoading = false;
					});
			});
		};

		const onUnLock = () => {
			userApi
				.UnLock(state.ruleForm.id)
				.then((rs: any) => {
					ElMessage.success('Succeed');
					context.emit('fetchData');
					closeDialog();
				})
				.catch((error: HandledError) => {
					if (!error.isHandled) {
						const errorCode = error.code;
						const errorMessage = error.message;
						ElMessage.error(errorMessage);
					}
				})
				.finally(() => {
					state.deleteLoading = false;
				});
		};

		const onInitForm = () => {
			state.isShowDialog = true;

			if (proxy.$refs.ruleFormRef) {
				proxy.$refs.ruleFormRef.resetFields();
			}

			state.ruleForm = {
				id: 0,
				mobile: '',
				email: '',
				userName: '',
				firstName: '',
				lastName: '',
				status: 0,
				timeZone: '',
				jobTitle: '',
				hasLock: false,
				accountIds: [],
				roleArr: [],
			};

			state.roleData = [];
			state.ruleForm.roleArr = [];
		};

		const selectAll = async (isChecked: boolean) => {
			state.indeterminate = false;
			if (state.accountTreeData.length === 0) {
				await loadAccountTree();
			}
			//console.log('selectAll', isChecked, state.accountTreeData);
			if (isChecked) {
				state.ruleForm.accountIds = getAllLeafNodeIds(state.accountTreeData);
			} else {
				state.ruleForm.accountIds = [];
			}
			nextTick(() => {
				if (accountTreeRef.value) {
					accountTreeRef.value.setCheckedKeys(state.ruleForm.accountIds);
				}
			});
		};

		const getAllLeafNodeIds = (nodes: AccountNode[]): string[] => {
			return nodes.flatMap((node) => {
				if (!node.children || node.children.length === 0) {
					return [node.id];
				}
				return getAllLeafNodeIds(node.children);
			});
		};

		// 在树选择变化时更新状态
		const handleCheckChange = () => {
			updateCheckedStatus();
		};

		// 计算当前选中状态
		const updateCheckedStatus = () => {
			if (!accountTreeRef.value) return;

			const checkedKeys = accountTreeRef.value.getCheckedKeys();
			const allLeafIds = getAllLeafNodeIds(state.accountTreeData);

			// 更新全选状态
			state.checkAll = checkedKeys.length > 0 && checkedKeys.length === allLeafIds.length;

			// 更新半选状态
			state.indeterminate = checkedKeys.length > 0 && checkedKeys.length < allLeafIds.length;
		};

		const onVisible = () => {
			userRef.value.blur();
		};

		const getTreeName = (treeList: any, id: any) => {
			for (let i = 0; i < treeList.length; i++) {
				let treeItem = treeList[i];
				if (treeItem.orgId === id) {
					return treeItem;
				} else {
					if (treeItem.children && treeItem.children.length > 0) {
						if (getTreeName(treeItem.children, id)) {
							return getTreeName(treeItem.children, id);
						}
					}
				}
			}
		};

		const findParent = (nodes: any, nodeId: any) => {
			return nodes.find((node: { nodeId: any }) => node.nodeId === nodeId);
		};

		const findItemInTree = (tree: any[], nodeIdToFind: string): any | null => {
			for (const item of tree) {
				if (item.nodeId === nodeIdToFind) {
					return item;
				}

				if (item.children && item.children.length > 0) {
					const foundChild = findItemInTree(item.children, nodeIdToFind);
					if (foundChild) {
						return foundChild;
					}
				}
			}

			return null;
		};

		const disableAssignRole = computed(() => {
			let disableResult = false;
			if (state.ruleForm.id == Roles.SuperAdmin.id) {
				disableResult = true;
			}
			return disableResult;
		});

		const onChangePwd = () => {
			form.userId = '';
			form.pwd = '';
			form.confirmPwd = '';

			form.userId = state.ruleForm.id;
			changePwdRef.value = true;
			state.isBlockCloseDialog = false;
		};

		const onSubmitChangePwd = () => {
			if (form.pwd != form.confirmPwd) {
				ElMessage.error(t('message.page.pwdMatch'));
				return;
			}

			proxy.$refs.changePwdFormRef.validate((valid: any) => {
				if (!valid) {
					return;
				}

				var obj = { userId: form.userId, pwd: form.confirmPwd };
				state.saveLoading = true;
				userApi
					.UserResetPassword(obj)
					.then(() => {
						ElMessage.success('Succeed');
						state.isBlockCloseDialog = true;
						closePwdDialog();
					})
					.catch((error: HandledError) => {
						if (!error.isHandled) {
							const errorCode = error.code;
							const errorMessage = error.message;
							ElMessage.error(errorMessage);
						}
					})
					.finally(() => {
						state.saveLoading = false;
					});
			});
		};

		const closePwdDialog = () => {
			changePwdRef.value = false;
			if (!state.isBlockCloseDialog) {
				state.isShowDialog = false;
			}
		};

		const onPwdCancel = (formEl: any) => {
			formEl.resetFields();
			closePwdDialog();
		};

		const onReturn = (formEl: any) => {
			formEl.resetFields();
			state.isBlockCloseDialog = true;
			closePwdDialog();
		};

		const loadAccountTree = async () => {
			const response = await accountApi.GetAccountTree();
			if (response.data && response.data.length > 0) {
				state.accountTreeData = response.data;
			}
		};

		const handleKeydown = () => {
			state.ruleForm.checkRemitToProjectData = [];
		};

		return {
			loadAccountTree,
			openDialog,
			onVisible,
			userRef,
			closeDialog,
			onCancel,
			onSubmit,
			onDelete,
			onInitForm,
			...toRefs(state),
			disableAssignRole,
			accountTreeRef,
			onChangePwd,
			onSubmitChangePwd,
			onPwdCancel,
			onReturn,
			changePwdRef,
			form,
			changePwdFormRef,
			changeRules,
			onUnLock,
			selectAll,
			handleKeydown,
			handleCheckChange,
		};
	},
});
</script>

<style scoped lang="scss">
.el-icon img {
	height: 1em;
	width: 1em;
	cursor: pointer;
}

.drawer-content {
	padding: 20px;
}

.drawer-footer {
	text-align: right;
}

.menu-item {
	display: flex;
	align-items: center;
}

.icon-item {
	cursor: pointer;
	margin-right: 10px;
	/* 调整图标与文本间距 */
	transition: color 0.3s;
	/* 颜色变化过渡时间 */

	/* 鼠标悬停时改变颜色 */
	&:hover {
		color: #729eff;
	}
}
</style>
