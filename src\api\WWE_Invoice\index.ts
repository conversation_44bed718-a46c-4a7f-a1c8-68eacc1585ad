import BaseApi from '/@/api/BaseApi';
import request from '/@/utils/request';

class wWE_InvoiceApi extends BaseApi {
	QueryV2(data: any) {
		return request({
			url: this.baseurl + 'QueryV2',
			method: 'post',
			data,
		});
	}

	MassUpdateMethod(data: any) {
		return request({
			url: this.baseurl + 'MassUpdateMethod',
			method: 'post',
			data,
		});
	}

	MassUpdateStatus(data: any) {
		return request({
			url: this.baseurl + 'MassUpdateStatus',
			method: 'post',
			data,
		});
	}

	GenerateFileBatch(data: any) {
		return request({
			url: this.baseurl + 'GenerateFileBatch',
			method: 'post',
			data,
		});
	}

	CombineFile(data: any) {
		return request({
			url: this.baseurl + 'CombineFile',
			method: 'post',
			data,
		});
	}

	CombineShipmentFile(data: any) {
		return request({
			url: this.baseurl + 'CombineShipmentFile',
			method: 'post',
			data,
		});
	}

	GetCarrierSummary(data: any) {
		return request({
			url: this.baseurl + 'GetCarrierSummary',
			method: 'post',
			data,
		});
	}

	GetCombineSummary(data: any) {
		return request({
			url: this.baseurl + 'GetCombineSummary',
			method: 'post',
			data,
		});
	}

	ReplaceImage(data: any) {
		return request({
			url: this.baseurl + 'ReplaceImage',
			method: 'post',
			data,
		});
	}

	CheckAssociatedData(data: any) {
		return request({
			url: this.baseurl + 'CheckAssociatedData',
			method: 'post',
			data,
		});
	}

	DeleteImage(data: any) {
		return request({
			url: this.baseurl + 'DeleteImage',
			method: 'post',
			data,
		});
	}

	GetProList(params?: any) {
		return request({
			url: this.baseurl + 'GetProList',
			method: 'get',
			params,
		});
	}

	GetShipmentEmail(data: any) {
		return request({
			url: this.baseurl + 'GetShipmentEmail',
			method: 'post',
			data,
		});
	}

	SendShipmentEmail(data: any) {
		return request({
			url: this.baseurl + 'SendShipmentEmail',
			method: 'post',
			data,
		});
	}

	GetFTPUploadSummary(data: any) {
		return request({
			url: this.baseurl + 'GetFTPUploadSummary',
			method: 'post',
			data,
		});
	}

	
	PrepareFTPUpload(data: any) {
		return request({
			url: this.baseurl + 'PrepareFTPUpload',
			method: 'post',
			data,
		});
	}

	Export(data?: any){
		data.selectedColumns = data.colNames;
		return request({
			url: this.baseurl + 'Export',
			method: 'post',
			data,
			responseType: 'blob'
		});
	}
	GetTableColumns(params: any) {
		return request({
			url: this.baseurl + 'GetTableColumns',
			method: 'get',
			params
		});
	}
}

export default new wWE_InvoiceApi('/api/WWE_Invoice/', 'invoiceId');
