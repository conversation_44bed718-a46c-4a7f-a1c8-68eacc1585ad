
export default {
    wWE_Invoice_HistoriesSearch: {
        //Search Area
    },
    wWE_Invoice_HistoriesButtons: {
        createWWE_Invoice_Histories: 'New W W E_ Invoice_ Histories',
        editWWE_Invoice_Histories: 'Edit W W E_ Invoice_ Histories',
        importWWE_Invoice_Histories: 'Import W W E_ Invoice_ Histories',
    },
    wWE_Invoice_HistoriesLabels: {
		Id: 'Id',
		InvoiceId: 'Invoice Id',
		Action: 'Action',
		FromStatus: 'From Status',
		ToStatus: 'To Status',
		ModifiedById: 'Modified By Id',
		CreatedAt: 'Created At'
    },
    wWE_Invoice_HistoriesFields: {
		Id: 'Id',
		InvoiceId: 'Invoice Id',
		Action: 'Action',
		FromStatus: 'From Status',
		ToStatus: 'To Status',
		ModifiedById: 'Modified By Id',
		CreatedAt: 'Created At'
    },
    wWE_Invoice_HistoriesDlgTips: {
        deleteError: 'There is already a configuration relationship, deletion is not allowed!',
    },
    wWE_Invoice_HistoriesCommon: {
        ImportData: 'Import Data',
		UploadExcelFile: 'Upload Excel File',
        UploadCsvFile: 'Upload Csv File',
		Note: '(Note: This action will overwrite the data of the selected month)'
	},
};
            