
export default {
    wWE_InvoiceSearch: {
        //Search Area
    },
    wWE_InvoiceButtons: {
        createWWE_Invoice: 'New Invoice',
        editWWE_Invoice: 'Edit',
        importWWE_Invoice: 'Import',
		MassMethodUpdate:'Mass Method Update',
		MassStatusUpdate:'Mass Status Update',
		SendEmail:'Send Email',
		ExportEmail:'Export Email',
		UploadToFTP:'Upload to FTP',
    },
    wWE_InvoiceLabels: {
		InvoiceId: 'Invoice Id',
		AccountId: 'Account Id',
		AccountName: 'Project',
		CarrierId: 'Carrier Id',
		CarrierName: 'Carrier',
		ImageId: 'Image Id',
		InvoiceNumber: 'Invoice#',
		ProNumber: 'Pro#',
		Amount: 'Amount',
		DocRetrievalMethod: 'Retrieval Method',
		DocRetrievedStatus: 'Retrieval Status',
		DocRetrievalType: 'Retrieval Type',
		Status: 'Status',
		BOL: 'BOL',
		POD: 'POD',
		Others: 'Others',
		CombineFile: 'Combine File',
		CarrierContact: 'Carrier Contact',
		CarrierContactEmail: 'Carrier Contact Email',
		CarrierContactPhone: 'Carrier Contact Phone',
		AgingDays: 'Aging Days',
		BatchNumber: 'File Batch',
		Source: 'Source',
		Comment: 'Comment',
		CreatedById: 'Created By Id',
		CreatedByName: 'Entered by',
		CreatedAt: 'Entered Date',
		ModifiedById: 'Modified By Id',
		ModifiedByName: 'Last Modified By',
		ModifiedAt: 'Last Modified Date',
		IsDeleted: 'Is Deleted'

    },
    wWE_InvoiceFields: {

		InvoiceId: 'Invoice Id',
		AccountId: 'Account Id',
		AccountName: 'Project',
		CarrierId: 'Carrier Id',
		CarrierName: 'Carrier',
		ImageId: 'Image Id',
		InvoiceNumber: 'Invoice#',
		ProNumber: 'Pro#',
		Amount: 'Amount',
		DocRetrievalMethod: 'Retrieval Method',
		DocRetrievedStatus: 'Retrieval Status',
		DocRetrievalType: 'Retrieval Type',
		Status: 'Status',
		BOL: 'BOL',
		POD: 'POD',
		Others: 'Others',
		CombineFile: 'Combine File',
		CarrierContact: 'Carrier Contact',
		CarrierContactEmail: 'Carrier Contact Email',
		CarrierContactPhone: 'Carrier Contact Phone',
		AgingDays: 'Aging Days',
		BatchNumber: 'File Batch',
		Source: 'Source',
		Comment: 'Comment',
		CreatedById: 'Created By Id',
		CreatedByName: 'Entered by',
		CreatedAt: 'Entered Date',
		ModifiedById: 'Modified By Id',
		ModifiedByName: 'Last Modified By',
		ModifiedAt: 'Last Modified Date',
		IsDeleted: 'Is Deleted',
		Aging:'Aging (Day)',
		UploadPDF:'Upload PDF',
		EmailTimestamp:'Email Timestamp',
    },
    wWE_InvoiceDlgTips: {
        deleteError: 'There is already a configuration relationship, deletion is not allowed!',
    },
    wWE_InvoiceCommon: {
        ImportData: 'Import Data',
		UploadExcelFile: 'Upload Excel File',
        UploadCsvFile: 'Upload Csv File',
		Note: '(Note: This action will overwrite the data of the selected month)'
	},
};
            