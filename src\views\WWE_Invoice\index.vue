<template>
	<div class="top-container">
		<div class="list-search">
			<el-card class="list-search-card" shadow="never">
				<el-form label-width="140px" @keyup.enter="onSearch">
					<el-row :gutter="24">
						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="5" class="mb1 mt1">
							<el-form-item :label="$t('message.wWE_InvoiceLabels.AccountName')" prop="accountId">
								<EnhancedTreeSelect v-model="state.formTable.params.accountId" :data="accountOptions"
									id-field="key" show-checkbox enable-check-all />
							</el-form-item>
						</el-col>

						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="5" class="mb1 mt1">
							<el-form-item :label="$t('message.wWE_InvoiceLabels.CarrierName')" prop="carrierId">
								<EnhancedTreeSelect v-model="state.formTable.params.carrierId" :data="carrierOptions"
									id-field="key" show-checkbox enable-check-all />
							</el-form-item>
						</el-col>

						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="5" class="mb1 mt1">
							<el-form-item :label="$t('message.wWE_InvoiceLabels.InvoiceNumber')" prop="invoiceNumber">
								<el-input v-model="state.formTable.params.invoiceNumber" clearable
									:placeholder="$t('message.page.searchKeyPlaceholder')" class="w-20"
									size="default"></el-input>
							</el-form-item>
						</el-col>

						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="5" class="mb1 mt1">
							<el-form-item :label="$t('message.wWE_InvoiceLabels.ProNumber')" prop="proNumber">
								<el-input v-model="state.formTable.params.proNumber" clearable
									:placeholder="$t('message.page.searchKeyPlaceholder')" class="w-20"
									size="default"></el-input>
							</el-form-item>
						</el-col>

						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="5" class="mb1 mt1">
							<el-form-item :label="$t('message.wWE_InvoiceLabels.DocRetrievalMethod')"
								prop="docRetrievalMethod">
								<EnhancedTreeSelect v-model="state.formTable.params.docRetrievalMethod"
									:data="retrievalMethodOptions" id-field="itemValue" label-field="itemName"
									show-checkbox enable-check-all />
							</el-form-item>
						</el-col>

						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="5" class="mb1 mt1">
							<el-form-item :label="$t('message.wWE_InvoiceLabels.DocRetrievedStatus')"
								prop="docRetrievedStatus">
								<EnhancedTreeSelect v-model="state.formTable.params.docRetrievedStatus"
									:data="retrievedStatusOptions" id-field="itemValue" label-field="itemName"
									show-checkbox enable-check-all />
							</el-form-item>
						</el-col>

						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="5" class="mb1 mt1">
							<el-form-item :label="$t('message.wWE_InvoiceLabels.Status')" prop="status">
								<EnhancedTreeSelect v-model="state.formTable.params.status" :data="invoiceStatusOptions"
									id-field="itemValue" label-field="itemName" show-checkbox enable-check-all />
							</el-form-item>
						</el-col>

						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="5" class="mb1 mt1">
							<el-form-item :label="$t('message.wWE_InvoiceLabels.CreatedAt')" prop="createdAt">
								<DateRangeSelector v-model:startDate="state.formTable.params.createdAtStart"
									v-model:endDate="state.formTable.params.createdAtEnd" dateType="day"
									:editable="true" :clearable="false" />
							</el-form-item>
						</el-col>

						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="5" class="mb1 mt1">
							<el-form-item class="nowidth" label=" ">
								<div class="searchBtn">
									<el-button type="primary" icon="search" @click="onSearch">
										{{ $t('message.page.buttonSearch') }}
									</el-button>
									<el-button type="danger" icon="refresh-left" @click="onClear">
										{{ $t('message.page.buttonReset') }}
									</el-button>
								</div>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>
			</el-card>
		</div>

		<div class="list-table">
			<el-card shadow="never" class="table-card">
				<el-header class="table_header">
					<div class="left-panel">
						<el-button type="primary" icon="plus" @click="onAdd" v-auth="'WWE_Invoice.Create'">
							{{ $t('message.wWE_InvoiceButtons.createWWE_Invoice') }}
						</el-button>
					</div>

					<div class="right-panel" v-auth="'WWE_Invoice.Export'">
						<el-button type="primary" icon="Edit" @click="onMassMethodUpdate" :disabled="!hasSelection"
							v-auth="'WWE_Invoice.MassMethodUpdate'">
							{{ $t('message.wWE_InvoiceButtons.MassMethodUpdate') }}
						</el-button>
						<Export ref="exportRef" :table-type="0" />
					</div>
				</el-header>

				<div class="scTable">
					<div class="scTable-table" :style="{ '--fixed-header-top': fixedHeaderTop + 'px' }">
						<vxe-table show-overflow border="full" :min-height="25" ref="tableRef"
							:header-cell-config="{ height: 40 }" :row-config="{ isHover: true, height: 33 }"
							:column-config="{ resizable: true }" :checkbox-config="{ highlight: true }"
							:loading="state.formTable.loading" :loading-config="{ text: $t('message.page.loading') }"
							:tree-config="{ transform: true }" :scroll-y="{ enabled: false, gt: 0, mode: 'wheel' }"
							:data="state.formTable.data" :header-cell-style="headerCellStyle" :sortConfig="sortConfig"
							:row-style="rowStyle" @sort-change="onSortChange" @cell-click="cellClick">
							<template #empty>
								<el-empty :description="$t('message.page.emptyDescription')"
									:image-size="100"></el-empty>
							</template>

							<vxe-column type="checkbox" width="50" height="50" fixed="left"></vxe-column>
							<vxe-column title="备注" fixed="left" min-width="60">
								<template #default="{ row }">
									<el-badge :value="row.commentCount" :show-zero="false" class="comment-count"
										:hidden="row.ticketContentsTotal == 0"
										@click="openCommentDrawer(row.invoiceId)">
										<vxe-button mode="text" status="primary" icon="vxe-icon-comment"></vxe-button>
									</el-badge>
								</template>
							</vxe-column>

							<vxe-column :title="$t('message.wWE_InvoiceFields.InvoiceNumber')" field="invoiceNumber"
								width="120" sortable fixed="left">
								<template #default="scope">
									<el-link @click="onEdit(scope.row)"
										:type="scope.row.status === 'Exception' ? 'danger' : 'primary'"
										:style="{ color: scope.row.status === 'Exception' ? '#F56C6C' : '' }">
										{{ scope.row.invoiceNumber || 'N/A' }}
									</el-link>
								</template>
							</vxe-column>
							<vxe-column :title="$t('message.wWE_InvoiceFields.AccountName')" field="accountName"
								width="120" sortable></vxe-column>
							<vxe-column :title="$t('message.wWE_InvoiceFields.CarrierName')" field="carrierName"
								width="120" sortable></vxe-column>

							<vxe-column :title="$t('message.wWE_InvoiceFields.ProNumber')" field="proNumber" width="120"
								sortable></vxe-column>
							<vxe-column :title="$t('message.wWE_InvoiceFields.Amount')" width="120" sortable>
								<template #default="scope">
									{{ formatCurrency(scope.row.amount, {
										useParenthesesForNegative: true, source:
											scope.row.source
									}) }}
								</template>
							</vxe-column>
							<vxe-column :title="$t('message.wWE_InvoiceFields.Status')" field="status" width="120"
								sortable></vxe-column>
							<vxe-column :title="$t('message.wWE_InvoiceFields.DocRetrievalMethod')"
								field="docRetrievalMethod" width="140" sortable></vxe-column>

							<vxe-column :title="$t('message.wWE_InvoiceFields.DocRetrievalType')"
								field="docRetrievalType" width="140" sortable></vxe-column>

							<vxe-column :title="$t('message.wWE_InvoiceFields.BatchNumber')" field="batchNumber"
								width="120" sortable></vxe-column>

							<vxe-column :title="$t('message.wWE_InvoiceFields.Source')" field="source" width="120"
								sortable></vxe-column>
							<vxe-column :title="$t('message.wWE_InvoiceFields.CreatedByName')" field="createdByName"
								width="120" sortable></vxe-column>
							<vxe-column :title="$t('message.wWE_InvoiceFields.CreatedAt')" field="createdAt" width="180"
								sortable></vxe-column>
							<vxe-column :title="$t('message.wWE_InvoiceFields.ModifiedByName')" field="modifiedByName"
								width="180" sortable></vxe-column>
							<vxe-column :title="$t('message.wWE_InvoiceFields.ModifiedAt')" field="modifiedAt"
								width="180" sortable></vxe-column>

							<vxe-column fixed="right" :title="$t('message.page.actions')" width="90">
								<template #default="scope">
									<el-tooltip class="box-item" effect="dark" :content="$t('message.limits.Edit')"
										placement="bottom">
										<vxe-button mode="text" status="primary" icon="vxe-icon-edit"
											v-auth="'WWE_Invoice.Edit'" @click="onEdit(scope.row)"></vxe-button>
									</el-tooltip>
									<el-tooltip class="box-item" effect="dark" :content="$t('message.limits.Delete')"
										placement="bottom">
										<vxe-button mode="text" status="error" icon="vxe-icon-delete"
											v-auth="'WWE_Invoice.Delete'" @click="onDelete(scope.row)"></vxe-button>
									</el-tooltip>
								</template>
							</vxe-column>
						</vxe-table>
					</div>
					<div class="scTable-page">
						<el-pagination @size-change="onSizeChange" @current-change="onCurrentChange" :pager-count="5"
							:page-sizes="[20, 30, 50, 100]" v-model:current-page="state.formTable.params.pageIndex"
							background v-model:page-size="state.formTable.params.pageSize"
							layout="total, sizes, prev, pager, next, jumper" :total="state.formTable.total" small>
						</el-pagination>
					</div>
				</div>
			</el-card>
		</div>

		<MassUpdateMethodDialog ref="massUpdateDialog" :table-ref="tableRef" @complete="handleUpdateComplete" />
		<InvoiceCommentsDrawer ref="commentsDrawerRef" :entityId="currentInvoiceId" entityType="Invoice"
			@action="handleDrawerAction" />
	</div>
</template>

<script lang="ts" setup name="wWE_Invoice/index">
import { ref, computed, reactive, onMounted, nextTick, defineAsyncComponent, onActivated, onDeactivated, getCurrentInstance, h, onUnmounted } from 'vue';
import { formatCurrency } from '/@/utils/index'; // Ensure this path points to the correct utility file
import { ElMessageBox, ElMessage, ElSelectV2 } from 'element-plus';
import { useI18n } from 'vue-i18n';
import { VxeTablePropTypes, VxeGridProps, VxeGridInstance } from 'vxe-table';
import { useRouter } from 'vue-router';

import DateRangeSelector from '/@/components/common/DateRangeSelector.vue';
import Export from './components/Export.vue';
import EnhancedTreeSelect from '/@/components/select/EnhancedTreeSelect.vue';

import { URLs } from '/@/constants';
import { DictionaryOption, SelectOption } from '/@/models';
import accountApi from '/@/api/WWE_Account';
import carrierApi from '/@/api/WWE_Carrier';
import dictItemApi from '/@/api/dictItem';
import invoiceApi from '/@/api/WWE_Invoice/index';

const InvoiceCommentsDrawer = defineAsyncComponent(() => import('./components/commentsDrawer.vue'));
const MassUpdateMethodDialog = defineAsyncComponent(() => import('./components/MassUpdateMethodDialog.vue'));

const { proxy } = getCurrentInstance() as any;
const { t } = useI18n();
const router = useRouter();
const exportRef = ref<InstanceType<typeof Export>>();

const retrievedStatusOptions = ref<DictionaryOption[]>([]);
const retrievalTypeOptions = ref<DictionaryOption[]>([]);
const retrievalMethodOptions = ref<DictionaryOption[]>([]);
const invoiceStatusOptions = ref<DictionaryOption[]>([]);
const accountOptions = ref<SelectOption[]>([]);
const carrierOptions = ref<SelectOption[]>([]);

const commentsDrawerRef = ref();
const massUpdateDialog = ref();

const commentDrawerVisible = ref(false);
const currentInvoiceId = ref('');

// 动态计算固定列头部的绝对定位
const fixedHeaderTop = ref(0);

interface RowVO {
	invoiceId: string;
	accountId: string;
	carrierId: string;
	invoiceNumber: string;
	proNumber: string;
	amount: number;
	docRetrievalMethod: string;
	docRetrievedStatus: string;
	docRetrievalType: string;
	status: string;
	comment: string;
	createdByName: string;
	modifiedByName: string;
	createdById: number;
	createdAt: string;
	modifiedById: number;
	modifiedAt: string;
}

const tableRef = ref<VxeGridInstance<RowVO>>();

const sortConfig = ref<VxeTablePropTypes.SortConfig<RowVO>>({
	defaultSort: {
		field: 'createdAt',
		order: 'desc',
	},
	trigger: 'cell',
});

const headerCellStyle = ({ column }: any) => {
	if (column.sortable) {
		return {
			cursor: 'pointer',
		};
	}
};

const rowStyle = ({ row }) => {
	if (row.status === 'Exception') {
		return {
			color: '#F56C6C', // 红色文本
		};
	}
};

function getDefaultQueryParams() {
	return {
		pageIndex: 1,
		pageSize: 20,
		order: 'createdAt',
		sort: 'desc',
		accountId: [] as string[],
		carrierId: [] as string[],
		invoiceNumber: '',
		proNumber: '',
		docRetrievalMethod: [],
		docRetrievedStatus: [],
		docRetrievalType: [],
		status: '',
		createdAtStart: null,
		createdAtEnd: null,
	};
}

const state = reactive({
	fromTitle: '',
	maxHeight: 0,
	formTable: {
		data: [],
		loading: false,
		total: 0,
		params: getDefaultQueryParams(),
	},
	options: [
		{ label: t('message.userFields.Active'), value: false },
		{ label: t('message.userFields.Inactive'), value: true },
	],
});

// 设置默认的最大高度
const tableMaxHeight = computed(() => (state.maxHeight > 0 ? state.maxHeight : 500));

var isMounted = false;

onMounted(async () => {
	isMounted = true;

	if (isMounted) {
		loadData();
		onSearch();
	}

	updateMaxHeight();
	window.addEventListener('resize', updateMaxHeight);
	window.addEventListener('scroll', handleScroll, { passive: true });

	// 初始化固定列头部位置
	setTimeout(() => {
		updateFixedHeaderPosition();
	}, 200);
});

// 清理事件监听器
onUnmounted(() => {
	window.removeEventListener('resize', updateMaxHeight);
	window.removeEventListener('scroll', handleScroll);
});

// 实时计算固定列头部的绝对定位，确保与主表格头部在同一水平线
const updateFixedHeaderPosition = () => {
	nextTick(() => {
		// 获取主表格头部（使用sticky定位的参照物）
		const mainTableHeader = document.querySelector('.vxe-table--main-wrapper .vxe-table--header-wrapper');
		// 获取固定列容器
		const fixedLeftWrapper = document.querySelector('.vxe-table--fixed-left-wrapper');
		const fixedRightWrapper = document.querySelector('.vxe-table--fixed-right-wrapper');

		if (mainTableHeader && (fixedLeftWrapper || fixedRightWrapper)) {
			const mainHeaderRect = mainTableHeader.getBoundingClientRect();

			// 计算主表格头部相对于固定列容器的位置
			if (fixedLeftWrapper) {
				const fixedLeftRect = fixedLeftWrapper.getBoundingClientRect();
				const relativeTop = mainHeaderRect.top - fixedLeftRect.top;
				fixedHeaderTop.value = relativeTop;
			} else if (fixedRightWrapper) {
				const fixedRightRect = fixedRightWrapper.getBoundingClientRect();
				const relativeTop = mainHeaderRect.top - fixedRightRect.top;
				fixedHeaderTop.value = relativeTop;
			}
		}
	});
};

// 节流函数，限制滚动事件的频率
let scrollTimer: number | null = null;
const handleScroll = () => {
	if (scrollTimer) {
		cancelAnimationFrame(scrollTimer);
	}
	scrollTimer = requestAnimationFrame(() => {
		updateFixedHeaderPosition();
	});
};

const updateMaxHeight = () => {
	nextTick(() => {
		setTimeout(() => {
			state.maxHeight = window.innerHeight - 460;
			updateFixedHeaderPosition();
		}, 100);
	});
};

onActivated(async () => {
	if (!isMounted) {
		await onSearch();
	}
	// 页面激活时更新固定列头部位置
	setTimeout(() => {
		updateFixedHeaderPosition();
	}, 100);
});

onDeactivated(() => {
	isMounted = false;
});

const hasSelection = computed(() => {
	const $table = tableRef.value;
	return $table ? $table.getCheckboxRecords().length > 0 : false;
});

const loadData = async () => {
	const [dictItemsResponse, accountsResponse, carriersResponse] = await Promise.all([dictItemApi.Many(['DocumentType', 'Retrieval Method', 'Retrieved Status', 'Invoice Status']), accountApi.GetAccountOptions(), carrierApi.GetCarrierOptions()]);

	retrievalTypeOptions.value = [];
	retrievalMethodOptions.value = [];

	if (dictItemsResponse?.data) {
		const docTypeResult = dictItemsResponse.data.find((item) => item.dictValue === 'DocumentType');
		if (docTypeResult?.items) retrievalTypeOptions.value = docTypeResult.items;

		const methodResult = dictItemsResponse.data.find((item) => item.dictValue === 'Retrieval Method');
		if (methodResult?.items) retrievalMethodOptions.value = methodResult.items;

		const statusResult = dictItemsResponse.data.find((item) => item.dictValue === 'Retrieved Status');
		if (statusResult?.items) retrievedStatusOptions.value = statusResult.items;

		const invoiceStatusResult = dictItemsResponse.data.find((item) => item.dictValue === 'Invoice Status');
		if (invoiceStatusResult?.items) invoiceStatusOptions.value = invoiceStatusResult.items;
	}

	accountOptions.value = accountsResponse?.data || [];
	carrierOptions.value = carriersResponse?.data || [];
	carrierOptions.value = (carriersResponse?.data || []).map((carrier) => ({
		...carrier,
		label: `${carrier.value} - ${carrier.label}`,
	}));
};

const onMassMethodUpdate = () => {
	massUpdateDialog.value.openDialog();
};

const handleUpdateComplete = async ({ success, ids }) => {
	if (success) {
		onSearch();
	}
};

const openCommentDrawer = (invoiceId: string) => {
	tableRef.value?.clearCheckboxRow(); // 清除所有复选框选中

	currentInvoiceId.value = invoiceId;
	commentsDrawerRef.value?.open();
};

const handleDrawerAction = (action: string) => {
	switch (action) {
		case 'close':
			commentDrawerVisible.value = false;
			break;
		case 'refresh':
			onSearch();
			break;
		case 'close-and-refresh':
			commentDrawerVisible.value = false;
			onSearch();
			break;
	}
};

const onAdd = () => {
	state.fromTitle = t('message.wWE_InvoiceButtons.createWWE_Invoice');
	router.push(URLs.InvoiceIndexing);
};

const onImport = () => { };

const onEdit = (row: any) => {
	router.push(URLs.InvoiceIndexing + '/' + row.invoiceId);
};

const onDelete = async (row: any) => {
	ElMessageBox.confirm(t('message.page.dlgDeleteText'), t('message.page.dlgTip'), {
		confirmButtonText: t('message.page.confirm'),
		cancelButtonText: t('message.page.cancel'),
		type: 'warning',
	})
		.then(async () => {
			if (row.invoiceId) {
				await invoiceApi
					.DeleteByKey(row.invoiceId)
					.then((rs: any) => {
						ElMessage.success(t('message.page.deleteSuccess'));
						onSearch();
					})
					.catch((error: HandledError) => {
						if (!error.isHandled) {
							const errorCode = error.code;
							const errorMessage = error.message;
							ElMessage.error(errorMessage);
						}
					});
			}
		})
		.catch((error: any) => {
			if (error !== 'cancel') {
				ElMessage.error(error.resultMsg);
			}
		});
};

const onSearch = async () => {
	state.formTable.loading = true;

	await invoiceApi
		.Query(state.formTable.params)
		.then((rs: any) => {
			state.formTable.data = rs.data;
			state.formTable.total = rs.totalCount;

			let obj = { entityName: 'WWE_Invoice', pageParams: state.formTable.params };
			exportRef?.value?.getColumns(obj);
		})
		.catch((error: HandledError) => {
			if (!error.isHandled) {
				const errorCode = error.code;
				const errorMessage = error.message;
				ElMessage.error(errorMessage);
			}
		})
		.finally(() => {
			state.formTable.loading = false;

			if (state.formTable.data.length <= 0) {
				//数据为空，清空表头的默认选中
				nextTick(() => {
					proxy.$refs.tableRef.clearCheckboxRow();
				});
			}

			// 数据加载完成后更新固定列头部位置
			setTimeout(() => {
				updateFixedHeaderPosition();
			}, 100);
		});
};

const onSortChange = (column: any) => {
	if (!column.field) {
		if (column.column.sortable) {
			state.formTable.params.order = column.column.field;

			let field = column.column.field;

			// 默认降序
			let order: 'desc' | 'asc' = 'desc';

			const $table = tableRef.value;

			if (state.formTable.params.sort == 'desc') {
				state.formTable.params.sort = 'asc';
				order = 'asc';

				if ($table) {
					$table.setSort({ field, order });
				}
			} else {
				state.formTable.params.sort = 'desc';
				order = 'desc';

				if ($table) {
					$table.setSort({ field, order });
				}
			}

			onSearch();
		}
	} else {
		state.formTable.params.order = column.field;
		state.formTable.params.sort = column.order;

		onSearch();
	}
};

const onClear = () => {
	clearQueryParams(state.formTable.params);
	onSearch();
};

function clearQueryParams(params: any): void {
	Object.assign(params, getDefaultQueryParams());
}

const onSizeChange = (val: number) => {
	state.formTable.params.pageSize = val;

	onSearch();
};

const onCurrentChange = (val: number) => {
	state.formTable.params.pageIndex = val;

	onSearch();
};

const cellClick = ({ row, column }: any) => {
	const $table = tableRef.value;
	if ($table) {
		$table.toggleCheckboxRow(row);
	}
};
</script>

<style scoped lang="scss">
.table-card {
	// 确保卡片没有overflow限制
	overflow: visible;
}

// 只为主表格的表头设置sticky，不包括固定列
:deep(.vxe-table--main-wrapper .vxe-table--header-wrapper) {
	position: sticky !important;
	position: -webkit-sticky !important; // 兼容Safari
	top: 34px !important; // 标签栏高度
	z-index: 100;
}

// 固定列头部 - 使用绝对定位实现动态计算
:deep(.vxe-table--fixed-left-wrapper .vxe-table--header-wrapper) {
	position: absolute !important;
	top: var(--fixed-header-top, 0px) !important;
	z-index: 102 !important;
}

:deep(.vxe-table--fixed-right-wrapper .vxe-table--header-wrapper) {
	position: absolute !important;
	top: var(--fixed-header-top, 0px) !important;
	z-index: 102 !important;
}

// 固定列容器保持原有的fixed定位
:deep(.vxe-table--fixed-left-wrapper),
:deep(.vxe-table--fixed-right-wrapper) {
	z-index: 101 !important;
}

@media (max-width: 800px) {
	::v-deep .el-drawer {
		width: 100% !important;
	}
}

.comment-count {
	margin-right: 40px;
	cursor: pointer;
}

:deep(.el-badge__content.is-fixed) {
	top: 4px !important;
}
</style>
