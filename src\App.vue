<template>
	<el-config-provider :size="getGlobalComponentSize" :locale="getGlobalI18n">
		<Setings ref="setingsRef" v-show="setLockScreen" />
		<router-view v-show="setLockScreen" />
		<LockScreen v-if="themeConfig.isLockScreen" />
		<CloseFull v-if="!themeConfig.isLockScreen" />
		<Upgrade v-if="showVersion" />
		<!-- <Sponsors /> -->
	</el-config-provider>
</template>

<script setup lang="ts" name="app">
import { defineAsyncComponent, computed, ref, onBeforeMount, onMounted, onUnmounted, nextTick, watch, reactive, getCurrentInstance } from 'vue';
import { useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { storeToRefs } from 'pinia';
import { useTagsViewRoutes } from '/@/stores/tagsViewRoutes';
import { useThemeConfig } from '/@/stores/themeConfig';
import other from '/@/utils/other';
import { Local, Session } from '/@/utils/storage';
import mittBus from '/@/utils/mitt';
import setIntroduction from '/@/utils/setIconfont';
import appSettings from '/@/config/index.js';

// 引入组件
const LockScreen = defineAsyncComponent(() => import('/@/layout/lockScreen/index.vue'));
// const Setings = defineAsyncComponent(() => import('/@/layout/navBars/topBar/setings.vue'));
import Setings from '/@/layout/navBars/topBar/setings.vue';
import { Consts } from './constants';

const CloseFull = defineAsyncComponent(() => import('/@/layout/navBars/topBar/closeFull.vue'));
const Upgrade = defineAsyncComponent(() => import('/@/layout/upgrade/index.vue'));
// const Sponsors = defineAsyncComponent(() => import('/@/layout/sponsors/index.vue'));

// 定义变量内容
const { messages, locale } = useI18n();
const setingsRef = ref();
const route = useRoute();
const stores = useTagsViewRoutes();
const storesThemeConfig = useThemeConfig();
const { themeConfig } = storeToRefs(storesThemeConfig);
const showVersion = ref(false);

const state = reactive({
	// @ts-ignore
	version: Local.get('version') || __NEXT_VERSION__,
	appSettings: appSettings,
});

// 设置锁屏时组件显示隐藏
const setLockScreen = computed(() => {
	// 防止锁屏后，刷新出现不相关界面
	// https://gitee.com/lyt-top/vue-next-admin/issues/I6AF8P
	return themeConfig.value.isLockScreen ? themeConfig.value.lockScreenTime > 1 : themeConfig.value.lockScreenTime >= 0;
});

// 获取版本号
const getVersion = computed(() => {
	let isVersion = false;
	if (route.path !== '/login') {
		if ((Local.get('version') && Local.get('version') !== state.version) || !Local.get('version')) isVersion = true;
	}
	return isVersion;
});

const checkVersion = async () => {
	const response = await fetch('/version.json?' + new Date().getTime());
	if (response.ok) {
		const data = await response.json();

		let isVersion = false;

		if ((Local.get('version') && Local.get('version') !== data.version) || !Local.get('version')) {
			isVersion = true
		}

		if (isVersion) {
			if (route.path === '/login') {
				Local.clear();
				Session.clear();
			} else {
				Local.clearLogin();
				Local.clearStyle();
				Session.clear();
				Local.set('version', data.version);
				window.location.reload();
			}
		}
	}
	else {
		console.error('Failed to load config data');
	}
};

const inspectVersion = async () => {
	const response = await fetch('/version.json?' + new Date().getTime());
	if (response.ok) {
		const data = await response.json();
		if ((Local.get('version') && Local.get('version') !== data.version) || !Local.get('version')) {
			Local.clearLogin();
			Local.set('version', data.version);
			window.location.reload();
		}
	}
	else {
		console.error('Failed to load config data');
	}
};

// 获取全局组件大小
const getGlobalComponentSize = computed(() => {
	return other.globalComponentSize();
});

// 获取全局 i18n
const getGlobalI18n = computed(() => {
	return messages.value[locale.value];
});

// 设置初始化，防止刷新时恢复默认
onBeforeMount(() => {
	if (!themeConfig.value.isShowBptsPage) {
		// 设置批量第三方 icon 图标
		setIntroduction.cssCdn();
		// 设置批量第三方 js
		setIntroduction.jsCdn();
	}
});

const startRefreshingVersion = () => {
	setInterval(async () => {
		try {
			checkVersion();
		} catch (error) {
			console.error('Error fetching config data:', error);
		}
	}, 1000 * 10);
};

// 页面加载时
onMounted(() => {
	nextTick(() => {
		// 监听布局配'置弹窗点击打开
		mittBus.on('openSetingsDrawer', () => {
			//
			setingsRef.value.openDrawer();
		});
		// 获取缓存中的布局配置
		if (Local.get('themeConfig')) {
			storesThemeConfig.setThemeConfig({ themeConfig: Local.get('themeConfig') });
			document.documentElement.style.cssText = Local.get('themeConfigStyle');
		}
		// 获取缓存中的全屏配置
		if (Session.get('isTagsViewCurrenFull')) {
			stores.setCurrenFullscreen(Session.get('isTagsViewCurrenFull'));
		}

		// inspectVersion();
	});

	startRefreshingVersion();
});

// 页面销毁时，关闭监听布局配置/i18n监听
onUnmounted(() => {
	mittBus.off('openSetingsDrawer', () => { });
});

// 监听路由的变化，设置网站标题
watch(
	() => route.path,
	() => {
		other.useTitle();
		checkVersion();
	},
	{
		deep: true,
	}
);
</script>
