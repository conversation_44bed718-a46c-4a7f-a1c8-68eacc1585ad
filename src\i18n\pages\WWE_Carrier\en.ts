export default {
	wWE_CarrierSearch: {
		//Search Area
	},
	wWE_CarrierButtons: {
		createWWE_Carrier: 'New Carrier',
		editWWE_Carrier: 'Edit',
		importWWE_Carrier: 'Import',
	},
	wWE_CarrierLabels: {
		CarrierId: 'Carrier Id',
		CarrierName: 'Carrier Name',
		CarrierCode: 'Carrier #',
		SCAC: 'SCAC',
		ContactEmail: 'Contact Email',
		ContactPhone: 'Contact Phone',
		Website: 'Website',
		Address: 'Address',
		RequiredFileTypes: 'Supported File Types',
		CreatedById: 'Created By Id',
		CreatedByName: 'Created By',
		CreatedAt: 'Created At',
		ModifiedById: 'Modified By Id',
		ModifiedByName: 'Modified By',
		ModifiedAt: 'Modified At',
		IsDeleted: 'Is Deleted',
	},
	wWE_CarrierFields: {
		CarrierId: 'Carrier Id',
		CarrierName: 'Carrier Name',
		CarrierCode: 'Carrier #',
		SCAC: 'SCAC',
		ContactEmail: 'Contact Email',
		ContactPhone: 'Contact Phone',
		Website: 'Website',
		Address: 'Address',
		RequiredFileTypes: 'Supported File Types',
		CreatedById: 'Created By Id',
		CreatedByName: 'Created By',
		CreatedAt: 'Created At',
		ModifiedById: 'Modified By Id',
		ModifiedByName: 'Modified By',
		ModifiedAt: 'Modified At',
		IsDeleted: 'Is Deleted',
	},
	wWE_CarrierDlgTips: {
		deleteError: 'There is already a configuration relationship, deletion is not allowed!',
	},
	wWE_CarrierCommon: {
		ImportData: 'Import Data',
		UploadExcelFile: 'Upload Excel File',
		UploadCsvFile: 'Upload Csv File',
		Note: '(Note: This action will overwrite the data of the selected month)',
	},
};
