<template>
	<el-row @click="showInput" style="border: 1px solid; width: 100%">
		<el-col>
			<template v-if="selectedIndex === -1">
				<el-tag v-for="tag in dynamicTags" :key="tag.content" :type="tag.status === 'err' ? 'danger' : ''" style="margin-right: 5px" closable :disable-transitions="false" @close="handleClose(tag)" @dblclick="editTag(tag)">
					{{ tag.content }}
				</el-tag>
			</template>

			<div style="display: inline">
				<el-autocomplete ref="InputRef" v-model="inputValue" :fetch-suggestions="querySearch" :trigger-on-focus="false" value-key="content" class="inline-input w-50" placeholder="" @select="handleSelect" @keyup="handleInputEnter" @blur="inputSuccess" style="width: 240px" size="middle" />
			</div>
		</el-col>
	</el-row>
</template>

<script lang="ts" setup>
import { ref, computed, nextTick, onMounted } from 'vue';
import { ElInput } from 'element-plus';
import emailsApi from '/@/api/emails/index';

const emit = defineEmits(['update:list']);
const props = defineProps({
	list: {
		type: Array,
		default: () => [],
	},
});
console.log('props', props);
const dynamicTags = computed({
	get: () => props.list || [],
	set: (value) => {
		emit('update:list', value);
	},
});

const inputValue = ref('');
const InputRef = ref<InstanceType<typeof ElInput>>();
const selectedIndex = ref(-1);
const allAddrData = ref<any[]>([]);
const tempQueryString = ref('');
const hasSelectedByEnter = ref(false);

// ===== 输入处理 =====

const handleClose = (tag: any) => {
	const index = dynamicTags.value.indexOf(tag);
	if (index > -1) {
		const newTags = [...dynamicTags.value];
		newTags.splice(index, 1);
		dynamicTags.value = newTags;
	}
};

const showInput = () => {
	nextTick(() => {
		InputRef.value?.focus();
	});
};

const handleInputEnter = (e: KeyboardEvent) => {
	if (e.key === ';' || e.key === 'Enter') {
		e.preventDefault();

		if (hasSelectedByEnter.value) {
			hasSelectedByEnter.value = false;
			return;
		}

		const highlightedItem = document.querySelector('.el-autocomplete-suggestion li.highlighted');
		if (highlightedItem) {
			const index = highlightedItem.getAttribute('data-index');
			if (index !== null && allAddrData.value[index]) {
				handleSelect(allAddrData.value[index]);
				return;
			}
		}

		const matches = allAddrData.value.filter(createFilter(tempQueryString.value));
		if (matches.length > 0) {
			handleSelect(matches[0]);
		} else {
			inputSuccess();
		}
	}
};

const inputSuccess = () => {
	if (inputValue.value) {
		const tagData = getEmailData(inputValue.value);
		if (!tagData) return;

		const exists = dynamicTags.value.some((tag) => tag.content === tagData.content);
		if (exists) {
			inputValue.value = '';
			selectedIndex.value = -1;
			return;
		}

		if (selectedIndex.value > -1) {
			const newTags = [...dynamicTags.value];
			newTags[selectedIndex.value] = tagData;
			dynamicTags.value = newTags;
		} else {
			dynamicTags.value = [...dynamicTags.value, tagData];
		}
	}

	inputValue.value = '';
	selectedIndex.value = -1;
};

const editTag = (tag: any) => {
	selectedIndex.value = dynamicTags.value.indexOf(tag);
	inputValue.value = tag.email;
	showInput();
};

const getEmailData = (value: string) => {
	value = value.replace(';', '').trim();
	if (!value) return null;

	const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
	let name = null;
	let email = null;

	const angleBracketMatch = value.match(/^(.*?)\s*<([^>]+)>$/);
	if (angleBracketMatch) {
		name = angleBracketMatch[1].trim();
		email = angleBracketMatch[2].trim();
	} else if (emailRegex.test(value)) {
		email = value;
		name = value.split('@')[0];
	} else {
		email = value; // 不合规，待标记
	}

	const isValid = !!email && emailRegex.test(email);
	const displayContent = name && email ? `${name} <${email}>` : value;

	return {
		content: displayContent,
		name: name || email?.split('@')[0] || null,
		email,
		status: isValid ? 'normal' : 'err',
	};
};

// ===== 搜索建议 =====

const createFilter = (queryString: string) => {
	const lowerQuery = queryString.toLowerCase();
	return (data: any) => {
		return (data.name && data.name.toLowerCase().includes(lowerQuery)) || (data.email && data.email.toLowerCase().includes(lowerQuery));
	};
};

const querySearch = (queryString: string, cb: (results: any[]) => void) => {
	tempQueryString.value = queryString;
	const results = queryString ? allAddrData.value.filter(createFilter(queryString)) : allAddrData.value;
	cb(results);
};

const handleSelect = (item: any) => {
	hasSelectedByEnter.value = true;

	dynamicTags.value = dynamicTags.value.filter((tag) => tag.content !== item.content);
	inputValue.value = item.content;
	selectedIndex.value = -1;

	inputSuccess();
};

// ===== 初始化邮件数据 =====

onMounted(async () => {
	try {
		const response = await emailsApi.GetEmails();
		allAddrData.value = (response.data || []).map((item: any) => {
			const name = item.name?.trim() || item.email?.split('@')[0];
			const email = item.email;
			const content = name;

			return {
				...item,
				name,
				email,
				content,
			};
		});
	} catch (error) {
		console.error('Failed to fetch emails:', error);
		allAddrData.value = [];
	}
});
</script>

<style scoped>
:deep(.el-input) {
	--el-input-focus-border: #fff;
	--el-input-transparent-border: 0 0 0 0px;
	--el-input-border-color: #fff;
	--el-input-hover-border: 0px;
	--el-input-hover-border-color: #fff;
	--el-input-focus-border-color: #fff;
	--el-input-clear-hover-color: #fff;
	box-shadow: 0 0 0 0px;
	--el-input-border: 0px;
}
:deep(.el-select .el-input__wrapper.is-focus) {
	box-shadow: 0 0 0 0px;
}
:deep(.el-select .el-input.is-focus .el-input__wrapper) {
	box-shadow: 0 0 0 0px;
}
:deep(.el-select) {
	--el-select-border-color-hover: #fff;
}
</style>
