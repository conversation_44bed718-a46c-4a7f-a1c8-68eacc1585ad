﻿<template>
	<div class="roleModulePermission-edit-container">
		<el-drawer
			:title="title"
			v-model="isShowDialog"
			destroy-on-close
			draggable
			:close-on-click-modal="false"
			:close-on-press-escape="false"
			:size="600"
			:modal="false"
			@close="onCancel">
			<!-- 使用 v-slot 自定义标题区域 -->
			<template v-slot:title>
				<div class="menu-item">
					<el-icon :size="18" @click="onReturn" title="return" class="icon-item"><ele-Back /></el-icon>
					<h3>{{ title }}</h3>
				</div>
			</template>
			<div class="drawer-content">
				<el-form ref="ruleFormRef" label-width="90px" label-position="top">
					<el-row :gutter="35">
						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
							<el-form-item>
								<el-input v-model="filterText" placeholder="Filter keyword" />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
							<el-form-item prop="createId">
								<div class="treeMain">
									<el-tree
										ref="treeRef"
										:data="treeApiData"
										show-checkbox
										node-key="id"
										highlight-current
										default-expand-all
										:props="{ children: 'children', label: 'name' }"
										:default-checked-keys="treeCheckData"
										:filter-node-method="filterNode" />
								</div>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>
				<div class="drawer-footer">
					<span class="dialog-footer">
						<el-button @click="onReturn" size="small">Cancel</el-button>

						<el-button type="primary" @click="onSubmit" size="small">Save</el-button>
					</span>
				</div>
			</div>
		</el-drawer>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, getCurrentInstance, ref, defineComponent, watch, nextTick } from 'vue';
import roleModulePermissionApi from '/@/api/roleModulePermission';
import apiApi from '/@/api/api/index';

export default defineComponent({
	name: 'roleModulePermissionCreateOrEdit',
	components: {},
	setup(props, context) {
		const { proxy } = getCurrentInstance() as any;
		const treeRef = ref();
		const state = reactive({
			title: 'Select Api',
			isShowDialog: false,
			isBlockCloseDialog: false,
			saveLoading: false,
			treeApiData: [],
			treeCheckData: [], //已勾选
			filterText: '',
		});
		// 打开弹窗
		const openDialog = (parmas: any, name: any) => {
			if (parmas) {
				state.treeCheckData = parmas;
			}
			state.isShowDialog = true;
			state.isBlockCloseDialog = false;
			window.console.log('parmas', parmas);
			apiApi.Tree().then((rs) => {
				state.treeApiData = rs.data;
			});
			nextTick(() => {
				// 在这里执行您想要在下一个 DOM 更新周期后运行的代码
				if (name) state.filterText = name.split('.')[0];
			});
		};
		// 关闭弹窗
		const closeDialog = () => {
			onInitForm();
			state.isShowDialog = false;
			if (!state.isBlockCloseDialog) {
				context.emit('fetchData', null);
			}
		};
		// 取消
		const onCancel = () => {
			closeDialog();
		};
		const onReturn = () => {
			state.isBlockCloseDialog = true;
			closeDialog();
		};
		// 保存
		const onSubmit = () => {
			var items = treeRef.value.getCheckedNodes();
			items = items.filter((a: any) => a.gid == null);
			//console.log('it', items);

			context.emit('fetchData', items);
			state.isBlockCloseDialog = true;
			closeDialog();
		};
		// 重置表单
		const onInitForm = () => {
			state.isShowDialog = true;
			state.treeCheckData = [];
			state.treeApiData = [];
			state.filterText = '';
		};
		const filterNode = (value: string, data: any) => {
			if (!value) return true;
			return data.name.toLowerCase().includes(value.toLowerCase());
		};
		watch(
			() => state.filterText,
			(val) => {
				treeRef.value!.filter(val);
			}
		);
		return {
			openDialog,
			closeDialog,
			onSubmit,
			onCancel,
			onReturn,
			onInitForm,
			treeRef,
			filterNode,
			...toRefs(state),
		};
	},
});
</script>
<style scoped>
.treeMain {
	height: 75vh;
	overflow: auto;
	border: 1px solid #dcdfe6;
	margin-bottom: 10px;
	width: 90vh;
}

.drawer-content {
	padding: 20px;
}

.drawer-footer {
	text-align: right;
}

.menu-item {
	display: flex;
	align-items: center;
}

.icon-item {
	cursor: pointer;
	margin-right: 10px;
	/* 调整图标与文本间距 */
	transition: color 0.3s;
	/* 颜色变化过渡时间 */

	/* 鼠标悬停时改变颜色 */
	&:hover {
		color: #729eff;
	}
}
</style>
