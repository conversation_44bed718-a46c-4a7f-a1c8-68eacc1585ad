<template>
	<div class="system-edit-menu-container">
		<el-drawer v-model="state.showDialog" destroy-on-close :title="title" draggable :close-on-click-modal="false" :close-on-press-escape="false" width="500px" :direction="direction">
			<div class="drawer-content">
				<el-form :model="state.form" ref="formRef" :rules="state.rules" label-width="130px" label-position="top">
					<el-row :gutter="35">
						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.wWE_CarrierFields.CarrierCode')" prop="carrierCode">
								<el-input v-model="state.form.carrierCode" clearable size="default" @change="handleCarrierCodeChange" />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.wWE_CarrierFields.CarrierName')" prop="carrierName">
								<el-input v-model="state.form.carrierName" clearable size="default" />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.wWE_CarrierFields.SCAC')" prop="scac">
								<el-input v-model="state.form.scac" clearable size="default" />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.wWE_CarrierFields.Website')" prop="website">
								<el-input v-model="state.form.website" clearable size="default" />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.wWE_CarrierFields.RequiredFileTypes')" prop="requiredFileTypes">
								<el-select v-model="state.form.requiredFileTypes" multiple clearable size="default" :placeholder="$t('message.page.selectKeyPlaceholder')" @change="handleFileTypeChange">
									<el-option v-for="item in documentTypeData" :key="item.itemId" :label="item.itemName" :value="item.itemValue" :disabled="shouldDisableOption(item.itemValue)"></el-option>
								</el-select>
								<div class="info-tip">
									<el-icon><InfoFilled /></el-icon>
									<span> Note: You can only select either <strong>POD</strong> or <strong>DR</strong>, not both </span>
								</div>
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.wWE_CarrierFields.Address')" prop="address">
								<el-input v-model="state.form.address" clearable size="default" />
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>

				<div class="drawer-footer">
					<span class="dialog-footer">
						<el-button @click="onCancel" size="small">{{ $t('message.page.buttonCancel') }}</el-button>
						<el-button :loading="state.loading" type="primary" @click="onSubmit" size="small">{{ $t('message.page.buttonSave') }}</el-button>
					</span>
				</div>
			</div>
		</el-drawer>
	</div>
</template>

<script lang="ts" setup name="wWE_Carrier/createOrEdit">
import { reactive, toRefs, ref, getCurrentInstance, computed, watch } from 'vue';
import { cloneDeep } from 'lodash-es';
import { useI18n } from 'vue-i18n';
import { ElMessageBox, ElMessage } from 'element-plus';
import type { DrawerProps } from 'element-plus';
import wWE_CarrierApi from '/@/api/WWE_Carrier/index';
import invoiceApi from '/@/api/WWE_Invoice/index';
import dictItem from '/@/api/dictItem';
import { validateForbiddenSymbols } from '/@/utils/toolsValidate';

import { useUserInfo } from '/@/stores/userInfo';
import { storeToRefs } from 'pinia';
import { Roles } from '/@/constants';

interface CreateOrEditInput {
	carrierId: number;
	carrierName: string;
	carrierCode: string;
	scac: string;
	contactEmail: string;
	contactPhone: string;
	website: string;
	address: string;
	requiredFileTypes: string | string[];
	createdByName: string;
	modifiedByName: string;
}

defineProps({
	title: {
		type: String,
		default: '',
	},
});

const stores = useUserInfo();
const { userInfos } = storeToRefs(stores);

const emit = defineEmits(['refreshData']);

const direction = ref<DrawerProps['direction']>('rtl');

const { t } = useI18n();
const formRef = ref();
const documentTypeData = ref([]) as unknown as any[];

const state = reactive({
	showDialog: false,
	loading: false,
	form: {} as CreateOrEditInput,
	rules: {
		carrierId: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: ['blur', 'change'] }],
		carrierName: [
			{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: ['blur', 'change'] },
			{
				validator: (_, value, callback) => {
					const error = validateForbiddenSymbols(value);
					error ? callback(new Error(error)) : callback();
				},
				trigger: ['blur', 'change'],
			},
		],
		carrierCode: [
			{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: ['blur', 'change'] },
			{
				validator: (_, value, callback) => {
					const error = validateForbiddenSymbols(value);
					error ? callback(new Error(error)) : callback();
				},
				trigger: ['blur', 'change'],
			},
		],
		website: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: ['blur', 'change'] }],
		requiredFileTypes: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: ['blur', 'change'] }],
		scac: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: ['blur', 'change'] }],
	},
});

const open = async (row: CreateOrEditInput) => {
	let formData = cloneDeep(row) as CreateOrEditInput;
	if (formData.requiredFileTypes && typeof formData.requiredFileTypes === 'string') {
		formData.requiredFileTypes = formData.requiredFileTypes.split(',');
	}
	state.form = formData;

	state.showDialog = true;

	dictItem.Many(['DocumentType']).then((rs: any) => {
		const result = rs.data.find((item: any) => item.dictValue === 'DocumentType');
		if (result && result.items) {
			documentTypeData.value = result.items;
		}
	});
};

const onCancel = () => {
	closeDialog();
};

const closeDialog = () => {
	onInitForm();
	state.showDialog = false;
};

const onSubmit = async () => {
	formRef.value.validate(async (valid: boolean) => {
		if (!valid) return;

		state.loading = true;

		if (state.form.carrierId) {
			try {
				const req = {
					carrierId: state.form.carrierId,
				};
				const result = await invoiceApi.CheckAssociatedData(req);

				if (result.data) {
					ElMessageBox.confirm('This record has associated data. Are you sure you want to proceed?', 'Warning', {
						confirmButtonText: 'Confirm',
						cancelButtonText: 'Cancel',
						type: 'warning',
					})
						.then(async () => {
							await saveData();
						})
						.catch(() => {
							state.loading = false;
						});
					return; // Exit early after showing confirmation
				}
			} catch (error) {
				ElMessage.error(t('message.page.CheckAssociatedDataError'));
				state.loading = false;
				return; // Exit early on error
			}
		}

		await saveData();
	});
};

const saveData = async () => {
	let obj = Object.assign({}, state.form);
	if (Array.isArray(obj.requiredFileTypes)) {
		obj.requiredFileTypes = obj.requiredFileTypes.join(',');
	}

	await wWE_CarrierApi
		.Save(obj)
		.then(() => {
			ElMessage.success(t('message.page.Success'));
			emit('refreshData');

			closeDialog();
			state.loading = false;
		})
		.catch((error: HandledError) => {
			if (!error.isHandled) {
				const errorCode = error.code;
				const errorMessage = error.message;
				ElMessage.error(errorMessage);
			}
		})
		.finally(() => {
			state.loading = false;
		});
};

const onInitForm = async () => {};

const handleCarrierCodeChange = (value: string) => {
	if (!state.form.scac && value) {
		state.form.scac = value;
	}
};

const handleFileTypeChange = (selectedValues: string[]) => {
	if (userInfos.value.userId === Roles.SuperAdmin.userId) {
		state.form.requiredFileTypes = selectedValues;
		return;
	}
	// Check if both POD and DR are selected
	const hasPOD = selectedValues.includes('POD');
	const hasDR = selectedValues.includes('DR');

	if (hasPOD && hasDR) {
		// If both are selected, keep only the last selected one
		const lastSelected = state.form.requiredFileTypes[state.form.requiredFileTypes.length - 1];
		state.form.requiredFileTypes = lastSelected === 'POD' ? selectedValues.filter((v) => v !== 'DR') : selectedValues.filter((v) => v !== 'POD');

		ElMessage.warning('You can only select either POD or DR, not both');
	}
};

const shouldDisableOption = (optionValue: string) => {
	if (userInfos.value.userId === Roles.SuperAdmin.userId) {
		return false; // Super Admin can select any option
	}

	// If the option is POD or DR, check if the opposite is already selected
	const currentValues = state.form.requiredFileTypes || [];

	if (optionValue === 'POD') {
		return currentValues.includes('DR');
	}

	if (optionValue === 'DR') {
		return currentValues.includes('POD');
	}

	return false;
};

defineExpose({
	open,
});
</script>

<style scoped>
.drawer-content {
	padding: 20px;
}

.drawer-footer {
	text-align: right;
}

.info-tip {
	display: flex;
	align-items: center;
	gap: 6px;
	font-size: 12px;
	color: var(--el-text-color-secondary);
	.el-icon {
		font-size: 14px;
	}

	strong {
		color: var(--el-color-primary);
		font-weight: 500;
	}
}
</style>
