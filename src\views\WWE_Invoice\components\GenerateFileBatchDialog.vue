<template>
	<div class="generate-batch-container">
		<el-dialog v-model="dialogVisible" :title="showResult ? 'Generation Result' : 'Generate File Batch'" class="generate-batch-dialog" width="550px" :close-on-click-modal="false" draggable @closed="handleDialogClosed">
			<!-- 生成表单 -->
			<div v-if="!showResult">
				<div class="dialog-header">
					<div class="header-text">
						<p class="tip-text">Generate batch files for carrier invoices</p>
					</div>
				</div>

				<div class="content-section">
					<div class="form-container">
						<el-form size="default" label-width="90" label-position="right">
							<!-- Records Filter (now first) -->
							<el-form-item label="Batch Filter:" class="mb5">
								<div class="smart-switch">
									<el-switch v-model="excludeGenerated" size="small" @change="loadCarrierStats" />
									<span class="switch-description">
										{{ excludeGenerated ? 'Skipping processed records' : 'Using all selected/queried records' }}
									</span>
								</div>
							</el-form-item>

							<!-- Generation Source -->
							<el-form-item label="Data Source:" class="mb5">
								<el-radio-group v-model="generateType" @change="handleGenerateTypeChange">
									<el-radio label="selected">Selected Items </el-radio>
									<el-radio label="query">Current Query </el-radio>
								</el-radio-group>
							</el-form-item>
							<el-form-item> </el-form-item>
						</el-form>
					</div>
					<div class="info-tip">
						<el-icon><InfoFilled /></el-icon>
						<span v-if="generateType === 'selected'">
							Will generate files for <strong>{{ totalCount }}</strong> selected items
						</span>
						<span v-else>
							Will generate files for <strong>{{ totalCount }}</strong> records matching current search
						</span>
					</div>
					<!-- Carrier statistics table -->
					<el-table :data="carrierStats" border style="width: 100%">
						<el-table-column prop="accountName" label="Company Account" />
						<el-table-column prop="carrierName" label="Carrier" />
						<el-table-column prop="processedCount" label="Count" width="100" align="right" />
						<template #empty>
							<div class="empty-text">No carrier data available</div>
						</template>
					</el-table>
				</div>
			</div>

			<!-- 结果展示 -->
			<div v-else class="result-container">
				<el-alert :title="result.succeeded ? 'Generation Completed' : 'Generation Failed'" :type="result.succeeded ? 'success' : 'error'" :closable="false" show-icon />

				<div v-if="result.succeeded" class="result-data">
					<el-table :data="result.data" border size="small">
						<el-table-column prop="accountName" label="Company Account" />
						<el-table-column prop="carrierName" label="Carrier" />
						<el-table-column prop="batchNumber" label="Batch Number" />
						<el-table-column prop="processedCount" label="Processed" width="100" align="right" />
					</el-table>
				</div>

				<div v-else class="error-message">
					{{ result.resultMsg || 'Unknown error occurred' }}
				</div>
			</div>

			<template #footer>
				<el-button v-if="!showResult" @click="dialogVisible = false"> Cancel </el-button>
				<el-button v-if="!showResult" type="primary" @click="confirmGenerate" :loading="loading" :disabled="totalCount === 0">
					Generate Files
					<template #loading>
						<span class="ml-2">Loading...</span>
					</template>
				</el-button>
				<el-button v-else type="primary" @click="handleClose"> Close </el-button>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent, PropType, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { Warning, InfoFilled } from '@element-plus/icons-vue';
import type { VxeGridInstance } from 'vxe-table';
import invoiceApi from '/@/api/WWE_Invoice';
import { CarrierSummaryResponse } from '/@/models/Invoice';

export default defineComponent({
	name: 'GenerateFileBatchDialog',
	components: { Warning, InfoFilled },
	props: {
		tableRef: {
			type: Object as PropType<VxeGridInstance>,
			required: true,
		},
		queryParams: {
			type: Object,
			required: true,
		},
		totalCount: {
			type: Number,
			required: true,
		},
	},
	emits: ['complete'],
	setup(props, { emit }) {
		const state = reactive({
			dialogVisible: false,
			totalCount: 0,
			generateType: null as string | null,
			loading: false,
			loadingStats: false,
			result: null as any,
			carrierStats: [] as CarrierSummaryResponse[],
			excludeGenerated: true,
		});

		const showResult = computed(() => !!state.result);

		const openDialog = () => {
			state.excludeGenerated = true; // 默认排除已处理记录
			state.dialogVisible = true;
			state.generateType = 'selected';
			state.result = null;
			loadCarrierStats();
		};

		const handleDialogClosed = () => {
			state.generateType = null;
			state.result = null;
			state.carrierStats = [];
		};

		const handleClose = () => {
			state.dialogVisible = false;
			emit('complete', {
				success: state.result?.succeeded || false,
				data: state.result?.data,
				type: state.generateType,
			});
		};

		const handleGenerateTypeChange = () => {
			loadCarrierStats();
		};

		const loadCarrierStats = () => {
			if (!state.generateType) return;

			state.loadingStats = true;
			state.carrierStats = [];

			if (state.generateType === 'selected') {
				// 只筛选未处理的选中记录
				const selectedRecords = (props.tableRef?.getCheckboxRecords() || []).filter((record) => (state.excludeGenerated ? record.isProcessed === false : true));
				//console.log('Selected records:', selectedRecords);
				state.totalCount = selectedRecords.length;
				const statsMap = new Map<
					string,
					{
						accountId: string;
						accountName: string;
						carrierId: string;
						carrierName: string;
						processedCount: number;
					}
				>();

				selectedRecords.forEach((record) => {
					const accountId = record.accountId || 'unknown';
					const accountName = record.accountName || 'Unknown';
					const carrierId = record.carrierId || 'unknown';
					const carrierName = record.carrierName || 'Unknown';

					if (!statsMap.has(carrierId)) {
						statsMap.set(carrierId, {
							accountId,
							accountName,
							carrierId,
							carrierName,
							processedCount: 0,
						});
					}
					statsMap.get(carrierId)!.processedCount++;
				});

				state.carrierStats = Array.from(statsMap.values());
				state.loadingStats = false;
			} else {
				const request: any = {
					...props.queryParams,
					isProcessed: state.excludeGenerated ? false : undefined,
					pageIndex: undefined,
					pageSize: undefined,
					order: undefined,
					sort: undefined,
				};

				invoiceApi
					.GetCarrierSummary(request)
					.then((response) => {
						if (response.resultCode !== 200) {
							const errorMsg = response.resultMsg || 'Failed to load carrier summary';
							ElMessage.warning(errorMsg);
							console.warn('GetCarrierSummary failed:', response);
							return;
						}
						state.totalCount = response.data?.reduce((sum, item) => sum + item.processedCount, 0) || 0;
						state.carrierStats = response.data || [];
					})
					.catch((error: HandledError) => {
						if (!error.isHandled) {
							const errorCode = error.code;
							const errorMessage = error.message;
							ElMessage.error(errorMessage);
						}
					})
					.finally(() => {
						state.loadingStats = false;
					});
			}
		};

		const confirmGenerate = () => {
			if (!state.generateType) {
				ElMessage.warning('Please select generation type');
				return;
			}

			const request: any = {
				...props.queryParams,
				isProcessed: state.excludeGenerated ? false : undefined,
				generateType: state.generateType,
				pageIndex: undefined,
				pageSize: undefined,
				order: undefined,
				sort: undefined,
			};

			if (state.generateType === 'selected') {
				const selectedUnprocessedRecords = props.tableRef?.getCheckboxRecords()?.filter((record) => (state.excludeGenerated ? record.isProcessed === false : true)) || [];
				request.invoiceIds = selectedUnprocessedRecords.map((row) => row.invoiceId);
			}

			state.loading = true;

			invoiceApi
				.GenerateFileBatch(request)
				.then((response) => {
					if (response.resultCode !== 200) {
						const errorMsg = response.resultMsg || 'File generation failed';
						state.result = {
							succeeded: false,
							resultMsg: errorMsg,
						};
						ElMessage.error(errorMsg);
						console.warn('GenerateFileBatch failed:', response);
						return;
					}

					state.result = {
						succeeded: true,
						data: response.data,
					};
				})
				.catch((error: HandledError) => {
					if (!error.isHandled) {
						const errorCode = error.code;
						const errorMessage = error.message;
						ElMessage.error(errorMessage);
					}
				})
				.finally(() => {
					state.loading = false;
				});
		};

		return {
			...toRefs(state),
			showResult,
			openDialog,
			handleDialogClosed,
			handleClose,
			confirmGenerate,
			handleGenerateTypeChange,
			loadCarrierStats,
		};
	},
});
</script>

<style lang="scss" scoped>
.generate-batch-container {
	position: relative;
}

.generate-batch-dialog {
	.dialog-header {
		display: flex;
		align-items: center;
		gap: 12px;
		margin-bottom: 16px;

		h3 {
			margin: 0;
			font-size: 16px;
			font-weight: 500;
		}

		.tip-text {
			margin: 4px 0 0;
			font-size: 13px;
			color: var(--el-text-color-secondary);
			line-height: 1.5;
		}
	}

	.content-section {
		margin-top: 16px;
		min-height: 200px;
	}

	.radio-group {
		margin-bottom: 16px;
	}

	.info-tip {
		display: flex;
		align-items: center;
		gap: 6px;
		font-size: 12px;
		color: var(--el-text-color-secondary);
		margin-bottom: 16px;

		.el-icon {
			font-size: 14px;
		}

		strong {
			color: var(--el-color-primary);
			font-weight: 500;
		}
	}

	.warning-icon {
		color: var(--el-color-warning);
		margin-left: 4px;
	}

	.carrier-stats {
		max-height: 300px;
		overflow-y: auto;
		border: 1px solid var(--el-border-color-light);
		border-radius: 4px;
		margin-top: 16px;
	}
}

.result-container {
	.result-data {
		max-height: 300px;
		overflow-y: auto;
		margin-top: 16px;
	}

	.error-message {
		color: var(--el-color-error);
		padding: 10px;
		background-color: var(--el-color-error-light-9);
		border-radius: 4px;
		margin-top: 16px;
	}
}

.dialog-footer {
	display: flex;
	justify-content: flex-end;
}

.empty-text {
	padding: 8px 0; /* 上下间距 */
	color: var(--el-text-color-secondary); /* 使用Element UI的次要文本色 */
	font-size: 14px;
	text-align: center;
}
.switch-description {
	font-size: 12px;
	color: var(--el-text-color-secondary);
	line-height: 1.5;
	margin-left: 8px;
	padding: 2px 6px;
	background: var(--el-fill-color-light);
	border-radius: 4px;
	white-space: nowrap;

	/* Optional hover effect */
	&:hover {
		background: var(--el-fill-color);
	}
}
</style>
