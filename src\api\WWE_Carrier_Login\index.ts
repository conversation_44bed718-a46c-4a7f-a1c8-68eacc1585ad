import BaseApi from '/@/api/BaseApi';
import request from '/@/utils/request';

class wWE_Carrier_LoginApi extends BaseApi {
	QueryV2(data: any) {
		return request({
			url: this.baseurl + 'QueryV2',
			method: 'post',
			data,
		});
	}

	CustomFileTypes(params: any) {
		return request({
			url: this.baseurl + 'CustomFileTypes',
			method: 'get',
			params,
		});
	}
	GetAccountCarrierOptions(params: any) {
		return request({
			url: this.baseurl + 'GetAccountCarrierOptions',
			method: 'get',
			params,
		});
	}
}

export default new wWE_Carrier_LoginApi('/api/WWE_Carrier_Login/', 'id');
