.profile-container .el-form-item_btn .el-form-item__content {
	display: flex;
	justify-content: flex-end;
	margin-top: 20px;

	.el-button {
		min-width: 150px;
	}
}

.profile-container .form-control {
	// background-color: #f0f4f7;
	// border-radius: 0.5rem;
	// transition:
	// 	background-color 0.2s,
	// 	border-color 0.4s;
	// border-color: #e8eef3;
	padding: 0 2rem;

	.el-form-item__label {
		margin-bottom: 0px !important;
	}

	.readonly {
		.el-input__wrapper {
			padding: 0px;
			box-shadow: 0 0 0 0px var(--el-input-border-color, var(--el-border-color)) inset;
			background-color: #f4f4f5;
			cursor: not-allowed !important;
		}
	}

	.el-input__inner {
		font-size: 13px;
		font-weight: bold;
		color: rgb(29, 38, 48);
		// color: #062a44;
	}
}

.profile-container .el-alert {
	border-width: 0px;
}

/* 消息列表样式 */
.message-list {
	list-style-type: none;
	/* 使用默认的项目符号 */
	margin-left: 20px;
	/* 列表的左边距 */
	padding-left: 0;
	/* 移除默认的内边距 */
}

.message-item {
	color: #000;
	font-size: 14px;
	height: 20px;
	/* 保持你设置的高度 */
	line-height: 20px;
	/* 设置行高与高度相同，以垂直居中文本 */
	padding: 0 5px;
	/* 如果需要，可以添加一些左右内边距 */
}

/* 错误类型的消息样式 */
.message-item.error {
	color: red;
	/* 错误消息的文字颜色设置为红色 */
}

/* 小点样式 */
.dot {
	margin-right: 2px;
	/* 小点与文本之间的间距 */
	margin-bottom: 3px;
	display: inline-block;
	width: 4px;
	/* 小点的宽度 */
	height: 4px;
	/* 小点的高度 */
	background-color: #000;
	/* 小点的颜色 */
	border-radius: 50%;
	/* 圆形小点 */
}

.custom-header {
	.el-checkbox {
		display: flex;
		height: unset;
	}
}

.button-container {
	.button-wrapper-left {
		display: flex;
		align-items: center;
	}

	.button-wrapper-right {
		display: flex;
		justify-content: flex-end;
		align-items: center;
	}

	.margin-left-small {
		margin-left: 10px;
	}
}

.border-t {
	border-top: 1px solid #e6e6e6;
}

.search-form-card {
	.el-form-item_none_ml {
		.el-form-item__content {
			margin-left: 0px !important;
		}
	}
}

.el-form-item--small .el-form-item__label {
	height: 24px;
	line-height: 30px;
}

.el-form-item--small .el-form-item__content {
	line-height: 30px;
}

.file-preview-dialog .el-dialog__header {
	padding-left: 20px;
	padding-bottom: 0px;
}

.top-container .list-search .el-card  {
	--el-card-padding: 9px !important;
	margin:0px 1px 0px 1px;
	border-radius: 6px !important;
	border-top: none;
}

.top-container .list-search .el-card .el-card__body  {
	padding: 15px 9px 14px 9px !important;
}

.top-container .list-table .el-card  {
	--el-card-padding: 9px !important;
	margin:5px 1px 0px 1px;
	border-radius: 6px !important;
}

.top-container .el-header.table_header  {
	height:33px;
}
