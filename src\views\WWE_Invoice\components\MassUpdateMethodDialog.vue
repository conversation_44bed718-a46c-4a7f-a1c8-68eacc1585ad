<template>
	<div class="mass_update-container">
		<el-dialog v-model="massUpdateDialogVisible" title="Update Document Retrieval Method" width="480px" :close-on-click-modal="false" draggable @closed="handleDialogClosed">
			<div class="mass-update-dialog">
				<div class="dialog-header">
					<div class="header-text">
						<div
							style="background: #fffbe6; color: #ad8b00; border: 1px solid #ffe58f; border-radius: 14px; padding: 14px 18px; margin-bottom: 10px; display: flex; align-items: center; box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08); font-size: 13px; font-weight: 500; justify-content: center">
							Invoices with status of Cancelled / Exception will not be included in the shipment list.
						</div>
					</div>
				</div>

				<el-form class="mt-4 w100">
					<el-form-item required>
						<el-select v-model="massUpdateMethod" placeholder="Select a method" style="width: 100%" clearable size="default">
							<el-option v-for="item in retrievalMethodOptions" :key="item.itemValue" :label="item.itemName" :value="item.itemValue" />
						</el-select>
						<div class="form-tip mt-1">
							<el-icon>
								<InfoFilled />
							</el-icon>
							<span>
								Total selected: <span style="color: #409eff; font-weight: bold">{{ allSelectedCount }}</span
								>; Matched: <span style="color: #67c23a; font-weight: bold">{{ selectedCount }}</span>
							</span>
						</div>
					</el-form-item>

					<div class="selection-summary" v-if="false">
						<el-tag type="info" size="small">
							<el-icon>
								<Tickets />
							</el-icon>
							{{ selectedCount }}
						</el-tag>
					</div>
				</el-form>
			</div>

			<template #footer>
				<div class="dialog-footer">
					<el-button @click="closeDialog">Cancel</el-button>
					<el-button type="primary" @click="confirmMassUpdate" :loading="massUpdateLoading" :disabled="!massUpdateMethod || selectedCount === 0"> Update </el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent, PropType, computed, ref } from 'vue';
import { Document, Tickets, InfoFilled } from '@element-plus/icons-vue';
import type { ElTable } from 'element-plus';
import invoiceApi from '/@/api/WWE_Invoice';
import { ElMessage } from 'element-plus';
import dictItemApi from '/@/api/dictItem';
import { DictionaryOption } from '/@/models';

export default defineComponent({
	name: 'MassUpdateMethodDialog',
	components: { Tickets, InfoFilled },
	props: {
		tableRef: {
			type: Object as PropType<InstanceType<typeof ElTable>>,
			required: true,
		},
	},
	emits: ['complete'],
	setup(props, { emit }) {
		const state = reactive({
			massUpdateDialogVisible: false,
			massUpdateMethod: null as string | number | null,
			massUpdateLoading: false,
			retrievalMethodOptions: [] as DictionaryOption[],
		});

		// 新增：用于存储过滤后的可操作记录和被过滤数量
		const filteredRecords = ref<any[]>([]);
		const filteredCount = ref(0);
		// 新增：总共选择的数量
		const allSelectedCount = ref(0);

		const selectedCount = computed(() => {
			return filteredRecords.value.length;
		});

		const openDialog = async () => {
			state.massUpdateDialogVisible = true;
			state.massUpdateMethod = null;
			state.retrievalMethodOptions = await fetchMethodOptions();
			// 过滤掉 status 为 Cancelled 和 Exception 的记录
			const allSelected = props.tableRef?.getCheckboxRecords?.() || [];
			allSelectedCount.value = allSelected.length;
			filteredRecords.value = allSelected.filter((r) => r.status !== 'Cancelled' && r.status !== 'Exception');
			filteredCount.value = allSelected.length - filteredRecords.value.length;
		};

		const closeDialog = () => {
			state.massUpdateDialogVisible = false;
		};

		const handleDialogClosed = () => {
			state.massUpdateMethod = null;
			filteredRecords.value = [];
			filteredCount.value = 0;
			allSelectedCount.value = 0;
		};

		const fetchMethodOptions = async () => {
			const response = await dictItemApi.Many(['Retrieval Method']);
			if (response?.data) {
				const methodResult = response.data.find((item) => item.dictValue === 'Retrieval Method');
				return methodResult?.items || [];
			}
			return [];
		};

		const confirmMassUpdate = async () => {
			if (!state.massUpdateMethod) {
				ElMessage.warning('Please select a method');
				return;
			}

			const ids = filteredRecords.value.map((r) => r.invoiceId);

			state.massUpdateLoading = true;
			try {
				await invoiceApi.MassUpdateMethod({
					invoiceIds: ids,
					retrievalMethod: state.massUpdateMethod,
				});
				ElMessage.success('Update successful');
				closeDialog();
				emit('complete', { success: true, ids });
			} catch {
				ElMessage.error('Update failed');
				emit('complete', { success: false, ids });
			} finally {
				state.massUpdateLoading = false;
			}
		};

		return {
			...toRefs(state),
			selectedCount,
			filteredCount,
			allSelectedCount,
			openDialog,
			closeDialog,
			handleDialogClosed,
			confirmMassUpdate,
		};
	},
});
</script>

<style lang="scss" scoped>
.mass_update-container {
	position: relative;
}

.mass-update-dialog {
	.dialog-header {
		display: flex;
		align-items: center;
		gap: 12px;
		margin-bottom: 16px;

		.header-icon {
			font-size: 24px;
			color: var(--el-color-primary);
		}

		h3 {
			margin: 0;
			font-size: 16px;
			font-weight: 500;
		}

		.tip-text {
			margin: 4px 0 0;
			font-size: 13px;
			color: var(--el-text-color-secondary);
		}
	}

	.form-tip {
		display: flex;
		align-items: center;
		gap: 6px;
		font-size: 12px;
		color: var(--el-text-color-secondary);

		.el-icon {
			font-size: 14px;
		}
	}

	.selection-summary {
		margin-top: 16px;
		text-align: center;

		.el-tag {
			padding: 4px 10px;

			.el-icon {
				margin-right: 4px;
			}
		}
	}
}

.dialog-footer {
	display: flex;
	justify-content: flex-end;
}
</style>
