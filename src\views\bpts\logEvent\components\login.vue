<template>
	<div class="top-container">
		<div class="list-search">
			<div class="search-form-card" shadow="never">
				<el-form @keyup.enter="onSearch">
					<el-row :gutter="24">
						<el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="11" class="mb1 mt1">
							<el-form-item>
								<el-radio-group v-model="dateType" @change="onTimeChange" size="middle">
									<el-radio-button label="0">today</el-radio-button>
									<el-radio-button label="1">yesterday</el-radio-button>
									<el-radio-button label="2">this week</el-radio-button>
									<el-radio-button label="3">last week</el-radio-button>
									<el-radio-button label="4">this month</el-radio-button>
								</el-radio-group>
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="24" :md="24" :lg="6" :xl="5" class="mb1 mt1">
							<el-form-item>
								<MyDate v-model:input="tableData.param.startTime" v-model="tableData.param.startTime"
									style="width: 125px" />
								&nbsp;
								<el-text>To</el-text>
								&nbsp;
								<MyDate v-model:input="tableData.param.endTime" v-model="tableData.param.endTime"
									style="width: 125px" />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="24" :md="24" :lg="6" :xl="8" class="mb1 mt1">
							<el-form-item label=" ">
								<div class="form_search_btn">
									<el-button type="primary" @click="onSearch" icon="search">
										{{ $t('message.page.buttonSearch') }}
									</el-button>
								</div>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>
			</div>

			<div class="list-table">
				<el-card shadow="never">
					<div class="scTable">
						<div class="scTable-table">
							<el-table ref="tableRef" :data="tableData.data" v-loading="tableData.loading"
								highlight-current-row @row-click="onDetail" border :row-style="{ height: '30px' }"
								:cell-style="{ padding: '0px' }">
								<template #empty>
									<el-empty description="No Data" :image-size="100"></el-empty>
								</template>
								<el-table-column type="selection" />
								<el-table-column label="Login Time" width="150">
									<template #default="{ row }">
										<span>{{ formatStrDate(row.LoginTime) }}</span>
									</template>
								</el-table-column>
								<el-table-column label="Logout Time" width="150">
									<template #default="{ row }">
										<span v-if="formatStrDate(row.LogoutTime) != ''">{{
											formatStrDate(row.LogoutTime)
										}}</span>
									</template>
								</el-table-column>
								<el-table-column label="Username" width="150">
									<template #default="{ row }">
										<span>{{ row.UserName }}</span>
									</template>
								</el-table-column>
								<el-table-column label="Login Status" width="150">
									<template #default="{ row }">
										<span>{{ row.LogStatus }}</span>
									</template>
								</el-table-column>
								<el-table-column label="Client IP" width="150">
									<template #default="{ row }">
										<span>{{ row.ClientIP.replace('::ffff:', '') }}</span>
									</template>
								</el-table-column>
								<el-table-column label="Device" show-overflow-tooltip>
									<template #default="{ row }">
										<span>{{ row.Device }}</span>
									</template>
								</el-table-column>
							</el-table>
						</div>
						<div class="scTable-page">
							<el-pagination @size-change="onSizechange" @current-change="onCurrentChange"
								:pager-count="5" :page-sizes="[20, 30, 50, 100]"
								v-model:current-page="tableData.param.pageIndex" background
								v-model:page-size="tableData.param.pageSize"
								layout="total, sizes, prev, pager, next, jumper" :total="tableData.total"
								small></el-pagination>
						</div>
					</div>
				</el-card>
			</div>

		</div>

		<el-drawer v-model="infoDrawer" title="Information details" :size="700" destroy-on-close>
			<template #header>
				<h3>Information details</h3>
			</template>
			<Detail ref="detailRef"></Detail>
		</el-drawer>
	</div>
</template>

<script lang="ts">
//import { useRouter } from 'vue-router';
import { toRefs, reactive, defineComponent, onMounted, ref } from 'vue';
import { formatStrDate, formatTypeTime } from '/@/utils/formatTime';
import Detail from './detail.vue';
import logEventApi from '/@/api/logEvent/index';
import MyDate from '/@/components/ticket/ticketDate.vue';
import type { TableInstance } from 'element-plus';

export default defineComponent({
	name: 'LoginTab',
	components: { Detail, MyDate },
	setup() {
		const detailRef = ref();
		const state = reactive({
			tableData: {
				data: [],
				total: 0,
				loading: false,
				selection: [],
				param: {
					logType: 'Login',
					pageIndex: 1,
					pageSize: 20,
					searchKey: '',
					order: 'id',
					sort: 'desc', // asc or desc
					startTime: '',
					endTime: '',
				},
			},
			infoDrawer: false,
			detailObj: {},
			dateType: '今天',
			logDate: [new Date(), new Date()], //Desc:更新时间
		});

		//初始化
		const onInit = () => {
			state.tableData.loading = true;
			logEventApi
				.Query(state.tableData.param)
				.then((rs) => {
					state.tableData.total = rs.totalCount;

					var jd: any[] = [];

					rs.data.forEach((item: { message: string }) => {
						//	var d = JSON.parse(item.message);
						jd.push(JSON.parse(item.message));
					});

					state.tableData.data = jd;
				})
				.catch((rs) => { })
				.finally(() => (state.tableData.loading = false));
		};

		//搜索
		const onSearch = () => {
			onInit();
		};

		const tableRef = ref<TableInstance>()
		const selection = ref<[]>([])

		//详细信息
		const onDetail = (row: any) => {
			tableRef.value!.clearSelection()

			tableRef.value!.toggleRowSelection(
				row,
				true
			)

			state.infoDrawer = true;

			setTimeout((a) => {
				detailRef.value.onShow(row, 'Login');
			}, 100);
		};

		// pageSize 改变时触发
		const onSizechange = (val: number) => {
			state.tableData.param.pageSize = val;
			onInit();
		};

		// current-change 改变时触发
		const onCurrentChange = (val: number) => {
			state.tableData.param.pageIndex = val;
			onInit();
		};

		const onTimeChange = (val: any) => {
			state.logDate = formatTypeTime(val);
			if (state.logDate) {
				state.tableData.param.startTime = state.logDate[0];
				state.tableData.param.endTime = state.logDate[1];
				onInit();
			}
		};

		// 页面加载时
		onMounted(() => {
			// setTimeout((a) => {
			onInit();
			// }, 2000);
		});

		return {
			onTimeChange,
			detailRef,
			formatStrDate,
			onInit,
			onDetail,
			onSizechange,
			onCurrentChange,
			onSearch,
			...toRefs(state),
			tableRef
		};
	},
});
</script>
