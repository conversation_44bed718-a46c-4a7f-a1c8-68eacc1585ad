<template>
	<div>
		<el-drawer :title="$t('message.wWE_Shipment_DocumentCommon.ImportData')" v-model="state.isShowDialog" size="500px" :destroy-on-close="true" @close="onCancel">
			<el-form ref="ruleFormRef" :model="state.ruleForm" :rules="state.rules" label-width="150px" label-position="top">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item>
							<div class="upload-container">
								<el-upload ref="uploadExcel" :action="state.actionStr" v-model:file-list="state.excelFileList" :limit="1" :on-exceed="handleExcelExceed" :headers="state.headers" accept=".xlsx" :before-upload="beforeExcelUpload" @change="handleUploadChange">
									<span style="color: red">*</span>
									<el-icon class="uploader-icon">
										<ele-Paperclip />
									</el-icon>
									<span style="line-height: 1.5">
										{{ $t('message.categoryTestCommon.UploadExcelFile') }}
										{{ $t('message.categoryTestCommon.Note') }}
									</span>
								</el-upload>
							</div>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>

			<template #footer>
				<div style="flex: auto; text-align: right; padding: 0 20px 20px 0">
					<el-button @click="onCancel" size="small">{{ $t('message.page.buttonCancel') }}</el-button>
					<el-button :loading="state.loading" type="primary" @click="onSubmit(ruleFormRef)" size="small">{{ $t('message.page.buttonUpload') }}</el-button>
				</div>
			</template>
		</el-drawer>
	</div>
</template>

<script lang="ts" setup>
import { reactive, ref, getCurrentInstance, computed, h } from 'vue';
import type { FormInstance, UploadInstance, UploadProps, UploadRawFile, Action } from 'element-plus';
import { ElMessage, genFileId, ElMessageBox } from 'element-plus';
import { useI18n } from 'vue-i18n';
import { Local } from '/@/utils/storage';
import WWE_Shipment_DocumentApi from '/@/api/WWE_Shipment_Document/index';

const { t } = useI18n();

const ruleFormRef = ref<FormInstance>();
const emit = defineEmits(['refreshData']);
const isUploading = ref(false);

const { proxy } = getCurrentInstance() as any;
const BASE_URL = computed(() => {
	const appSettings = proxy?.$appSettings;
	return appSettings?.BASE_URL || import.meta.env.VITE_API_URL;
});

const state = reactive({
	loading: false,
	isShowDialog: false,
	ruleForm: {
		filesInfo: [] as any,
		NeedValidate: '' as any,
	},
	rules: {},
	excelFileList: [],
	headers: {
		Authorization: `Bearer ${Local.get('token')}`,
	},
	actionStr: `${BASE_URL.value}Api/Files/UpLoadV2`,
});

const onSubmit = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;

	if (state.excelFileList.length === 0) {
		ElMessage.error(t('message.page.PleaseUploadExcelFile'));
		return;
	}

	if (isUploading.value) {
		ElMessage.error(t('message.page.PleaseWaitFileUploading'));
		return;
	}

	await formEl.validate((valid, fields) => {
		if (valid) {
			state.loading = true;
			const obj = state.ruleForm;
			obj.filesInfo = state.excelFileList.map((item: any) => ({
				FileId: item.response.data[0].fileId,
				FileUrl: item.response.data[0].fileUrl,
				OriginalName: item.name,
				Ext: item.name.substring(item.name.lastIndexOf('.') + 1).toLowerCase(),
			}));

			importData(obj, 1);
		} else {
			console.log('Error submit!', fields);
		}
	});
};

function importData(obj: any, needValidate: any) {
	obj.NeedValidate = needValidate;

	WWE_Shipment_DocumentApi.Import(obj)
		.then((rs) => {
			if (rs.data.validateMessage && rs.data.validateMessage.length > 0) {
				const messageArr = [] as any;
				let nextFlag = true;

				rs.data.validateMessage.forEach((element: any) => {
					let message = '';
					if (element.row) {
						message = `${t('message.page.Row')} ${element.row}: ${element.message}`;
					} else {
						message = `${element.message}`;
					}
					messageArr.push(h('span', null, message));
					messageArr.push(h('br'));
					if (element.messageType == 1) {
						nextFlag = false;
					}
				});

				ElMessageBox({
					title: t('message.page.dlgTip'),
					message: h('div', { style: 'max-height: 400px; width: 400px; overflow-y: auto;' }, messageArr),
					confirmButtonText: nextFlag ? t('message.page.buttonContinue') : t('message.page.buttonOk'),
					showCancelButton: nextFlag,
					cancelButtonText: t('message.page.buttonCancel'),
					callback: (action: Action) => {
						if (action === 'confirm' && nextFlag) {
							importData(obj, 0);
						}
					},
				});
			} else {
				ElMessage.success(t('message.page.ImportSuccess'));
				emit('refreshData', 'No');
				closeDialog();
			}
		})
		.catch((error: HandledError) => {
			if (!error.isHandled) {
				const errorCode = error.code;
				const errorMessage = error.message;
				ElMessage.error(errorMessage);
			}
		})
		.finally(() => {
			state.loading = false;
		});
}

const onCancel = () => {
	onInitForm();
	closeDialog();
};

const closeDialog = () => {
	state.isShowDialog = false;
};

const open = async () => {
	state.isShowDialog = true;
	await onInitForm();
};

const onInitForm = async () => {
	state.excelFileList = [];
	resetForm(ruleFormRef.value);
};

const resetForm = (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	formEl.resetFields();
};

const handleUploadChange = (file: any, fileList: any) => {
	if (file.status === 'ready') {
		isUploading.value = true;
	} else if (fileList.every((f: { status: string }) => ['success', 'fail'].includes(f.status))) {
		isUploading.value = false;
	}
};

const beforeExcelUpload = (file: any) => {
	const ext = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase();
	const isXlsx = ext === 'xlsx';
	if (!isXlsx) {
		ElMessage.error(t('message.page.OnlyXlsxFileAllowed'));
	}
	return isXlsx;
};

const uploadExcel = ref<UploadInstance>();

const handleExcelExceed: UploadProps['onExceed'] = (files) => {
	uploadExcel.value!.clearFiles();
	const file = files[0] as UploadRawFile;
	file.uid = genFileId();
	uploadExcel.value!.handleStart(file);
	uploadExcel.value!.submit();
};

defineExpose({
	open,
});
</script>

<style scoped>
.upload-container {
	padding: 20px;
}

.el-drawer__body {
	padding: 20px;
}

.uploader-icon {
	margin-right: 8px;
	vertical-align: middle;
}
</style>
