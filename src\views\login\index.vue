<template>
	<div class="login-container flex">
		<div class="login-left">
			<div class="login-left-logo animate__animated animate__fadeInLeft">
				<!-- <img :src="logoMini" /> -->
				<!-- <img src="/img/Logo_DocXtract.png?v=1.1.1" /> -->
				<img v-if="isLeftImageVisible" :src="getSiteInfo?.logoOutLeft" :width="state.info?.logoOutLeftWidth"
					:height="state.info?.logoOutLeftHeight" />
				<div class="login-left-logo-text">
					<!-- <span>{{ getThemeConfig.globalViceTitle }}</span>
					<span class="login-left-logo-text-msg">{{ getThemeConfig.globalViceTitleMsg }}</span> -->
					<span class="animate__animated animate__fadeInLeft animate__delay-1s">{{ getSiteInfo?.demo }}</span>

				</div>
			</div>
			<div class="login-left-img">
				&nbsp;
				<!-- <img :src="loginMain" /> -->
			</div>
			<div class="login-left-content animate__animated animate__fadeIn animate__delay-2s">
				<h1 class="welcome-text">Welcome</h1>
			</div>
			<!-- 云朵效果 -->
			<div class="clouds-container">
				<div class="cloud cloud1"></div>
				<div class="cloud cloud2"></div>
				<div class="cloud cloud3"></div>
				<div class="cloud cloud4"></div>
			</div>
			<div class="login-left-waves-container">
				<div class="wave wave1"></div>
				<div class="wave wave2"></div>
				<div class="wave wave3"></div>
			</div>
			<div class="login_adv__bottom animate__animated animate__fadeIn animate__delay-3s">
				<p>
					Copyright © <span>{{ new Date().getFullYear() }}</span>&nbsp;
					<a class="copyRightLink" href="http://www.bptsllc.com" target="_blank">BPTS LLC</a>
				</p>
				<p>{{ getSiteInfo.copyright }}</p>
			</div>
			<img :src="loginBg" class="login-left-waves" style="display: none;" />
			<!-- 添加固定云朵 -->
			<div class="fixed-clouds">
				<div class="fixed-cloud cloud-1"></div>
				<div class="fixed-cloud cloud-2"></div>
				<div class="fixed-cloud cloud-3"></div>
				<div class="fixed-cloud cloud-4"></div>
				<div class="fixed-cloud cloud-5"></div>
			</div>
		</div>
		<div class="login-right flex">
			<div class="login-right-warp flex-margin">
				<div class="login-right-content">
					<div class="login-header">
						<div class="login-logo animate__animated animate__fadeInDown">
							<img v-if="isRightImageVisible" :src="getSiteInfo.logoOutRigth"
								:width="getSiteInfo.logoOutRigthWidth" :height="getSiteInfo.logoOutRigthHeight" />
						</div>
						<h1 class="login-title animate__animated animate__fadeInDown animate__delay-1s">Log into
							DocXtract</h1>
						<p class="login-subtitle animate__animated animate__fadeInDown animate__delay-2s">{{
							getSiteInfo.systemName }}</p>
					</div>

					<div class="login-form-container animate__animated animate__fadeIn animate__delay-3s">
						<div v-if="!state.isScan">
							<Account />
						</div>
					</div>

					<div class="login-footer animate__animated animate__fadeIn animate__delay-4s">
						<p class="login-footer-text">
							© {{ new Date().getFullYear() }} BPTS LLC. All rights reserved.
						</p>
					</div>
				</div>
			</div>
		</div>
		<!-- <div class="topRightLogo">WWE Dashboard</div> -->
		<div class="topRightLogo">
			<!-- <img src="/img/wweLogo.png?v=1.1.1" width="300px" /> -->
			<img v-if="isRightImageVisible" :src="getSiteInfo.logoOutRigth" :width="getSiteInfo.logoOutRigthWidth"
				:height="getSiteInfo.logoOutRigthHeight" />
		</div>
	</div>
</template>

<script setup lang="ts" name="loginIndex">
import { defineAsyncComponent, onMounted, reactive, computed, getCurrentInstance, ref } from 'vue';
import { storeToRefs } from 'pinia';
import { useThemeConfig } from '/@/stores/themeConfig';
import { NextLoading } from '/@/utils/loading';
import { useGlobalConfig } from 'element-plus';
// import logoMini from '/@/assets/logo-mini.svg';
// import loginMain from '/@/assets/login-main.svg';
import loginBg from '/@/assets/login-bg.svg';
import configApi from '/@/api/config/index';
import { Siteinfo } from '/@/types/siteConfig';
// 引入组件
const Account = defineAsyncComponent(() => import('/@/views/login/component/account.vue'));
const Mobile = defineAsyncComponent(() => import('/@/views/login/component/mobile.vue'));
const Scan = defineAsyncComponent(() => import('/@/views/login/component/scan.vue'));

const { proxy } = getCurrentInstance() as any;
const BASE_URL = computed(() => {
	const appSettings = proxy?.$appSettings;
	return appSettings?.BASE_URL || import.meta.env.VITE_API_URL;
});
const isProd = BASE_URL.value.indexOf('ticketmgmtapi') > -1 ? true : false;

//webSystemUrl.ENV = 'production'

// 定义变量内容


const storesThemeConfig = useThemeConfig();
const { themeConfig } = storeToRefs(storesThemeConfig);
const state = reactive({
	tabsActiveName: 'account',
	isScan: false,
	info: {} as Siteinfo,
});


// 获取布局配置信息
const getThemeConfig = computed(() => {
	return themeConfig.value;
});
const getSiteInfo = computed(() => {
	return state.info || {};
});

const isLeftImageVisible = ref(true)
const isRightImageVisible = ref(true)

// 页面加载时
onMounted(() => {
	NextLoading.done();
	configApi.GetAppConfig().then((rs) => {
		state.info = rs.data;
		loadImage(state.info.logoOutLeft).then(img => {
			isLeftImageVisible.value = true
		}).catch(err => {
			isLeftImageVisible.value = false
		});

		loadImage(state.info.logoOutRigth).then(img => {
			isRightImageVisible.value = true
		}).catch(err => {
			isRightImageVisible.value = false
		});
	});

	// 更强力的解决方案
	const config = useGlobalConfig();
	config.value.zIndex = 3000;

	// 修改 Element Plus 的全局配置，强制所有弹窗挂载到 body
	if (config.value.dialog) {
		config.value.dialog.appendToBody = true;
	}

	// 添加更强力的全局样式
	const style = document.createElement('style');
	style.innerHTML = `
		body > .el-dialog__wrapper {
			position: fixed !important;
			top: 0 !important;
			right: 0 !important;
			bottom: 0 !important;
			left: 0 !important;
			overflow: auto !important;
			margin: 0 !important;
			z-index: 9999 !important;
			display: flex !important;
			align-items: center !important;
			justify-content: center !important;
		}
		
		body > .el-dialog__wrapper .el-dialog {
			margin: 0 auto !important;
			max-width: 500px !important;
			width: 90% !important;
			border-radius: 8px !important;
			box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
			transform: none !important;
			position: relative !important;
			top: 0 !important;
			left: 0 !important;
		}
		
		body > .el-overlay {
			position: fixed !important;
			top: 0 !important;
			right: 0 !important;
			bottom: 0 !important;
			left: 0 !important;
			z-index: 9998 !important;
		}
		
		/* 确保弹窗内的表单元素正确显示 */
		body > .el-dialog__wrapper .el-form-item {
			margin-bottom: 20px !important;
		}
		
		body > .el-dialog__wrapper .el-input {
			width: 100% !important;
		}
		
		body > .el-dialog__wrapper .el-button {
			min-width: 100px !important;
		}
	`;
	document.head.appendChild(style);

	// 强制将所有弹窗移动到 body 下
	const moveDialogsToBody = () => {
		const dialogs = document.querySelectorAll('.el-dialog__wrapper');
		dialogs.forEach(dialog => {
			// 只处理不是 body 直接子元素的弹窗
			if (dialog.parentElement !== document.body) {
				document.body.appendChild(dialog);
				dialog.setAttribute('style', 'position: fixed !important; z-index: 9999 !important; top: 0 !important; left: 0 !important; right: 0 !important; bottom: 0 !important; display: flex !important; align-items: center !important; justify-content: center !important;');

				// 找到弹窗内部的 dialog 元素并设置样式
				const innerDialog = dialog.querySelector('.el-dialog');
				if (innerDialog) {
					innerDialog.setAttribute('style', 'margin: 0 auto !important; position: relative !important; top: 0 !important; left: 0 !important; transform: none !important;');
				}
			}
		});
	};

	// 初始执行一次
	setTimeout(moveDialogsToBody, 500);

	// 监听点击事件，每次点击后检查是否有新弹窗需要移动
	document.addEventListener('click', () => {
		setTimeout(moveDialogsToBody, 100);
	});

	// 创建一个 MutationObserver 来监视 DOM 变化
	const observer = new MutationObserver((mutations) => {
		setTimeout(moveDialogsToBody, 100);
	});

	// 开始观察 document.body 的子节点变化
	observer.observe(document.body, { childList: true, subtree: true });
});

const loadImage = (src: any) => {
	return new Promise((resolve, reject) => {
		let img = new Image();
		img.onload = () => resolve(img);
		img.onerror = () => reject(new Error('Image load failed'));
		img.src = src;
	});
}

</script>

<style scoped lang="scss">
.login-container {
	height: 100%;
	background: var(--el-color-white);

	// 添加响应式布局
	@media screen and (max-width: 1200px) {
		flex-direction: column;
		background: var(--el-color-white); // 改为白色背景
	}

	.login-left {
		flex: 1;
		position: relative;
		background: linear-gradient(135deg, rgba(78, 113, 189, 1), rgba(32, 52, 104, 0.95));
		margin-right: 100px;
		overflow: hidden;
		display: flex;
		flex-direction: column;
		justify-content: space-between;

		// 平板端和移动端响应式 - 完全隐藏左侧
		@media screen and (max-width: 1200px) {
			display: none;
		}

		.login-left-logo {
			display: flex;
			align-items: center;
			position: relative;
			padding: 30px 0 0 50px;
			z-index: 5;

			img {
				max-height: 60px;
				transition: all 0.3s ease;

				&:hover {
					transform: scale(1.05);
				}
			}

			.login-left-logo-text {
				display: flex;
				flex-direction: column;

				span {
					margin-left: 15px;
					font-size: 24px;
					font-weight: 600;
					color: white;
					text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
					transition: all 0.3s ease;

					&:hover {
						text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
					}
				}
			}
		}

		.login-left-content {
			display: flex;
			justify-content: center;
			align-items: center;
			flex: 1;
			z-index: 5;

			.welcome-text {
				font-size: 60px;
				font-weight: 700;
				color: rgba(255, 255, 255, 0.9);
				text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
				letter-spacing: 2px;
				animation: float 6s ease-in-out infinite;
			}
		}

		// 云朵容器
		.clouds-container {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			overflow: hidden;
			z-index: 2;

			.cloud {
				position: absolute;
				background: rgba(255, 255, 255, 0.2);
				border-radius: 50%;
				filter: blur(20px);
			}

			.cloud1 {
				width: 150px;
				height: 80px;
				top: 15%;
				left: -50px;
				animation: cloudMove 35s linear infinite;
			}

			.cloud2 {
				width: 200px;
				height: 100px;
				top: 40%;
				left: -80px;
				animation: cloudMove 45s linear infinite;
				animation-delay: -10s;
			}

			.cloud3 {
				width: 120px;
				height: 60px;
				top: 65%;
				left: -40px;
				animation: cloudMove 30s linear infinite;
				animation-delay: -5s;
			}

			.cloud4 {
				width: 180px;
				height: 90px;
				top: 25%;
				left: -60px;
				animation: cloudMove 40s linear infinite;
				animation-delay: -20s;
			}
		}

		// 波浪容器
		.login-left-waves-container {
			position: absolute;
			bottom: 0;
			left: 0;
			width: 100%;
			height: 100%;
			overflow: hidden;
			z-index: 1;

			.wave {
				position: absolute;
				bottom: 0;
				left: 0;
				width: 200%;
				height: 100%;
				background: url('data:image/svg+xml;utf8,<svg viewBox="0 0 1440 320" xmlns="http://www.w3.org/2000/svg"><path fill="white" fill-opacity="0.1" d="M0,192L48,197.3C96,203,192,213,288,229.3C384,245,480,267,576,250.7C672,235,768,181,864,181.3C960,181,1056,235,1152,234.7C1248,235,1344,181,1392,154.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>');
				background-repeat: repeat-x;
				background-position: 0 bottom;
				background-size: 50% 100px;
				animation: wave 15s linear infinite;
			}

			.wave1 {
				opacity: 0.3;
				animation-delay: 0s;
				animation-duration: 15s;
			}

			.wave2 {
				opacity: 0.2;
				animation-delay: -5s;
				animation-duration: 20s;
			}

			.wave3 {
				opacity: 0.1;
				animation-delay: -2s;
				animation-duration: 25s;
			}
		}

		.login_adv__bottom {
			position: relative;
			padding: 20px 50px;
			z-index: 5;
			background: rgba(0, 0, 0, 0.1);
			backdrop-filter: blur(5px);

			p {
				margin: 5px 0;
				color: rgba(255, 255, 255, 0.7);
				font-size: 14px;
				text-align: center;
			}

			.copyRightLink {
				color: white;
				text-decoration: none;
				font-weight: 500;
				transition: all 0.3s ease;

				&:hover {
					text-decoration: underline;
				}
			}
		}

		// 添加固定云朵
		.fixed-clouds {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			z-index: 1;
			pointer-events: none;

			.fixed-cloud {
				position: absolute;
				background: rgba(255, 255, 255, 0.15);
				border-radius: 50%;
				filter: blur(15px);
			}

			.cloud-1 {
				width: 180px;
				height: 100px;
				top: 10%;
				left: 15%;
			}

			.cloud-2 {
				width: 150px;
				height: 80px;
				top: 30%;
				right: 20%;
			}

			.cloud-3 {
				width: 200px;
				height: 110px;
				bottom: 35%;
				left: 25%;
			}

			.cloud-4 {
				width: 120px;
				height: 70px;
				top: 60%;
				right: 30%;
			}

			.cloud-5 {
				width: 160px;
				height: 90px;
				bottom: 15%;
				left: 40%;
			}
		}
	}

	.login-right {
		width: 60%;
		display: flex;
		justify-content: center;
		align-items: center;

		// 平板和移动端响应式
		@media screen and (max-width: 1200px) {
			width: 100%;
			padding: 40px 0;
			min-height: 100vh; // 确保在小屏幕上占满整个视口高度
		}

		@media screen and (max-width: 768px) {
			padding: 30px 0;
		}

		.login-right-warp {
			width: 500px;
			background-color: var(--el-color-white);
			border-radius: 12px;
			box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
			overflow: hidden;
			padding: 40px;
			transition: all 0.3s ease;
			position: relative;

			// 平板和移动端响应式
			@media screen and (max-width: 1200px) {
				width: 80%;
				max-width: 450px;
				margin: auto; // 在小屏幕上居中
				box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05); // 减轻阴影
			}

			@media screen and (max-width: 768px) {
				width: 90%;
				padding: 50px 40px !important;
				box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03); // 进一步减轻阴影
			}

			// 移除顶部线条
			&:before {
				display: none; // 隐藏顶部线条
			}

			&:hover {
				box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
			}

			.login-right-content {
				display: flex;
				flex-direction: column;
				height: 100%;

				.login-header {
					text-align: center;
					margin-bottom: 30px;

					@media screen and (max-width: 768px) {
						margin-bottom: 20px;
					}

					.login-logo {
						margin-bottom: 20px;

						@media screen and (max-width: 768px) {
							margin-bottom: 15px;
						}

						img {
							max-height: 60px;
							object-fit: contain;
							transition: all 0.3s ease;

							@media screen and (max-width: 768px) {
								max-height: 45px;
							}

							&:hover {
								transform: scale(1.05);
							}
						}
					}

					.login-title {
						font-size: 28px;
						font-weight: 600;
						color: var(--el-color-primary);
						margin-bottom: 10px;
						font-family: 'Arial', sans-serif;
						position: relative;
						display: inline-block;

						@media screen and (max-width: 768px) {
							font-size: 24px;
							margin-bottom: 8px;
						}

						&:after {
							content: '';
							position: absolute;
							bottom: -5px;
							left: 50%;
							width: 0;
							height: 2px;
							background: var(--el-color-primary);
							transition: all 0.3s ease;
							transform: translateX(-50%);
						}

						&:hover:after {
							width: 50%;
						}
					}

					.login-subtitle {
						font-size: 16px;
						color: var(--el-text-color-secondary);
						margin-bottom: 0;
						transition: all 0.3s ease;

						@media screen and (max-width: 768px) {
							font-size: 14px;
						}

						&:hover {
							color: var(--el-color-primary);
						}
					}
				}

				.login-form-container {
					flex: 1;
					margin-bottom: 30px;
					transition: all 0.3s ease;

					@media screen and (max-width: 768px) {
						margin-bottom: 20px;
					}

					&:hover {
						// Remove this transform
						// transform: translateY(-2px);
					}

					// 添加这些样式来修复移动端表单间距问题
					:deep(.el-form) {
						.el-form-item {
							// margin-bottom: 30px !important;

							@media screen and (max-width: 768px) {
								margin-bottom: 16px;
							}
						}

						.el-input {
							margin-bottom: 15px !important;
						}

						.el-button {
							margin-top: 10px;

							@media screen and (max-width: 768px) {
								margin-top: 15px;
							}
						}
					}

					// Add these styles to make validation messages visible
					:deep(.el-form-item__error) {
						position: static !important;
						display: block !important;
						margin-top: -10px !important;
						margin-bottom: 10px !important;
						color: #f56c6c !important;
						font-size: 12px !important;
						visibility: visible !important;
						opacity: 1 !important;
					}

					// Target the specific error messages in the login form
					:deep(.login-form-container .el-form-item__error) {
						position: static !important;
						display: block !important;
						visibility: visible !important;
					}

					// Target any custom error message classes
					:deep(.error-message),
					:deep(.validation-error),
					:deep(.form-error) {
						color: #f56c6c !important;
						display: block !important;
						margin-top: 4px !important;
						font-size: 12px !important;
						visibility: visible !important;
					}

					// Ensure the placeholder text for validation is visible
					:deep(input::placeholder) {
						color: #909399 !important;
						opacity: 1 !important;
					}

					// Add specific styling for the validation text that appears below inputs
					:deep(.el-form-item__error),
					:deep(.el-form-item__error--inline) {
						position: static !important;
						padding-top: 4px !important;
						margin-bottom: 5px !important;
					}
				}

				// Add specific styling for the validation messages
				:deep(.form-error-msg) {
					color: #f56c6c !important;
					font-size: 12px !important;
					line-height: 1.2 !important;
					margin-top: 4px !important;
					display: block !important;
					visibility: visible !important;
				}

				.login-footer {
					text-align: center;

					.login-footer-text {
						font-size: 12px;
						color: var(--el-text-color-secondary);
						transition: all 0.3s ease;

						@media screen and (max-width: 768px) {
							font-size: 11px;
						}

						&:hover {
							color: var(--el-color-primary);
						}
					}
				}
			}
		}
	}

	// 隐藏顶部右侧Logo
	.topRightLogo {
		display: none;
	}
}

.login_adv__bottom {
	position: absolute;
	left: 0px;
	right: 0px;
	bottom: 0px;
	// color: #b3b0b0;
	padding: 40px 20px;

	// background-image: linear-gradient(transparent, #000);
	.copyRightLink {
		color: #2a72c5;
		text-decoration: none;
	}
}

@media screen and (max-width: 1370px) {
	.login-container {
		.login-right {
			.login-right-warp {
				.login-right-warp-mian {
					.login-right-warp-main-title {
						height: auto;
					}
				}
			}
		}
	}
}

// 添加动画关键帧
@keyframes fadeIn {
	from {
		opacity: 0;
		transform: translateY(20px);
	}

	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes pulse {
	0% {
		transform: scale(1);
	}

	50% {
		transform: scale(1.05);
	}

	100% {
		transform: scale(1);
	}
}

// 添加动画类
.animate__animated {
	animation-duration: 1s;
	animation-fill-mode: both;
}

.animate__fadeIn {
	animation-name: fadeIn;
}

.animate__fadeInDown {
	animation-name: fadeIn;
}

.animate__pulse {
	animation-name: pulse;
}

.animate__delay-1s {
	animation-delay: 0.2s;
}

.animate__delay-2s {
	animation-delay: 0.4s;
}

.animate__delay-3s {
	animation-delay: 0.6s;
}

.animate__delay-4s {
	animation-delay: 0.8s;
}

// 添加波浪动画
@keyframes wave {
	0% {
		transform: translateX(0);
	}

	100% {
		transform: translateX(-50%);
	}
}

// 添加云朵动画
@keyframes cloudMove {
	0% {
		transform: translateX(0);
		opacity: 0;
	}

	10% {
		opacity: 0.8;
	}

	90% {
		opacity: 0.8;
	}

	100% {
		transform: translateX(calc(100vw + 200px));
		opacity: 0;
	}
}

// 添加浮动动画
@keyframes float {
	0% {
		transform: translateY(0px);
	}

	50% {
		transform: translateY(-15px);
	}

	100% {
		transform: translateY(0px);
	}
}

// 使用全局选择器确保样式应用到所有弹窗
:global(body > .el-dialog__wrapper) {
	position: fixed !important;
	top: 0 !important;
	right: 0 !important;
	bottom: 0 !important;
	left: 0 !important;
	overflow: auto !important;
	margin: 0 !important;
	z-index: 9999 !important;
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
}

:global(body > .el-dialog__wrapper .el-dialog) {
	margin: 0 auto !important;
	max-width: 500px !important;
	width: 90% !important;
	border-radius: 8px !important;
	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
	transform: none !important;
	position: relative !important;
	top: 0 !important;
	left: 0 !important;
}

:global(body > .el-overlay) {
	position: fixed !important;
	top: 0 !important;
	right: 0 !important;
	bottom: 0 !important;
	left: 0 !important;
	z-index: 9998 !important;
}

:global(body > .el-message) {
	z-index: 9999 !important;
}

// Add this to the style section to ensure consistent form spacing
.login-form-container {

	// Ensure consistent spacing for form elements regardless of screen size
	:deep(.el-form) {
		.el-form-item {
			// margin-bottom: 30px !important;
		}

		.el-input {
			margin-bottom: 15px !important;
		}

		// Ensure error messages don't compress the layout
		.el-form-item__error {
			position: absolute;
			padding-top: 2px;
		}
	}
}

// Add a min-height to input fields to ensure they're consistently sized
:deep(.el-input__wrapper) {
	min-height: 45px !important;
}
</style>
