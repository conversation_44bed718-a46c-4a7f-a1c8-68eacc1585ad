<template>
	<span>
		<el-popover placement="bottom" trigger="hover" :width="260">
			<template #reference>
				<el-button type="primary" icon="ele-Filter">{{ $t('message.page.filterColumn') }}</el-button>
			</template>
			<div class="tree-container">
				<el-tree
					ref="treeExportRef"
					v-model="state.selectTreeValue"
					:data="state.selectTreeData"
					node-key="value"
					show-checkbox
					highlight-current
					default-expand-all
					check-on-click-node
					:expand-on-click-node="false"
					:default-checked-keys="state.selectDefaultTreeValue" />
			</div>
		</el-popover>
	</span>

	<el-dropdown>
		<el-button type="primary" class="ml10" icon="download">
			<!-- <el-icon><ele-ArrowDownBold /></el-icon> -->
			{{ $t('message.page.buttonExport') }}
		</el-button>
		<template #dropdown>
			<el-dropdown-menu>
				<el-dropdown-item @click="onExportRecord('excel')">Export Excel</el-dropdown-item>
				<el-dropdown-item @click="onExportRecord('csv')">Export CSV</el-dropdown-item>
			</el-dropdown-menu>
		</template>
	</el-dropdown>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import exportApi from '/@/api/export/index';
import invoiceApi from '/@/api/WWE_Invoice/index';
import { formatDateTime } from '/@/utils/formatTime';
import { ElMessage } from 'element-plus';

interface TreeNode {
	value: string;
	label: string;
	Id: string;
	children: null;
}

const props = defineProps<{
	tableType: number;
}>();

const treeExportRef = ref();

const state = reactive({
	selectTreeData: [],
	selectTreeValue: [] as any[],
	selectDefaultTreeValue: [] as any[],
	exportName: '',
	exportParams: {},
});

onMounted(() => {
	//state.exportName = propInfo.pageName;
	//state.exportParams=propInfo.pageParams;
	//console.log("state.exportName :",state.exportName );
	//let obj={entityName:propInfo.pageName,columnId:propInfo.pageById};
	//getColumns(obj);
});

const getColumns = async (obj: any) => {
	state.exportName = obj.entityName;
	state.exportParams = obj.pageParams;
	let exportIds: string[] = [];

	// await exportApi
	// 	.GetEntityColumns(obj)
	// 	.then((rs: any) => {
	//
	// 		console.log(rs);
	// 		state.selectTreeData = rs.data;
	//
	// 		rs.data.forEach((element: { value: any; nodeChecked: any }) => {
	// 			if (element.nodeChecked) {
	// 				exportIds.push(element.value);
	// 			}
	// 		});
	// 	})
	// 	.catch(() => {})
	// 	.finally();

	await invoiceApi.GetTableColumns({tableType: props.tableType})
		.then((rs: any) => {
			state.selectTreeData = rs.data.map((item:any) => ({ label: item, value: item }))

			exportIds = rs.data;
		})
		.catch(() => {})
		.finally();

	// state.selectTreeValue = exportIds;
	state.selectDefaultTreeValue = exportIds;
};

const onExportRecord = (exportType: string) => {
	let selectTreeCheck = treeExportRef.value?.getCheckedNodes(true);

	let obj = {
		exportType: exportType,
		name: state.exportName,
		colNames: {},
		WWE_SowPageQueryDto: state.exportParams,
		InvoicePageQueryDto: state.exportParams,
		WWE_TestPageQueryDto: state.exportParams,
		OrganizationPageQueryDto: state.exportParams,
		cmCompanyContactPageQueryDto: state.exportParams,
		cmCompanyContactAuditPageQueryDto: state.exportParams,
		CCB_TestPageQueryDto: state.exportParams,
		CategoryPageQueryDto: state.exportParams,
		CategoryGLCodePageQueryDto: state.exportParams,
		GLCodeExpensePageQueryDto: state.exportParams,
		GLCodeInvoicePageQueryDto: state.exportParams,
		GLCodeOrganizationPageQueryDto: state.exportParams,
		CategoryOrganizationPageQueryDto: state.exportParams,
		ReceiptPageQueryDto: state.exportParams,
		ReceiptImagePageQueryDto: state.exportParams,
		CategoryTestPageQueryDto: state.exportParams,
		WWE_Shipment_DocumentPageQueryDto: state.exportParams,
		WWE_CarrierPageQueryDto: state.exportParams,
		WWE_AccountPageQueryDto: state.exportParams,
		WWE_Carrier_LoginPageQueryDto: state.exportParams,
		WWE_ShipmentPageQueryDto: state.exportParams,
		// WWE_InvoicePageQueryDto: state.exportParams,
		WWE_ImagesPageQueryDto: state.exportParams,
		WWE_Image_BatchsPageQueryDto: state.exportParams,
	};

	const newObj: Record<string, any> = {
		exportType: exportType,
		tableType: props.tableType,
		name: state.exportName,
		colNames: {},
	};

	if (obj.name) {
		// 检查 obj.name 是否存在且不为空
		newObj[(obj.name as string) + 'PageQueryDto'] = state.exportParams;
	}

	let cols: string[] = [];

	selectTreeCheck.forEach((item: any) => {
		cols.push(item.value);
	});

	if (cols.length == 0) {
		ElMessage.error('Please select the export column.');
		return;
	}

	obj.colNames = cols;
	newObj.colNames = cols;

	getExport(newObj);
};

const getExport = async (exportInfo: any) => {
	await invoiceApi
		.Export(exportInfo)
		.then((rs: any) => {
			downloadCallback(rs, exportInfo.exportType);
		})
		.catch(() => {})
		.finally();
};

const downloadCallback = (rs: any, exportType: string) => {
	let data = rs;

	console.log('data:', data);

	var newBlob = new Blob([data.data], { type: 'text/plain;charset=UTF-8' });
	var anchor = document.createElement('a');
	var name = state.exportName;
	if (state.exportName == 'WWE_Image_BatchsInfo') {
		name = 'WWE_Image_BatchsInfo';
	}

	if (state.exportName == 'WWE_ImagesInfo') {
		name = 'WWE_ImagesInfo';
	}

	if (state.exportName == 'WWE_InvoiceInfo') {
		name = 'WWE_InvoiceInfo';
	}

	if (state.exportName == 'WWE_ShipmentInfo') {
		name = 'WWE_ShipmentInfo';
	}

	if (state.exportName == 'WWE_Carrier_LoginInfo') {
		name = 'WWE_Carrier_LoginInfo';
	}

	if (state.exportName == 'WWE_AccountInfo') {
		name = 'WWE_AccountInfo';
	}

	if (state.exportName == 'WWE_CarrierInfo') {
		name = 'WWE_CarrierInfo';
	}

	if (state.exportName == 'WWE_Shipment_DocumentInfo') {
		name = 'WWE_Shipment_Document';
	}

	if (state.exportName == 'CategoryTest') {
		name = 'CategoryTest';
	}

	if (state.exportName == 'ReceiptImage') {
		name = 'ReceiptImage';
	}

	if (state.exportName == 'Receipt') {
		name = 'Receipt';
	}

	if (state.exportName == 'Category') {
		name = 'Category';
	}

	if (state.exportName == 'GLCodeExpense') {
		name = 'GLCodeExpense';
	}

	if (state.exportName == 'GLCodeInvoice') {
		name = 'GLCodeInvoice';
	}

	if (state.exportName == 'CategoryGLCode') {
		name = 'CategoryGLCode';
	}

	if (state.exportName == 'CategoryOrganization') {
		name = 'CategoryOrganization';
	}

	if (state.exportName == 'GLCodeOrganization') {
		name = 'GLCodeOrganization';
	}

	if (state.exportName == 'CCB_Test') {
		name = 'CCB_Test';
	}

	if (state.exportName == 'cmCompanyContact') {
		name = 'cmCompanyContact';
	}

	if (state.exportName == 'cmCompanyContactAudit') {
		name = 'cmCompanyContactAudit';
	}

	if (state.exportName == 'Organization') {
		name = 'Organization';
	}

	if (state.exportName == 'WWE_Test') {
		name = 'WWE_Test';
	}

	if (state.exportName == 'Invoice') {
		name = 'Invoice';
	}

	if (state.exportName == 'WWE_Sow') {
		name = 'WWE_Sow';
	}

	if (exportType == 'excel') {
		anchor.download = name + '_' + formatDateTime() + '.xlsx';
	} else {
		anchor.download = name + '_' + formatDateTime() + '.csv';
	}

	anchor.href = window.URL.createObjectURL(newBlob);
	anchor.click();
};

defineExpose({
	getColumns,
});
</script>

<style scoped>
.tree-container {
	max-height: 400px;
	overflow-y: auto;
}
</style>
