<template>
	<div class="config-index-container scrolly layout-padding w100">
		<el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="100px" class="mt35 mb35">
			<el-row :gutter="35">
				<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
					<el-form-item label="Issuer" prop="issuer">
						<el-input v-model="ruleForm.issuer" placeholder="请输入issuer" clearable></el-input>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
					<el-form-item label="Audience" prop="audience">
						<el-input v-model="ruleForm.audience" placeholder="请输入expires" clearable></el-input>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
					<el-form-item label="Secret" prop="audience">
						<el-input v-model="ruleForm.secret" placeholder="请输入expires" clearable></el-input>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
					<el-form-item label="有效时间" prop="tokenExp">
						<el-input v-model="ruleForm.tokenExp" placeholder="请输入expires" clearable></el-input>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
					<el-form-item label="验证的方式" prop="tokenValid">
						<el-select v-model="ruleForm.tokenValid" placeholder="请选择" clearable class="w100">
							<el-option label="URL方式" :value="0"></el-option>
							<el-option label="HTTP头部" :value="1"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
					<el-form-item label="登录方式" prop="tokenMode">
						<el-select v-model="ruleForm.tokenMode" placeholder="请选择" clearable class="w100">
							<el-option label="单一登录" :value="0"></el-option>
							<el-option label="同时登录" :value="1"></el-option>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
					<el-form-item>
						<el-button type="primary" @click="onSave" size="small">保 存</el-button>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
	</div>
</template>

<script lang="ts">
import { toRefs, reactive, onMounted, defineComponent, getCurrentInstance, watch } from 'vue';
import { ElMessage } from 'element-plus';
import configApi from '/@/api/config/index';

export default defineComponent({
	name: 'tokenCom',
	components: {},
	props: {
		tokenObj: {
			type: Object,
			default: {},
		},
	},
	setup(props: any) {
		const { proxy } = getCurrentInstance() as any;
		const state = reactive({
			loading: false,
			ruleForm: { tokenExp: 20000, tokenMode: 0, tokenValid: 1 },
			rules: {
				issuer: [{ required: true, message: 'Please input', trigger: 'blur' }],
				audience: [{ required: true, message: 'Please input', trigger: 'blur' }],
				tokenExp: [{ required: true, message: 'Please input', trigger: 'blur' }],
				tokenMode: [{ required: true, message: 'Please select', trigger: 'change' }],
				tokenValid: [{ required: true, message: 'Please select', trigger: 'change' }],
			},
		});

		const onSave = () => {
			proxy.$refs.ruleFormRef.validate((valid: boolean) => {
				if (!valid) {
					return;
				}
				var obj = { Token: state.ruleForm };
				state.loading = true;
				configApi
					.SaveConfig(obj)
					.then(() => {
						ElMessage.success('Saved Successfully');
					})
					.finally(() => {
						state.loading = false;
					});
			});
		};
		watch(
			() => props.tokenObj,
			() => {
				state.ruleForm = props.tokenObj || {};
				//console.log('1', state.ruleForm);
			}
		);
		// 页面加载时
		onMounted(() => {
			// console.log(state.ruleForm)
		});
		return {
			onSave,
			...toRefs(state),
		};
	},
});
</script>
