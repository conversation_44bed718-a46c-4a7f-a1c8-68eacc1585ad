<template>
	<div class="system-edit-menu-container">
		<el-drawer v-model="state.showDialog" destroy-on-close :title="title" draggable :close-on-click-modal="false" :close-on-press-escape="false" width="500px" :direction="direction">
			<div class="drawer-content">
				<el-form :model="state.form" ref="formRef" :rules="state.rules" label-width="130px" label-position="top">
					<el-row :gutter="35">
						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.wWE_Shipment_FileFields.ShipmentFileId')" prop="shipmentFileId">
								<el-input v-model="state.form.shipmentFileId" clearable size="default" />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.wWE_Shipment_FileFields.FileId')" prop="fileId">
								<el-input v-model="state.form.fileId" clearable size="default" />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.wWE_Shipment_FileFields.CarrierId')" prop="carrierId">
								<el-input v-model="state.form.carrierId" clearable size="default" />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.wWE_Shipment_FileFields.ShipmentId')" prop="shipmentId">
								<el-input v-model="state.form.shipmentId" clearable size="default" />
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>

				<div class="drawer-footer">
					<span class="dialog-footer">
						<el-button @click="onCancel" size="small">{{ $t('message.page.buttonCancel') }}</el-button>
						<el-button :loading="state.loading" type="primary" @click="onSubmit" size="small">{{ $t('message.page.buttonSave') }}</el-button>
					</span>
				</div>
			</div>
		</el-drawer>
	</div>
</template>

<script lang="ts" setup name="wWE_Shipment_File/createOrEdit">
import { reactive, toRefs, ref, getCurrentInstance, computed, watch } from 'vue';

import { cloneDeep } from 'lodash-es';

import { useI18n } from 'vue-i18n';

import { ElMessageBox, ElMessage } from 'element-plus';

import type { DrawerProps } from 'element-plus';

import { formatNumberWithDecimal, formatDecimal } from '/@/utils/toolsValidate';
import DateSelector from '/@/components/common/DateSelector.vue';

import wWE_Shipment_FileApi from '/@/api/WWE_Shipment_File/index';

interface CreateOrEditInput {
	shipmentFileId: number;
	fileId: number;
	carrierId: number;
	shipmentId: number;
}

defineProps({
	title: {
		type: String,
		default: '',
	},
});

const emit = defineEmits(['refreshData']);

const direction = ref<DrawerProps['direction']>('rtl');

const { t } = useI18n();
const formRef = ref();

const state = reactive({
	showDialog: false,
	loading: false,
	form: {} as CreateOrEditInput,
	rules: {
		shipmentFileId: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: ['blur', 'change'] }],
		fileId: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: ['blur', 'change'] }],
		carrierId: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: ['blur', 'change'] }],
		shipmentId: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: ['blur', 'change'] }],
	},
});

const open = async (row: CreateOrEditInput) => {
	let formData = cloneDeep(row) as CreateOrEditInput;

	state.form = formData;

	state.showDialog = true;
};

const onCancel = () => {
	closeDialog();
};

const closeDialog = () => {
	onInitForm();
	state.showDialog = false;
};

const onSubmit = async () => {
	formRef.value.validate(async (valid: boolean) => {
		if (!valid) return;

		state.loading = true;

		let obj = state.form;

		await wWE_Shipment_FileApi
			.Save(obj)
			.then(() => {
				ElMessage.success(t('message.page.Success'));
				emit('refreshData');

				closeDialog();
				state.loading = false;
			})
			.catch((error: HandledError) => {
				if (!error.isHandled) {
					const errorCode = error.code;
					const errorMessage = error.message;
					ElMessage.error(errorMessage);
				}
			})
			.finally(() => {
				state.loading = false;
			});
	});
};

const onInitForm = async () => {};

defineExpose({
	open,
});
</script>

<style scoped>
.drawer-content {
	padding: 20px;
}

.drawer-footer {
	text-align: right;
}
</style>
