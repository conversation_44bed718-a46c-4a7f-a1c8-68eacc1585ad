<template>
	<el-card shadow="never">
		<div>
			<el-tabs v-model="activeName">
				<el-tab-pane label="Site" name="basic">
					<Basic ref="basicRef" :basicObj="configData.info" />
				</el-tab-pane>
				<el-tab-pane label="Upload" name="upload" v-if="false">
					<Upload ref="uploadRef" :uploadObj="configData.upload" />
				</el-tab-pane>
				<el-tab-pane label="Security" name="security" v-if="false">
					<Token ref="tokenRef" :tokenObj="configData.token" />
				</el-tab-pane>
				<el-tab-pane label="Connections" name="email">
					<Third ref="thirdRef" :smsObj="configData.sms" :emailObj="configData.email"
						:sftpObj="configData.sftp" />
				</el-tab-pane>
				<el-tab-pane label="Toggles" name="toggles">
					<Toggles ref="togglesRef" :toggleObj="configData.toggles" />
				</el-tab-pane>
			</el-tabs>
		</div>
	</el-card>
</template>

<script lang="ts">
import { toRefs, reactive, onMounted, ref, defineComponent } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import Basic from './components/basic.vue';
import Token from './components/token.vue';
import configApi from '/@/api/config/index';
import Third from './components/third.vue';
import Upload from './components/upload.vue';
import Toggles from './components/toggles.vue';

export default defineComponent({
	name: 'systemSetting',
	components: { Basic, Token, Third, Upload, Toggles },
	setup() {
		//const addUserRef = ref();

		const state = reactive({
			activeName: 'basic',
			configData: {
				info: {},
				token: {},
				sms: {},
				email: {},
				toggles: {},
				sftp: {},
			},
		});

		// 页面加载时
		onMounted(() => {
			configApi.GetConfig().then((rs) => {
				state.configData = rs.data;
			});
		});
		return {
			...toRefs(state),
		};
	},
});
</script>

<style scoped lang="scss">
.config-index-container {
	padding: 10px;
}
</style>
