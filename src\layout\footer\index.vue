<template>
	<div class="layout-footer mt1" :style="layoutFooterImg">
		<div class="layout-footer-warp">
			<div>
				<p>
					Copyright © <span>{{ new Date().getFullYear() }}</span>&nbsp;
					<a id="ctl00_ctlCopyright_lnkTGS" class="copyRightLink" href="http://www.bptsllc.com"
						target="_blank">BPTS LLC</a>
				</p>
			</div>
			<div class="mt5">
				<p>
					<span class="sCopyRight" style="width: 560px">{{ state.info.copyright }}
					</span>
				</p>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts" name="layoutFooter">
// 此处需有内容（注释也得），否则缓存将失败
import { onMounted, reactive, computed, } from 'vue';
import { Siteinfo } from '/@/types/siteConfig';
import configApi from '/@/api/config/index';
const state = reactive({
	info: {} as Siteinfo,
});
const layoutFooterImg = computed(() => {
	let footImg = state.info?.logoInFooter;
	let footPath = '';
	if (footImg) {
		footPath = "background: no-repeat url('" + footImg + "') " + state.info?.logoInFooterWidth + "% " + state.info?.logoInFooterHeight + "%";
		//footPath = "background: no-repeat url('" + footImg + "') 98% 30%";
	}
	//console.log("footPath:",footPath);
	return footPath;

});

onMounted(() => {
	configApi.GetAppConfig().then((rs) => {
		state.info = rs.data;
	});

});

</script>

<style scoped lang="scss">
.layout-footer {
	width: 100%;
	display: flex;
	padding-top: 30px;
	// background: no-repeat url('../img/poweredby02.png') 98% 30%;
	font-weight: bold;
	font-size: 12px;

	&-warp {
		margin: auto;
		//color: var(--el-text-color-secondary);
		color: #000;
		text-align: center;
		animation: error-num 1s ease-in-out;
	}
}

.login_bottom {
	position: absolute;
	bottom: 10px;
	right: 20px;
}

.copyRightLink {
	color: #2a72c5;
	text-decoration: none;
}
</style>
