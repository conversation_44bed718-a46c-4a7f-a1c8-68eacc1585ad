import BaseApi from '/@/api/BaseApi';
import { UploadFormDataStructure, ImportRequest } from '/@/models/WWEImagesDto';
import request from '/@/utils/request';

class wWE_ImagesApi extends BaseApi {
  QueryV2(data: any) {
    return request({
      url: this.baseurl + 'QueryV2',
      method: 'post',
      data,
    });
  }
  upload(data: UploadFormDataStructure) {
    const formData = new FormData();
    formData.append('file', data.file);
    formData.append('batchNumber', data.batchNumber);
    if (data.receivingType) {
      formData.append('receivingType', data.receivingType);
    }
    if (data.priority) {
      formData.append('priority', data.priority);
    }
    if (data.queue) {
      formData.append('queue', data.queue.toString());
    }
    return request({
      url: this.baseurl + 'upload',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }
  importBySFTP(data: ImportRequest) {
    return request({
      url: this.baseurl + 'import-invoice-by-sftp',
      method: 'post',
      data,
    });
  }
  ImportInvoiceFromLocal(data: any) {
    return request({
      url: this.baseurl + 'import-invoice-from-local',
      method: 'post',
      data,
    });
  }
  getValidImagesInfo(priority: string, receivingType: string) {
    return request({
      url: this.baseurl + 'valid-images-info',
      method: 'get',
      params: {
        priority,
        receivingType,
      },
    });
  }
	restore(id: string) {
		return request({
			url: this.baseurl + 'restore/' + id,
			method: 'post',
		});
	}
}

export default new wWE_ImagesApi('/api/WWE_Images/', 'id');
