<template>
	<div class="top-container">
		<div class="list-search">
			<div class="search-form-card" shadow="never">
				<el-form @keyup.enter="onSearch">
					<el-row :gutter="24">
						<el-col :xs="24" :sm="24" :md="24" :lg="6" :xl="5" class="mb1 mt1">
							<el-form-item>
								<el-select v-model="tableData.param.dirName" placeholder="Date" clearable
									style="width: 180px; margin-right: 10px" @change="onInit" size="middle">
									<el-option v-for="item in dirs" :key="item.name" :label="item.name"
										:value="item.name" />
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="24" :md="24" :lg="6" :xl="8" class="mb1 mt1">
							<el-form-item label=" ">
								<div class="form_search_btn">
									<el-button type="primary" @click="onSearch" icon="search">
										{{ $t('message.page.buttonSearch') }}
									</el-button>
								</div>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>
			</div>
		</div>

		<div class="list-table">
			<el-card shadow="never">
				<div class="scTable" style="height: 100%" ref="scTableMain">
					<div class="scTable-table">
						<el-table :data="tableData.data" v-loading="tableData.loading" height="calc(100%)">
							<template #empty>
								<el-empty description="No data" :image-size="100"></el-empty>
							</template>
							<el-table-column type="selection" />
							<el-table-column label="File Name">
								<template #default="{ row }">
									<el-link type="primary" @click="onLogFile(row)">{{ row.fileName }}</el-link>
								</template>
							</el-table-column>
							<el-table-column label="Creation Time">
								<template #default="{ row }">
									<span>{{ row.creationTime }}</span>
								</template>
							</el-table-column>
							<el-table-column label="File Size">
								<template #default="{ row }">
									<span>{{ row.fileSize }}</span>
								</template>
							</el-table-column>
						</el-table>
					</div>
				</div>
			</el-card>
		</div>
	</div>
</template>

<script lang="ts">
import { toRefs, reactive, onMounted, defineComponent, ref } from 'vue';
import { formatStrDate, formatTypeTime } from '/@/utils/formatTime';
import logEventApi from '/@/api/logEvent/index';
import filesApi from '/@/api/Files/index';
import { ElMessage } from 'element-plus';

export default defineComponent({
	name: 'LogFile',
	components: {},
	setup() {
		const state = reactive({
			tableData: {
				data: [],
				total: 0,
				loading: false,
				selection: [],
				param: {
					dirName: '',
					searchKey: '',
				},
			},
			dirs: [],
		});


		const onInit = () => {
			state.tableData.loading = true;
			logEventApi
				.GetLogFiles(state.tableData.param.dirName)
				.then((rs) => {
					state.tableData.data = rs.data;
				})
				.catch((rs) => { })
				.finally(() => (state.tableData.loading = false));
		};

		const onSearch = () => {
			onInit();
		};

		const onLogFile = (row: any) => {
			state.tableData.loading = true;
			const req = {
				Data: state.tableData.param.dirName,
				Name: row.fileName,
			};

			filesApi
				.VisitLogs(req)
				.then((response) => {
					//console.log('response.headers', response.headers);
					const contentDisposition = response.headers['content-disposition'];
					let fileName = 'default';


					if (contentDisposition) {
						const fileNameMatch = contentDisposition.match(/filename\*?=["']?(?:UTF-\d['"]*)?([^;\r\n"']*)["']?/i);
						if (fileNameMatch && fileNameMatch[1]) {
							fileName = fileNameMatch[1];
						} else {

							const fileExtension = row.fileName.split('.').pop();
							fileName = `default.${fileExtension}`;
						}
					} else {

						const fileExtension = row.fileName.split('.').pop();
						fileName = `default.${fileExtension}`;
					}


					const url = window.URL.createObjectURL(new Blob([response.data]));
					const a = document.createElement('a');
					a.href = url;
					a.download = fileName;
					document.body.appendChild(a);
					a.click();
					document.body.removeChild(a);
					window.URL.revokeObjectURL(url);
				})
				.catch((error) => {
					console.error('Export failed:', error);
					ElMessage.error('Export failed. Please try again later.');
				})
				.finally(() => {
					state.tableData.loading = false;
				});
		};


		onMounted(() => {
			logEventApi
				.GetLogDirectories(state.tableData.param)
				.then((rs) => {
					const data = rs.data;
					state.dirs = data;
					if (data && data.length > 0) {
						state.tableData.param.dirName = data[0].name;
						onInit();
					}
				})
				.catch((rs) => { })
				.finally(() => (state.tableData.loading = false));
		});
		return {
			formatStrDate,
			onInit,
			onSearch,
			onLogFile,
			...toRefs(state),
		};
	},
});
</script>

<style>
.system-tab-container .log-msg {
	overflow: hidden;
	white-space: nowrap;
}
</style>
