import { defineStore } from 'pinia';
import { Local, Session } from '/@/utils/storage';
import { LocalStorageKeys, SessionStorageKeys, StoreKeys } from '/@/constants';
import loginApi from '/@/api/login';

/**
 * 用户信息
 * @methods setUserInfos 设置用户信息
 */
export const useUserInfo = defineStore(StoreKeys.PINIA_USER_INFO, {
	state: (): UserInfosState => ({
		userInfos: {} as UserInfos,
	}),
	actions: {
		async setUserInfos() {
			const cachedUserInfo = Local.get(LocalStorageKeys.UserInfo);
			this.userInfos = cachedUserInfo || (await this.getApiUserInfo());
			return this.userInfos;
		},
		async getUserInfos() {
			await this.setUserInfos();
			return this.userInfos;
		},
		async getApiUserInfo(): Promise<UserInfos> {
			const token = Local.get(LocalStorageKeys.Token);
			if (!token) throw 'Token 缺失';

			const rs = await loginApi.GetMyInfo();
			this.userInfos = {
				...this.createDefaultUserInfos(),
				userId: rs.data.id,
				userName: rs.data.name,
				mobile: rs.data.mobile,
				email: rs.data.email,
				photo: rs.data.photo,
				lastLoginTime: rs.data.lastLoginTime,
				firstName: rs.data.firstName,
				lastLoginIp: rs.data.lastLoginIp,
				roles: rs.data.roleNames,
				authBtnList: rs.data.permissions,
				userType: rs.data.userType,
				language: rs.data.language ?? 'en',
				projects: rs.data.projects,
				accounts: rs.data.accounts,
				privateTicket: rs.data.privateTicket,
				nodeId: rs.data.nodeId,
				systemUrl: rs.data.systemUrl,
				needToChangePwd: rs.data.needToChangePwd,
				time: Date.now(),
			};
			
			const bptsSSOLogin = Session.get(SessionStorageKeys.BptsSSOLogin);
			if (bptsSSOLogin) {
				this.userInfos.needToChangePwd = false;
			}
			Local.set(LocalStorageKeys.UserInfo, this.userInfos);

			return this.userInfos;
		},
		createDefaultUserInfos(): UserInfos {
			return {
				userId: '',
				userName: '',
				mobile: '',
				email: '',
				photo: '',
				lastLoginTime: 0,
				firstName: '',
				lastLoginIp: '',
				roles: [],
				authBtnList: [],
				userType: '',
				language: '',
				projects: [],
				accounts: [],
				privateTicket: '',
				nodeId: '',
				systemUrl: '',
				time: 0,
				needToChangePwd: false,
			};
		},
	},
});
