<template>
	<div class="top-container">
		<div class="list-search">
			<el-card class="list-search-card" shadow="never">
				<el-form label-width="120px" @keyup.enter="onSearch">
					<el-row :gutter="24">
						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="5" class="mb1 mt1">
							<el-form-item :label="$t('message.wWE_AccountLabels.AccountCode')" prop="accountCode">
								<el-input v-model="state.formTable.params.accountCode" clearable
									:placeholder="$t('message.page.searchKeyPlaceholder')" class="w-20"
									size="default"></el-input>
							</el-form-item>
						</el-col>

						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="5" class="mb1 mt1">
							<el-form-item :label="$t('message.wWE_AccountLabels.AccountName')" prop="accountName">
								<el-input v-model="state.formTable.params.accountName" clearable
									:placeholder="$t('message.page.searchKeyPlaceholder')" class="w-20"
									size="default"></el-input>
							</el-form-item>
						</el-col>

						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="5" class="mb1 mt1">
							<el-form-item class="nowidth" label=" ">
								<div class="searchBtn">
									<el-button type="primary" icon="search" @click="onSearch">
										{{ $t('message.page.buttonSearch') }}
									</el-button>
									<el-button type="danger" icon="refresh-left" @click="onClear">
										{{ $t('message.page.buttonReset') }}
									</el-button>
								</div>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>
			</el-card>
		</div>

		<div class="list-table">
			<el-card shadow="never">
				<el-header class="table_header">
					<div class="left-panel">
						<el-button type="primary" icon="plus" @click="onAdd" v-auth="'WWE_Account.Create'">
							{{ $t('message.wWE_AccountButtons.createWWE_Account') }}
						</el-button>

						<el-button type="primary" icon="bottom-left" @click="onImport" v-auth="'WWE_Account.Import'">
							{{ $t('message.wWE_AccountButtons.importWWE_Account') }}
						</el-button>

						<el-button type="primary" icon="bottom-left" @click="onImportCsv" v-auth="'WWE_Account.Import'">
							{{ $t('message.wWE_AccountButtons.importWWE_Account') }}
						</el-button>
					</div>

					<div class="right-panel" v-auth="'WWE_Account.Export'">
						<Export ref="exportRef" pageName="WWE_Account" :pageParams="state.formTable.params" />
					</div>
				</el-header>
				<div class="scTable">
					<div class="scTable-table">
						<vxe-table show-overflow border="full" :min-height="25" ref="tableRef"
							:header-cell-config="{ height: 23, padding: false }"
							:row-config="{ isHover: true, height: 33 }" :column-config="{ resizable: true }"
							:checkbox-config="{ highlight: true }" :loading="state.formTable.loading"
							:loading-config="{ text: $t('message.page.loading') }" :tree-config="{ transform: true }"
							:scroll-y="{ enabled: false, gt: 0, mode: 'wheel' }" :data="state.formTable.data"
							:header-cell-style="headerCellStyle" :sortConfig="sortConfig" @sort-change="onSortChange"
							@cell-click="cellClick">
							<template #empty>
								<el-empty :description="$t('message.page.emptyDescription')"
									:image-size="100"></el-empty>
							</template>

							<vxe-column type="checkbox" width="50"></vxe-column>

							<vxe-column :title="$t('message.wWE_AccountFields.AccountCode')" field="accountCode"
								min-width="150" sortable>
								<template #default="scope">
									<el-link @click="onEdit(scope.row)" type="primary">
										{{ scope.row.accountCode }}
									</el-link>
								</template>
							</vxe-column>

							<vxe-column :title="$t('message.wWE_AccountFields.AccountName')" field="accountName"
								min-width="150"></vxe-column>

							<vxe-column :title="$t('message.wWE_AccountFields.CustomerEmailAddress')"
								field="customerEmailAddress" min-width="150"></vxe-column>

							<vxe-column :title="$t('message.wWE_AccountFields.FileNamingConvention')"
								field="fileNamingConvention" min-width="180"></vxe-column>

							<vxe-column :title="$t('message.wWE_AccountFields.IfCombine')" field="ifCombine"
								min-width="120">

							</vxe-column>

							<vxe-column :title="$t('message.wWE_AccountFields.CreatedByName')" field="createdByName"
								min-width="150"></vxe-column>

							<vxe-column :title="$t('message.wWE_AccountFields.ModifiedAt')" field="modifiedAt"
								min-width="180" sortable></vxe-column>

							<vxe-column fixed="right" :title="$t('message.page.actions')" width="90">
								<template #default="scope">
									<el-tooltip class="box-item" effect="dark" :content="$t('message.limits.Edit')"
										placement="bottom">
										<vxe-button mode="text" status="primary" icon="vxe-icon-edit"
											v-auth="'WWE_Account.Edit'" @click="onEdit(scope.row)"></vxe-button>
									</el-tooltip>
									<el-tooltip class="box-item" effect="dark" :content="$t('message.limits.Delete')"
										placement="bottom">
										<vxe-button mode="text" status="error" icon="vxe-icon-delete"
											v-auth="'WWE_Account.Delete'" @click="onDelete(scope.row)"></vxe-button>
									</el-tooltip>
								</template>
							</vxe-column>
						</vxe-table>
					</div>

					<div class="scTable-page">
						<el-pagination @size-change="onSizeChange" @current-change="onCurrentChange" :pager-count="5"
							:page-sizes="[20, 30, 50, 100]" v-model:current-page="state.formTable.params.pageIndex"
							background v-model:page-size="state.formTable.params.pageSize"
							layout="total, sizes, prev, pager, next, jumper" :total="state.formTable.total" small>
						</el-pagination>
					</div>
				</div>
			</el-card>
		</div>

		<createOrEdit ref="createOrEditRef" :title="state.fromTitle" @refreshData="onSearch" />
		<importData ref="importDataRef" title="Import Data" @refreshData="onSearch" />
		<importCsvData ref="importCsvDataRef" title="Import Data" @refreshData="onSearch" />
	</div>
</template>

<script lang="ts" setup name="wWE_Account/index">
import { ref, computed, reactive, onMounted, nextTick, defineAsyncComponent, onActivated, onDeactivated, getCurrentInstance, h } from 'vue';
import { ElMessageBox, ElMessage, ElSelectV2 } from 'element-plus';
import type { TableInstance } from 'element-plus';
import { useI18n } from 'vue-i18n';

import DateSelector from '/@/components/common/DateSelector.vue';
import DateRangeSelector from '/@/components/common/DateRangeSelector.vue';
import { verifyNumberComma, verifyNumberCommaV2, verifyNumberIntegerAndFloat } from '/@/utils/toolsValidate';

import wWE_AccountApi from '/@/api/WWE_Account/index';

import Export from '/@/components/export/index.vue';
import { VxeTablePropTypes, VxeGridProps, VxeGridInstance } from 'vxe-table';

const createOrEdit = defineAsyncComponent(() => import('./components/createOrEdit.vue'));
const importData = defineAsyncComponent(() => import('./components/ImportData.vue'));
const importCsvData = defineAsyncComponent(() => import('./components/ImportCsvData.vue'));

const { proxy } = getCurrentInstance() as any;
const { t } = useI18n();
const createOrEditRef = ref();
const importDataRef = ref();
const importCsvDataRef = ref();
const exportRef = ref();

interface RowVO {
	accountId: number;
	accountCode: string;
	accountName: string;
	fileNamingConvention: string;
	ifCombine: string;
	requiredFileTypes: string;
	createdByName: string;
	modifiedByName: string;
	createdById: number;
	createdAt: string;
	modifiedById: number;
	modifiedAt: string;
}

const tableRef = ref<VxeGridInstance<RowVO>>();

const sortConfig = ref<VxeTablePropTypes.SortConfig<RowVO>>({
	defaultSort: {
		field: 'createdAt',
		order: 'desc',
	},
	trigger: "cell"
});

const headerCellStyle = ({ column }: any) => {
	if (column.sortable) {
		return {
			cursor: 'pointer',
		};
	}
};

function getDefaultQueryParams() {
	return {
		pageIndex: 1,
		pageSize: 20,
		order: 'createdAt',
		sort: 'desc',
		accountId: 0,
		accountCode: '',
		accountName: '',
		fileNamingConvention: '',
		ifCombine: '',
		requiredFileTypes: '',
		createdByName: '',
		modifiedByName: '',
		createdById: '',
		createdAt: null,
		createdAtStart: null,
		createdAtEnd: null,
		modifiedById: '',
		modifiedAt: null,
		modifiedAtStart: null,
		modifiedAtEnd: null,
	};
}

const state = reactive({
	fromTitle: '',
	maxHeight: 0,
	formTable: {
		data: [],
		loading: false,
		total: 0,
		params: getDefaultQueryParams(),
	},
	options: [
		{ label: t('message.userFields.Active'), value: false },
		{ label: t('message.userFields.Inactive'), value: true },
	],
});

// 设置默认的最大高度
const tableMaxHeight = computed(() => (state.maxHeight > 0 ? state.maxHeight : 500));

var isMounted = false;

onMounted(async () => {
	isMounted = true;

	if (isMounted) {
		onSearch();
	}

	updateMaxHeight();
	window.addEventListener('resize', updateMaxHeight);
});

const updateMaxHeight = () => {
	nextTick(() => {
		setTimeout(() => {
			state.maxHeight = window.innerHeight - 420;
		}, 100);
	});
};

onActivated(async () => {
	if (!isMounted) {
		await onSearch();
	}
});

onDeactivated(() => {
	isMounted = false;
});

const onAdd = () => {
	state.fromTitle = t('message.wWE_AccountButtons.createWWE_Account');
	createOrEditRef.value.open(getDefaultQueryParams());
};

const onImport = () => {
	state.fromTitle = 'Import WWE_Account';
	importDataRef.value.open();
};

const onImportCsv = () => {
	state.fromTitle = 'Import CategoryTest';
	importCsvDataRef.value.open();
};

const onEdit = (row: any) => {
	state.fromTitle = t('message.wWE_AccountButtons.editWWE_Account');
	createOrEditRef.value.open(row);
};

const onDelete = async (row: any) => {
	ElMessageBox.confirm(t('message.page.dlgDeleteText'), t('message.page.dlgTip'), {
		confirmButtonText: t('message.page.confirm'),
		cancelButtonText: t('message.page.cancel'),
		type: 'warning',
	})
		.then(async () => {
			if (row.accountId > 0) {
				await wWE_AccountApi
					.DeleteByKey(row.accountId)
					.then((rs: any) => {
						ElMessage.success(t('message.page.deleteSuccess'));
						onSearch();
					})
					.catch((error: HandledError) => {
						if (!error.isHandled) {
							const errorCode = error.code;
							const errorMessage = error.message;
							ElMessage.error(errorMessage);
						}
					});
			}
		})
		.catch((error: any) => {
			if (error !== 'cancel') {
				ElMessage.error(error.resultMsg);
			}
		});
};

const onSearch = async () => {
	state.formTable.loading = true;

	await wWE_AccountApi
		.Query(state.formTable.params)
		.then((rs: any) => {
			state.formTable.data = rs.data;
			state.formTable.total = rs.totalCount;

			let obj = { entityName: 'WWE_Account', pageParams: state.formTable.params };
			exportRef?.value?.getColumns(obj);
		})
		.catch((error: HandledError) => {
			if (!error.isHandled) {
				const errorCode = error.code;
				const errorMessage = error.message;
				ElMessage.error(errorMessage);
			}
		})
		.finally(() => {
			state.formTable.loading = false;

			if (state.formTable.data.length <= 0) {
				//数据为空，清空表头的默认选中
				nextTick(() => {
					proxy.$refs.tableRef.clearCheckboxRow();
				});
			}
		});
};

const onSortChange = (column: any) => {
	if (!column.field) {
		if (column.column.sortable) {
			state.formTable.params.order = column.column.field;

			let field = column.column.field;

			// 默认降序
			let order: 'desc' | 'asc' = 'desc';

			const $table = tableRef.value;

			if (state.formTable.params.sort == 'desc') {
				state.formTable.params.sort = 'asc';
				order = 'asc';

				if ($table) {
					$table.setSort({ field, order });
				}
			} else {
				state.formTable.params.sort = 'desc';
				order = 'desc';

				if ($table) {
					$table.setSort({ field, order });
				}
			}

			onSearch();
		}
	} else {
		state.formTable.params.order = column.field;
		state.formTable.params.sort = column.order;

		onSearch();
	}
};

const onClear = () => {
	clearQueryParams(state.formTable.params);
	onSearch();
};

function clearQueryParams(params: any): void {
	Object.assign(params, getDefaultQueryParams());
}

const onSizeChange = (val: number) => {
	state.formTable.params.pageSize = val;

	onSearch();
};

const onCurrentChange = (val: number) => {
	state.formTable.params.pageIndex = val;

	onSearch();
};

const cellClick = ({ row, column }: any) => {
	const $table = tableRef.value;
	if ($table) {
		$table.toggleCheckboxRow(row);
	}
};
</script>

<style scoped lang="scss">
@media (max-width: 800px) {
	::v-deep .el-drawer {
		width: 100% !important;
	}
}
</style>
