<template>
	<div class="mass_update-container">
		<el-dialog v-model="massUpdateDialogVisible" title="Update Document Retrieval Status" width="450px" :close-on-click-modal="false" draggable @closed="handleDialogClosed">
			<div class="mass-update-dialog">
				<div class="dialog-header">
					<div class="header-text">
						<h3>Update Status for Selected Invoices</h3>
						<p class="tip-text">This will update the document retrieval Status for all selected items</p>
					</div>
				</div>

				<el-form class="mt-4 w100">
					<el-form-item required>
						<el-select v-model="massUpdateStatus" placeholder="Select a status" style="width: 100%" clearable size="default">
							<el-option v-for="item in retrievalStatusOptions" :key="item.itemValue" :label="item.itemName" :value="item.itemValue" />
						</el-select>
						<div class="form-tip mt-1">
							<el-icon>
								<InfoFilled />
							</el-icon>
							<span>Current status will be replaced for all selected items</span>
						</div>
					</el-form-item>

					<div class="selection-summary">
						<el-tag type="info" size="small">
							<el-icon>
								<Tickets />
							</el-icon>
							{{ selectedCount }} items selected
						</el-tag>
					</div>
				</el-form>
			</div>

			<template #footer>
				<div class="dialog-footer">
					<el-button @click="closeDialog">Cancel</el-button>
					<el-button type="primary" @click="confirmMassUpdate" :loading="massUpdateLoading" :disabled="!massUpdateStatus || selectedCount === 0"> Update </el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent, PropType, computed } from 'vue';
import { Document, Tickets, InfoFilled } from '@element-plus/icons-vue';
import type { ElTable } from 'element-plus';
import invoiceApi from '/@/api/WWE_Invoice';
import { ElMessage } from 'element-plus';
import dictItemApi from '/@/api/dictItem';
import { DictionaryOption } from '/@/models';

export default defineComponent({
	name: 'MassUpdateStatusDialog',
	components: {Tickets,InfoFilled},
	props: {
		tableRef: {
			type: Object as PropType<InstanceType<typeof ElTable>>,
			required: true,
		},
	},
	emits: ['complete'],
	setup(props, { emit }) {
		const state = reactive({
			massUpdateDialogVisible: false,
			massUpdateStatus: null as string | number | null,
			massUpdateLoading: false,
			retrievalStatusOptions: [] as DictionaryOption[],
		});

		const selectedCount = computed(() => {
			return props.tableRef?.getCheckboxRecords?.()?.length || 0;
		});

		const openDialog = async () => {
			state.massUpdateDialogVisible = true;
			state.massUpdateStatus = null;
			state.retrievalStatusOptions = await fetchStatusOptions();
		};

		const closeDialog = () => {
			state.massUpdateDialogVisible = false;
		};

		const handleDialogClosed = () => {
			state.massUpdateStatus = null;
		};

		const fetchStatusOptions = async () => {
			const response = await dictItemApi.Many(['Retrieved Status']);
			if (response?.data) {
				const methodResult = response.data.find((item) => item.dictValue === 'Retrieved Status');
				return methodResult?.items || [];
			}
			return [];
		};

		const confirmMassUpdate = async () => {
			if (!state.massUpdateStatus) {
				ElMessage.warning('Please select a status');
				return;
			}

			const selectedRecords = props.tableRef?.getCheckboxRecords() || [];
			const ids = selectedRecords.map((r) => r.invoiceId);

			state.massUpdateLoading = true;
			try {
				await invoiceApi.MassUpdateStatus({
					invoiceIds: ids,
					retrievalStatus: state.massUpdateStatus,
				});
				ElMessage.success('Update successful');
				closeDialog();
				emit('complete', { success: true, ids });
			} catch {
				ElMessage.error('Update failed');
				emit('complete', { success: false, ids });
			} finally {
				state.massUpdateLoading = false;
			}
		};

		return {
			...toRefs(state),
			selectedCount,
			openDialog,
			closeDialog,
			handleDialogClosed,
			confirmMassUpdate,
		};
	},
});
</script>

<style lang="scss" scoped>
.mass_update-container {
	position: relative;
}

.mass-update-dialog {
	.dialog-header {
		display: flex;
		align-items: center;
		gap: 12px;
		margin-bottom: 16px;

		.header-icon {
			font-size: 24px;
			color: var(--el-color-primary);
		}

		h3 {
			margin: 0;
			font-size: 16px;
			font-weight: 500;
		}

		.tip-text {
			margin: 4px 0 0;
			font-size: 13px;
			color: var(--el-text-color-secondary);
		}
	}

	.form-tip {
		display: flex;
		align-items: center;
		gap: 6px;
		font-size: 12px;
		color: var(--el-text-color-secondary);

		.el-icon {
			font-size: 14px;
		}
	}

	.selection-summary {
		margin-top: 16px;
		text-align: center;

		.el-tag {
			padding: 4px 10px;

			.el-icon {
				margin-right: 4px;
			}
		}
	}
}

.dialog-footer {
	display: flex;
	justify-content: flex-end;
}
</style>
