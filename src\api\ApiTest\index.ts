import request from '/@/utils/request';
import { MessageModel } from '../types';
import { TableGenerateConfig, DbConnectionConfig, TableInfo, ColumnInfo } from './types';

const apiTestApi = {
    TestConnection(data: DbConnectionConfig) {
        return request<MessageModel<boolean>>({
            url: '/Api/ApiTest/TestConnection',
            method: 'post',
            data
        });
    },

    GenerateAllCode(data: string) {
        return request<MessageModel<string>>({
            url: '/Api/ApiTest/GenerateAllCode',
            method: 'post',
            data
        });
    },

    CreateDynamicTableAllCode(data: string) {
        return request<MessageModel<string>>({
            url: '/Api/ApiTest/CreateDynamicTableAllCode',
            method: 'post',
            data
        });
    },

    GenerateModel() {
        return request<MessageModel<string>>({
            url: '/Api/ApiTest/GenerateModel',
            method: 'post'
        });
    },

    SaveDbConfig(data: DbConnectionConfig) {
        return request<MessageModel<boolean>>({
            url: '/Api/ApiTest/SaveDbConfig',
            method: 'post',
            data
        });
    },

    GetDbConfig() {
        return request<MessageModel<DbConnectionConfig>>({
            url: '/Api/ApiTest/GetDbConfig',
            method: 'get'
        });
    },

    GetTables() {
        return request<MessageModel<TableInfo[]>>({
            url: '/Api/ApiTest/GetTables',
            method: 'get'
        });
    },

    GenerateCodeByTableNames(data: TableGenerateConfig[]) {
        return request<MessageModel<string>>({
            url: '/Api/ApiTest/GenerateCodeByTableNames',
            method: 'post',
            data
        });
    },

    GetTableColumns(tableName: string, schemaName: string) {
        return request<MessageModel<ColumnInfo[]>>({
            url: `/Api/ApiTest/GetTableColumns?tableName=${tableName}&schemaName=${schemaName}`,
            method: 'get'
        });
    }
};

export default apiTestApi;
