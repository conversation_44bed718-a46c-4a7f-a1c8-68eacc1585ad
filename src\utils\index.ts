import type { ElForm } from 'element-plus';
import appSettings from '/@/config/index.js';

export const resetForm = (formEl: InstanceType<typeof ElForm> | undefined) => {
	if (!formEl) return;
	formEl.resetFields();
};

/**
 * 取数组的最后一个元素
 * @param {Array} data 数组[]
 * @param {any}  defValue 默认值
 */
export const getElcascaderSingle = (data: any, defValue: any) => {
	if (defValue == undefined) defValue = '';
	if (!data || data.length == 0) {
		return defValue;
	}
	return data[data.length - 1];
};

export const getElcascaderMultiple = (data: any, defValue: any) => {
	if (defValue == undefined) defValue = [];
	if (!data || data.length == 0) {
		return defValue;
	}

	let arr = [];

	data.forEach((item) => {
		if (item instanceof Array) {
			arr.push(item[item.length - 1]);
		} else {
			arr.push(item);
		}
	});

	return arr;
};

export const isNullOrWhiteSpace = (value: string | null | undefined): boolean => {
	return !value || !value.trim();
};

/**
 * 判断对象不是空的
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export const isNotEmptyOrNull = (obj: any) => {
	if (!obj) {
		return false;
	}
	if (obj == null || obj == undefined) {
		return false;
	}
	if (obj && obj.length == 0) {
		return false;
	}

	return true;
};
/**
 * 判断是否大于0
 */
export const parseThanZero = (val: any) => {
	if (!isNotEmptyOrNull(val)) return false;

	if (parseInt(val) <= 0) return false;

	return true;
};
/**
 * 获取菜单的父节点，返回数组
 */
export const getCascaderMenuParent = (data: any, mid: any) => {
	var temp: any[] = [];
	var forFn = function (arr: string | any[], mid: any) {
		for (var i = 0; i < arr.length; i++) {
			var item = arr[i];
			if (item.id === mid) {
				temp.push(item.id);
				forFn(data, item.parentId);
				break;
			} else {
				if (item.children) {
					forFn(item.children, mid);
				}
			}
		}
	};
	forFn(data, mid);
	// 数组倒序
	if (temp.length > 0) {
		return temp.reverse();
	}

	if (temp.length == 0) {
		temp.push(mid);
	}

	return temp;
};

/**
 * 获取菜单的父节点，返回数组
 */
export const getCascaderIdustryParent = (data: any, mid: any) => {
	var temp: any[] = [];
	var forFn = function (arr: string | any[], mid: any) {
		for (var i = 0; i < arr.length; i++) {
			var item = arr[i];
			if (item.industryId === mid) {
				temp.push(item.industryId);
				forFn(data, item.parentId);
				break;
			} else {
				if (item.children) {
					forFn(item.children, mid);
				}
			}
		}
	};
	forFn(data, mid);
	// 数组倒序
	if (temp.length > 0) {
		return temp.reverse();
	}

	if (temp.length == 0) {
		temp.push(mid);
	}

	return temp;
};

export const toThousandFilter = (num: any) => {
	return (+num || 0).toString().replace(/^-?\d+/g, (m) => m.replace(/(?=(?!\b)(\d{3})+$)/g, ','));
};

export const newGuid = () => {
	return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
		var r = (Math.random() * 16) | 0,
			v = c == 'x' ? r : (r & 0x3) | 0x8;
		return v.toString(16);
	});
};

export const getCaseInsensitivePropertyName = (obj: any, key: string) => {
	var lowerKey = key.toLowerCase();
	for (var prop in obj) {
		if (prop.toLowerCase() === lowerKey) {
			return prop;
		}
	}

	return undefined;
};

/**
 * 打印日志
 *
 */
export const debugLog = (message: any, optionalParams?: any) => {
	if (appSettings && appSettings.LOGGING_ENABLED) {
		if (optionalParams) {
			window.console.log(message, optionalParams);
		} else {
			window.console.log(message);
		}
	}
};


/**
 * 处理 API 请求错误
 * @param ex 异常对象
 * @returns 格式化的错误信息
 */
export const handleApiError = (ex: any): string => {
    let errorMessage = ex.message || 'Unknown error';
    if (ex.resultMsg) {
        errorMessage = ex.resultMsg;
    } else if (ex.response?.data) {
        const errorData = ex.response.data;
        errorMessage = `${errorData.resultMsg} (Code: ${errorData.resultCode})`;
    }
    return errorMessage;
};

/**
 * 比较两个字符串是否相等，默认忽略大小写
 * @param {string} str1 - 第一个字符串
 * @param {string} str2 - 第二个字符串
 * @param {boolean} [caseSensitive=false] - 是否区分大小写
 * @returns {boolean} - 如果两个字符串相等则返回 true，否则返回 false
 */
export const equals = (str1: string, str2: string, caseSensitive: boolean = false): boolean => {
	if (caseSensitive) {
		return str1 === str2;
	} else {
		return str1.toLowerCase() === str2.toLowerCase();
	}
};

/**
 * 比较两个字符串是否相等，不区分大小写
 * @param {string} str1 - 第一个字符串
 * @param {string} str2 - 第二个字符串
 * @returns {boolean} - 如果两个字符串相等则返回 true，否则返回 false
 */
export const equalsIgnoreCase = (str1: string, str2: string): boolean => {
	return str1.toLowerCase() === str2.toLowerCase();
};

/**
 * 在数组中进行不区分大小写的包含检查
 * @param {string[]} array - 要检查的字符串数组
 * @param {string} value - 要查找的值
 * @returns {boolean} - 如果数组中包含指定值则返回 true，否则返回 false
 */
export const includesIgnoreCase = (array: string[], value: string): boolean => {
	return array.map((item) => item.toLowerCase()).includes(value.toLowerCase());
};


export const formatCurrency = (
	value: string | number | null | undefined,
	options?: {
		useParenthesesForNegative?: boolean; // 是否用括号表示负数
		decimalPlaces?: number; // 小数位数（默认2位）
		source?: string; // 来源，用于处理特殊逻辑
	}
): string => {
	// 处理默认值和空值
	const decimalPlaces = options?.decimalPlaces ?? 2;
	const useParentheses = options?.useParenthesesForNegative ?? false;
	const source = options?.source?.toLowerCase();

	if (value === null || value === undefined || value === '') {
		return '$';
	}

	// 转换为字符串处理
	let strValue = typeof value === 'number' ? value.toString() : value;

	// 移除所有非数字、减号、小数点
	let raw = strValue.replace(/[^0-9.-]/g, '');

	// 判断是否为负数（仅检查减号）
	const isNegative = raw.startsWith('-');

	// 去掉减号以便格式化（后面会再加回来）
	if (isNegative) {
		raw = raw.substring(1);
	}

	// 分离整数和小数部分
	const parts = raw.split('.');
	let integerPart = parts[0] || '0'; // 处理无整数部分的情况

	// 处理小数部分（限制位数并补零）
	let decimalPart = '';
	if (decimalPlaces > 0) {
		const decimalDigits = parts.length > 1 ? parts[1] : '';
		const paddedDecimal = decimalDigits.padEnd(decimalPlaces, '0').substring(0, decimalPlaces);
		decimalPart = `.${paddedDecimal}`;
	}

	// 判断是否为零金额
	const isZero = parseFloat(`${integerPart}.${decimalPart.replace('.', '')}`) === 0;
	//console.log(`isZero: ${isZero}, source: ${source}`);
	// 特殊处理：来源是 From Spreadsheet 且金额为 0，返回 N/A
	if (isZero && source === 'from spreadsheet') {
		return 'N/A';
	}

	// 添加千分位
	integerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

	// 根据配置格式化负数
	if (isNegative) {
		if (useParentheses) {
			return `$ (${integerPart}${decimalPart})`;
		} else {
			return `-$ ${integerPart}${decimalPart}`;
		}
	}

	return `$ ${integerPart}${decimalPart}`;
};