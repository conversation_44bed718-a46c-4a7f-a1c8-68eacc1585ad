
export default {
    wWE_ImagesSearch: {
        //Search Area
    },
    wWE_ImagesButtons: {
        createWWE_Images: 'New W W E_ Images',
        editWWE_Images: 'Edit W W E_ Images',
        importWWE_Images: 'Import W W E_ Images',
    },
    wWE_ImagesLabels: {
		Id: 'Id',
		FilePath: 'File Path',
		OriginalFileName: 'Original File Name',
		FileName: 'File Name',
		FileExt: 'File Ext',
		FileMimeType: 'File Mime Type',
		FileSize: 'File Size',
		Priority: 'Priority',
		Queue: 'Queue',
		Indexed: 'Indexed',
		Comment: 'Comment',
		BatchNum: 'Batch Num',
		ReceivingType: 'Receiving Type',
		IsDeleted: 'Is Deleted',
		DeletedAt: 'Deleted At',
		AccountId: 'Account Id',
		CreatedById: 'Created By Id',
		CreatedByName: 'Created By Name',
		CreatedAt: 'Created At',
		ModifiedById: 'Modified By Id',
		ModifiedByName: 'Modified By Name',
		ModifiedAt: 'Modified At'
    },
    wWE_ImagesFields: {
		Id: 'Id',
		FilePath: 'File Path',
		OriginalFileName: 'Original File Name',
		FileName: 'File Name',
		FileExt: 'File Ext',
		FileMimeType: 'File Mime Type',
		FileSize: 'File Size',
		Priority: 'Priority',
		Queue: 'Queue',
		Indexed: 'Indexed',
		Comment: 'Comment',
		BatchNum: 'Batch Num',
		ReceivingType: 'Receiving Type',
		IsDeleted: 'Is Deleted',
		DeletedAt: 'Deleted At',
		AccountId: 'Account Id',
		CreatedById: 'Created By Id',
		CreatedByName: 'Created By Name',
		CreatedAt: 'Created At',
		ModifiedById: 'Modified By Id',
		ModifiedByName: 'Modified By Name',
		ModifiedAt: 'Modified At'
    },
    wWE_ImagesDlgTips: {
        deleteError: 'There is already a configuration relationship, deletion is not allowed!',
    },
    wWE_ImagesCommon: {
        ImportData: 'Import Data',
		UploadExcelFile: 'Upload Excel File',
        UploadCsvFile: 'Upload Csv File',
		Note: '(Note: This action will overwrite the data of the selected month)'
	},
};
            