<template>
	<el-dialog v-model="dialogVisible" fullscreen :title="title || displayName || fileName" :close-on-click-modal="false" destroy-on-close @closed="resetState" class="file-preview-dialog enhanced-preview">
		<!-- Error state -->
		<div v-if="renderError" class="error-state">
			<el-alert type="error" :closable="false" :title="renderError" center show-icon style="max-width: 400px; margin: 0 auto" />
			<div class="error-actions">
				<el-button type="primary" @click="retryRender">
					<el-icon><Refresh /></el-icon>
					Retry
				</el-button>
				<el-button @click="dialogVisible = false">Close</el-button>
			</div>
		</div>

		<!-- Loading state -->
		<div v-else-if="loading" class="loading-container">
			<el-skeleton :rows="5" animated />
		</div>

		<!-- PDF Preview -->
		<vue-office-pdf v-else-if="fileType === 'pdf'" :src="fileData" class="preview-content" @rendered="handleRendered" @error="handleError" />

		<!-- Word Preview -->
		<vue-office-docx v-else-if="fileType === 'docx'" :src="fileData" class="preview-content" @rendered="handleRendered" @error="handleError" />

		<!-- Excel Preview -->
		<vue-office-excel v-else-if="fileType === 'xlsx'" :src="fileData" class="preview-content" @rendered="handleRendered" @error="handleError" />

		<!-- PPT Preview -->
		<vue-office-pptx v-else-if="fileType === 'pptx'" :src="fileData" class="preview-content" @rendered="handleRendered" @error="handleError" />

		<!-- Unsupported Format -->
		<div v-else class="unsupported-format">
			<el-alert type="warning" :closable="false"> Preview not supported for {{ fileType }} format. Please download to view. </el-alert>
		</div>

		<template #footer v-if="!renderError">
			<div class="dialog-footer-center">
				<el-button @click="dialogVisible = false" size="default">Close</el-button>
				<el-button type="primary" :loading="downloading" @click="handleDownload" size="default">Download</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import { ElMessage, ElSkeleton, ElAlert } from 'element-plus';
import { Refresh } from '@element-plus/icons-vue';
import VueOfficePdf from '@vue-office/pdf';
import VueOfficeDocx from '@vue-office/docx';
import VueOfficeExcel from '@vue-office/excel';
import VueOfficePptx from '@vue-office/pptx';
import { saveAs } from 'file-saver';

interface FilePreviewOptions {
	api?: {
		method: (params: any) => Promise<any>;
		params: any;
	};
	url?: string;
	blob?: Blob;
	fileName: string; // Actual file name (for download)
	displayName?: string; // Display name (fallback for title)
	title?: string; // Custom dialog title (highest priority)
	type?: string; // Optional file type override
}

export default defineComponent({
	name: 'FilePreview',
	components: {
		VueOfficePdf,
		VueOfficeDocx,
		VueOfficeExcel,
		VueOfficePptx,
		ElSkeleton,
		ElAlert,
		Refresh,
	},
	setup() {
		const dialogVisible = ref(false);
		const downloading = ref(false);
		const loading = ref(false);
		const renderError = ref<string | null>(null);
		const fileData = ref<string | Blob | null>(null);
		const fileType = ref('');
		const fileName = ref('');
		const displayName = ref('');
		const title = ref('');
		const blobUrl = ref<string | null>(null);

		const getFileType = (filename: string): string => {
			const extension = filename.split('.').pop()?.toLowerCase() || '';
			switch (extension) {
				case 'pdf':
					return 'pdf';
				case 'doc':
				case 'docx':
					return 'docx';
				case 'xls':
				case 'xlsx':
					return 'xlsx';
				case 'ppt':
				case 'pptx':
					return 'pptx';
				default:
					return extension;
			}
		};

		const openPreview = async (file: FilePreviewOptions) => {
			if (!file) {
				ElMessage.warning('Invalid file parameter');
				return;
			}

			if (!file.fileName) {
				ElMessage.warning('File name is required');
				return;
			}

			try {
				resetState();
				loading.value = true;
				dialogVisible.value = true;

				// Set names
				fileName.value = file.fileName;
				displayName.value = file.displayName || '';
				title.value = file.title || '';

				// Determine file type
				fileType.value = file.type || getFileType(file.fileName);

				if (file.url) {
					fileData.value = file.url;
				} else if (file.api) {
					await fetchFile(file.api);
				} else if (file.blob) {
					blobUrl.value = URL.createObjectURL(file.blob);
					fileData.value = blobUrl.value;
				} else {
					ElMessage.warning('Invalid file source');
					dialogVisible.value = false;
				}
			} catch (error) {
				console.error('Error loading file:', error);
				handleError(new Error('Failed to load file'));
			} finally {
				loading.value = false;
			}
		};

		const fetchFile = async (apiConfig: { method: (params: any) => Promise<any>; params: any }) => {
			try {
				const response = await apiConfig.method(apiConfig.params);

				if (response.data instanceof Blob) {
					blobUrl.value = URL.createObjectURL(response.data);
					fileData.value = blobUrl.value;
				} else {
					fileData.value = response.data;
				}

				// Extract filename from headers if available
				if (response.headers?.['content-disposition']) {
					const disposition = response.headers['content-disposition'];
					const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
					const matches = filenameRegex.exec(disposition);
					if (matches?.[1]) {
						const extractedName = decodeURIComponent(matches[1].replace(/['"]/g, ''));
						fileName.value = extractedName;
						if (!displayName.value) {
							displayName.value = extractedName;
						}
					}
				}
			} catch (error) {
				throw error;
			}
		};

		const handleDownload = async () => {
			if (!fileData.value) return;

			downloading.value = true;
			try {
				if (typeof fileData.value === 'string') {
					const response = await fetch(fileData.value);
					const blob = await response.blob();
					saveAs(blob, fileName.value);
				} else if (fileData.value instanceof Blob) {
					saveAs(fileData.value, fileName.value);
				}
			} catch (error) {
				ElMessage.error('Download failed');
				console.error('Download error:', error);
			} finally {
				downloading.value = false;
			}
		};

		const resetState = () => {
			if (blobUrl.value) {
				URL.revokeObjectURL(blobUrl.value);
				blobUrl.value = null;
			}
			fileData.value = null;
			fileType.value = '';
			fileName.value = '';
			displayName.value = '';
			title.value = '';
			loading.value = false;
			renderError.value = null;
		};

		const handleRendered = () => {
			renderError.value = null;
			console.log(`${fileType.value} rendered successfully`);
		};

		const handleError = (err: Error) => {
			console.error('Rendering error:', err);
			renderError.value = `Failed to render file: ${err.message || 'Unknown error'}`;
		};

		const retryRender = () => {
			renderError.value = null;
			if (fileData.value) {
				// Force re-render by reassigning the value
				const currentData = fileData.value;
				fileData.value = null;
				setTimeout(() => {
					fileData.value = currentData;
				}, 50);
			}
		};

		return {
			dialogVisible,
			downloading,
			loading,
			renderError,
			fileData,
			fileType,
			fileName,
			displayName,
			title,
			openPreview,
			handleDownload,
			handleRendered,
			handleError,
			resetState,
			retryRender,
		};
	},
});
</script>

<style scoped lang="scss">
.file-preview-dialog {
	:deep(.el-dialog__body) {
		padding: 0;
	}
}

.preview-content {
	height: calc(100vh - 150px);
	border: none;
	margin: 0 auto;
	background-color: gray;
	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); 
	border-radius:  8px ; 
}
:deep(.vue-office-pdf-wrapper) {
	max-width: min(920px, 90vw);
	margin: 0 auto;
	overflow: hidden;
}

.unsupported-format,
.error-state {
	height: calc(100vh - 150px);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 20px;
	padding: 20px;
}

.error-actions {
	margin-top: 20px;
}

.loading-container {
	height: calc(100vh - 150px);
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	gap: 20px;
	padding: 20px;
}
.dialog-footer-center {
	display: flex;
	justify-content: center; /* 水平居中 */
	gap: 12px; /* 按钮间距 */
}
</style>
