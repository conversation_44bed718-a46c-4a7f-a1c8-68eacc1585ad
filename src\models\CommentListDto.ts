// WWE_CommentListDto.ts
export interface CommentListDto {
    id: string;
    invoiceId?: number | null;
    shipmentId?: number | null;
    content: string;
    createdById: number;
    createdAt: Date;
    files: FileDto[];
}

// WWE_CommentAddOrUpdateDto.ts
export interface CommentAddOrUpdateDto {
    id?: string | null;
    invoiceId?: number | null;
    shipmentId?: number | null;
    content: string;
    fileIds: number[];
}

// FileDto.ts
export interface FileDto {
    id: string;
    fileName: string;
    fileUrl: string;
    fileType: string;
    fileSize: number;
    createdAt: Date;
}