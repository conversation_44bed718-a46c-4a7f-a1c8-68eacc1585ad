
export default {
    wWE_AccountSearch: {
        //Search Area
    },
    wWE_AccountButtons: {
        createWWE_Account: 'New Account',
        editWWE_Account: 'Edit',
        importWWE_Account: 'Import',
    },
    wWE_AccountLabels: {
		AccountId: 'Account Id',
		AccountCode: 'WWE Account#',
		AccountName: 'Project',
		CustomerEmailAddress: 'Customer Email Address',
		FileNamingConvention: 'File Naming Convention',
		IfCombine: 'If Combine',
		RequiredFileTypes: 'Default File Types',
		CreatedById: 'Created By Id',
		CreatedByName: 'Created By',
		CreatedAt: 'Created At',
		ModifiedById: 'Modified By Id',
		ModifiedByName: 'Modified By',
		ModifiedAt: 'Modified At',
		IsDeleted: 'Is Deleted'
    },
    wWE_AccountFields: {
		AccountId: 'Account Id',
		AccountCode: 'WWE Account#',
		AccountName: 'Project',
		CustomerEmailAddress: 'Customer Email Address',
		FileNamingConvention: 'File Naming Convention',
		IfCombine: 'If Combine',
		RequiredFileTypes: 'Default File Types',
		CreatedById: 'Created By Id',
		CreatedByName: 'Created By',
		CreatedAt: 'Created At',
		ModifiedById: 'Modified By Id',
		ModifiedByName: 'Modified By',
		ModifiedAt: 'Modified At',
		IsDeleted: 'Is Deleted'
    },
    wWE_AccountDlgTips: {
        deleteError: 'There is already a configuration relationship, deletion is not allowed!',
    },
    wWE_AccountCommon: {
        ImportData: 'Import Data',
		UploadExcelFile: 'Upload Excel File',
        UploadCsvFile: 'Upload Csv File',
		Note: '(Note: This action will overwrite the data of the selected month)'
	},
};
            