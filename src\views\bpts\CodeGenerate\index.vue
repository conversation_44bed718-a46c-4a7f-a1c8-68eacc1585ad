<template>
    <div class="code-generate-container">
        <el-card :title="$t('message.codeGenerate.dbConfig')" class="mb-4">
            <el-form ref="formRef" :model="formState" :rules="rules" label-width="120px" class="db-form">
                <el-form-item :label="$t('message.codeGenerate.dbType')" prop="dbType">
                    <el-select v-model="formState.dbType" class="w-full">
                        <el-option value="sqlserver" label="SQL Server" />
                    </el-select>
                </el-form-item>

                <el-form-item :label="$t('message.codeGenerate.host')" prop="host">
                    <el-input v-model="formState.host" />
                </el-form-item>

                <el-form-item :label="$t('message.codeGenerate.port')" prop="port">
                    <el-input-number v-model="formState.port" :min="1" :max="65535" class="w-full" />
                </el-form-item>

                <el-form-item :label="$t('message.codeGenerate.database')" prop="database">
                    <el-input v-model="formState.database" />
                </el-form-item>

                <el-form-item :label="$t('message.codeGenerate.username')" prop="username">
                    <el-input v-model="formState.username" />
                </el-form-item>

                <el-form-item :label="$t('message.codeGenerate.password')" prop="password">
                    <el-input v-model="formState.password" type="password" show-password />
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" @click="onTestConnection" :loading="loading">
                        {{ $t('message.codeGenerate.testConnection') }}
                    </el-button>
                    <el-button type="success" @click="onGenerateCode" :loading="generating">
                        {{ $t('message.codeGenerate.generateCode') }}
                    </el-button>
                    <el-button @click="onSaveConfig" :loading="saving">
                        {{ $t('message.codeGenerate.saveConfig') }}
                    </el-button>
                </el-form-item>
            </el-form>
        </el-card>

        <el-card v-if="tables.length > 0" :title="$t('message.codeGenerate.tableSelect')" class="mb-4">
            <el-table :data="tables" @selection-change="handleSelectionChange" style="width: 100%" highlight-current-row
                @current-change="handleCurrentChange" :row-class-name="tableRowClassName">
                <el-table-column type="selection" width="55" />
                <el-table-column prop="name" :label="$t('message.codeGenerate.tableName')" />
                <el-table-column prop="description" :label="$t('message.codeGenerate.tableComment')" />
                <el-table-column :label="$t('message.codeGenerate.tableList')" width="150">
                    <template #default="{ row }">
                        <el-select v-model="row.tableList" class="w-full" @change="handleTableTypeChange(row)">
                            <el-option value="normal" :label="$t('message.codeGenerate.normalList')" />
                            <el-option value="tree" :label="$t('message.codeGenerate.treeList')" />
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column :label="$t('message.codeGenerate.tableComponent')" width="150">
                    <template #default="{ row }">
                        <el-select v-model="row.tableComponent" class="w-full" @change="handleComponentChange(row)">
                            <el-option value="element" :label="$t('message.codeGenerate.elementTable')" />
                            <el-option value="vxe" :label="$t('message.codeGenerate.vxeTable')" />
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column :label="$t('message.codeGenerate.actions')" width="250">
                    <template #default="{ row }">
                        <el-button type="primary" size="small" @click="onGenerateCode(row)">
                            {{ $t('message.codeGenerate.generate') }}
                        </el-button>
                        <el-button type="info" size="small" @click="showSubTableConfig(row)">
                            {{ $t('message.codeGenerate.configSubTable') }}
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <div class="table-footer">
                <el-button type="primary" :disabled="selectedTables.length === 0" @click="onBatchGenerate"
                    :loading="generating">
                    {{ $t('message.codeGenerate.batchGenerate') }}
                </el-button>
            </div>
        </el-card>

        <!-- 子表配置对话框 -->
        <el-dialog v-model="subTableDialogVisible" :title="$t('message.codeGenerate.subTableConfig')" width="900px"
            class="sub-table-dialog">
            <el-form :model="currentTableConfig" label-width="180px" class="sub-table-form">
                <el-form-item :label="$t('message.codeGenerate.mainTable')">
                    <el-input v-model="currentTableConfig.tableName" disabled />
                </el-form-item>
                <el-form-item :label="$t('message.codeGenerate.tableList')">
                    <el-select v-model="currentTableConfig.tableList" class="w-full" disabled>
                        <el-option value="normal" :label="$t('message.codeGenerate.normalList')" />
                        <el-option value="tree" :label="$t('message.codeGenerate.treeList')" />
                    </el-select>
                </el-form-item>

                <div class="sub-tables-container">
                    <!-- 子表列表 -->
                    <div v-for="(subTable, index) in currentTableConfig.subTables" :key="index" class="sub-table-item">
                        <el-divider>{{ $t('message.codeGenerate.subTable') }} {{ index + 1 }}</el-divider>
                        <el-form-item :label="$t('message.codeGenerate.subTableName')">
                            <el-select v-model="subTable.tableName" class="w-full"
                                @change="(val) => handleSubTableChange(val, index)">
                                <el-option v-for="table in tables" :key="table.name" :label="table.name"
                                    :value="table.name" />
                            </el-select>
                        </el-form-item>
                        <el-form-item :label="$t('message.codeGenerate.tableList')">
                            <el-select v-model="subTable.tableList" class="w-full">
                                <el-option value="normal" :label="$t('message.codeGenerate.normalList')" />
                                <el-option value="tree" :label="$t('message.codeGenerate.treeList')" />
                            </el-select>
                        </el-form-item>

                        <el-form-item :label="$t('message.codeGenerate.tableComponent')">
                            <el-select v-model="subTable.tableComponent" class="w-full">
                                <el-option value="element" :label="$t('message.codeGenerate.elementTable')" />
                                <el-option value="vxe" :label="$t('message.codeGenerate.vxeTable')" />
                            </el-select>
                        </el-form-item>

                        <el-form-item :label="$t('message.codeGenerate.relationType')">
                            <template #label>
                                {{ $t('message.codeGenerate.relationType') }}
                                <el-tooltip :content="$t('message.codeGenerate.relationTypeDesc')" placement="top">
                                    <el-icon class="info-icon">
                                        <InfoFilled />
                                    </el-icon>
                                </el-tooltip>
                            </template>
                            <el-select v-model="subTable.relationType" class="w-full"
                                @change="handleRelationTypeChange(index)">
                                <el-option value="oneToMany" :label="$t('message.codeGenerate.oneToMany')" />
                                <el-option value="manyToMany" :label="$t('message.codeGenerate.manyToMany')" />
                            </el-select>
                        </el-form-item>

                        <!-- 一对多关系配置 -->
                        <template v-if="subTable.relationType === 'oneToMany'">
                            <div class="relation-config one-to-many">
                                <div class="relation-title">
                                    {{ $t('message.codeGenerate.oneToManyConfig') }}
                                    <div class="relation-desc">{{ $t('message.codeGenerate.oneToManyDesc') }}</div>
                                </div>

                                <el-form-item :label="$t('message.codeGenerate.parentKey')">
                                    <template #label>
                                        {{ $t('message.codeGenerate.parentKey') }}
                                        <el-tooltip :content="$t('message.codeGenerate.parentKeyDesc')" placement="top">
                                            <el-icon class="info-icon">
                                                <InfoFilled />
                                            </el-icon>
                                        </el-tooltip>
                                    </template>
                                    <el-select v-model="subTable.parentKey" class="w-full" filterable>
                                        <el-option v-for="column in mainTableColumns" :key="column.name"
                                            :label="column.name" :value="column.name" />
                                    </el-select>
                                </el-form-item>

                                <el-form-item :label="$t('message.codeGenerate.foreignKey')">
                                    <template #label>
                                        {{ $t('message.codeGenerate.foreignKey') }}
                                        <el-tooltip :content="$t('message.codeGenerate.foreignKeyDesc')"
                                            placement="top">
                                            <el-icon class="info-icon">
                                                <InfoFilled />
                                            </el-icon>
                                        </el-tooltip>
                                    </template>
                                    <el-select v-model="subTable.foreignKey" class="w-full" filterable
                                        :disabled="!subTable.tableName">
                                        <el-option v-for="column in subTableColumns[subTable.tableName] || []"
                                            :key="column.name" :label="column.name" :value="column.name" />
                                    </el-select>
                                </el-form-item>
                            </div>
                        </template>

                        <!-- 多对多关系配置 -->
                        <template v-if="subTable.relationType === 'manyToMany'">
                            <div class="relation-config many-to-many">
                                <div class="relation-title">
                                    {{ $t('message.codeGenerate.manyToManyConfig') }}
                                    <div class="relation-desc">{{ $t('message.codeGenerate.manyToManyDesc') }}</div>
                                </div>
                                <el-form-item :label="$t('message.codeGenerate.relationTable')">
                                    <template #label>
                                        {{ $t('message.codeGenerate.relationTable') }}
                                        <el-tooltip :content="$t('message.codeGenerate.relationTableDesc')"
                                            placement="top">
                                            <el-icon class="info-icon">
                                                <InfoFilled />
                                            </el-icon>
                                        </el-tooltip>
                                    </template>
                                    <el-select v-model="subTable.manyToMany.relationTable" class="w-full" filterable
                                        @change="(val) => handleRelationTableChange(val, index)">
                                        <el-option v-for="table in tables" :key="table.name" :label="table.name"
                                            :value="table.name" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item :label="$t('message.codeGenerate.sourceKey')">
                                    <template #label>
                                        {{ $t('message.codeGenerate.sourceKey') }}
                                        <el-tooltip :content="$t('message.codeGenerate.sourceKeyDesc')" placement="top">
                                            <el-icon class="info-icon">
                                                <InfoFilled />
                                            </el-icon>
                                        </el-tooltip>
                                    </template>
                                    <el-select v-model="subTable.manyToMany.sourceKey" class="w-full" filterable>
                                        <el-option v-for="column in getRelationTableColumns(subTable)"
                                            :key="column.name" :label="column.name" :value="column.name" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item :label="$t('message.codeGenerate.targetKey')">
                                    <template #label>
                                        {{ $t('message.codeGenerate.targetKey') }}
                                        <el-tooltip :content="$t('message.codeGenerate.targetKeyDesc')" placement="top">
                                            <el-icon class="info-icon">
                                                <InfoFilled />
                                            </el-icon>
                                        </el-tooltip>
                                    </template>
                                    <el-select v-model="subTable.manyToMany.targetKey" class="w-full" filterable>
                                        <el-option v-for="column in getRelationTableColumns(subTable)"
                                            :key="column.name" :label="column.name" :value="column.name" />
                                    </el-select>
                                </el-form-item>
                            </div>
                        </template>

                        <el-button type="danger" @click="removeSubTable(index)">
                            {{ $t('message.codeGenerate.removeSubTable') }}
                        </el-button>
                    </div>
                </div>

                <el-button type="primary" @click="addSubTable">
                    {{ $t('message.codeGenerate.addSubTable') }}
                </el-button>
            </el-form>
            <template #footer>
                <el-button @click="subTableDialogVisible = false">{{ $t('message.page.cancel') }}</el-button>
                <el-button type="primary" @click="saveSubTableConfig">
                    {{ $t('message.page.confirm') }}
                </el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { useI18n } from 'vue-i18n';
import apiTestApi from '/@/api/ApiTest/index';
import { TableGenerateConfig, TableInfo, ColumnInfo } from '/@/api/ApiTest/types';
import { InfoFilled } from '@element-plus/icons-vue'

const { t } = useI18n();
const loading = ref(false);
const generating = ref(false);
const saving = ref(false);
const formRef = ref<FormInstance>();

interface FormState {
    dbType: string
    host: string
    port: number
    database: string
    username: string
    password: string
}

// 创建一个默认配置
const defaultConfig: FormState = {
    dbType: 'sqlserver',
    host: '***********',
    port: 1433,
    database: 'WWEDOCRetrival',
    username: 'WWEDOCRetrival',
    password: 'WWEDOCRetrival'
};

const formState = reactive<FormState>({ ...defaultConfig });

const rules = reactive<FormRules>({
    dbType: [
        { required: true, message: () => t('message.codeGenerate.pleaseSelectDbType'), trigger: 'change' }
    ],
    host: [
        { required: true, message: () => t('message.codeGenerate.pleaseInputHost'), trigger: 'blur' }
    ],
    port: [
        { required: true, message: () => t('message.codeGenerate.pleaseInputPort'), trigger: 'blur' }
    ],
    database: [
        { required: true, message: () => t('message.codeGenerate.pleaseInputDatabase'), trigger: 'blur' }
    ],
    username: [
        { required: true, message: () => t('message.codeGenerate.pleaseInputUsername'), trigger: 'blur' }
    ],
    password: [
        { required: true, message: () => t('message.codeGenerate.pleaseInputPassword'), trigger: 'blur' }
    ]
})

const tables = ref<TableInfo[]>([]);
const selectedTables = ref<TableInfo[]>([]);

// 测试连接成功后获取表列表
const onTestConnection = async () => {
    if (!formRef.value) return

    await formRef.value.validate(async (valid) => {
        if (valid) {
            loading.value = true
            try {
                const response = await apiTestApi.TestConnection(formState)
                if (response.data.success) {
                    ElMessage.success(t('message.codeGenerate.connectionSuccess'))
                    // 连接成功后获取表列表
                    await loadTables();
                } else {
                    ElMessage.error(response.data.resultMsg || t('message.codeGenerate.connectionFailed'))
                }
            } catch (error: any) {
                ElMessage.error(error.response?.data?.resultMsg || t('message.codeGenerate.connectionFailed'))
            } finally {
                loading.value = false
            }
        }
    })
}

// 加载数据库表列表
const loadTables = async () => {
    try {
        const response = await apiTestApi.GetTables();
        if (response.data.success) {
            tables.value = response.data.data?.map(table => ({
                name: table.name,
                schema: table.schema,
                description: table.description,
                tableList: 'normal',
                tableComponent: 'vxe'
            })) ?? [];
        } else {
            ElMessage.error(response.data.resultMsg || t('message.codeGenerate.loadTablesFailed'));
        }
    } catch (error: any) {
        ElMessage.error(error.response?.data?.resultMsg || t('message.codeGenerate.loadTablesFailed'));
    }
};

// 表格选择改变
const handleSelectionChange = (selection: TableInfo[]) => {
    selectedTables.value = selection;
};

// 生成单个表的代码
const onGenerateCode = async (row: TableInfo) => {
    generating.value = true;
    try {
        // 获取当前表的配置
        let config = tableConfigs.value.find(c => c.tableName === row.name);

        // 如果没有找到配置，创建一个默认配置
        if (!config) {
            config = {
                tableName: row.name,
                tableList: row.tableList || 'normal',
                tableComponent: row.tableComponent || 'vxe',
                subTables: []
            };
        }

        // 确保 subTables 存在且是数组
        if (!config.subTables) {
            config.subTables = [];
        }

        // 过滤掉没有选择表名的子表
        config.subTables = config.subTables.filter(subTable => subTable.tableName);

        // 确保每个子表都有必要的字段
        config.subTables = config.subTables.map(subTable => ({
            ...subTable,
            relationType: subTable.relationType || 'oneToMany',
            manyToMany: subTable.manyToMany || {
                relationTable: '',
                sourceKey: '',
                targetKey: ''
            }
        }));

        const response = await apiTestApi.GenerateCodeByTableNames([config]);
        if (response.data.success) {
            ElMessage.success(t('message.codeGenerate.generateSuccess'));
        } else {
            ElMessage.error(response.data.resultMsg || t('message.codeGenerate.generateFailed'));
        }
    } catch (error: any) {
        ElMessage.error(error.response?.data?.resultMsg || t('message.codeGenerate.generateFailed'));
    } finally {
        generating.value = false;
    }
};

// 批量生成代码
const onBatchGenerate = async () => {
    generating.value = true;
    try {
        const configs = selectedTables.value.map(table => {
            let config = tableConfigs.value.find(c => c.tableName === table.name);

            // 如果没有找到配置，创建一个默认配置
            if (config) {
                // 确保 subTables 存在且是数组
                if (!config.subTables) {
                    config.subTables = [];
                }

                // 过滤掉没有选择表名的子表
                config.subTables = config.subTables.filter(subTable => subTable.tableName);

                // 确保每个子表都有必要的字段
                config.subTables = config.subTables.map(subTable => ({
                    ...subTable,
                    relationType: subTable.relationType || 'oneToMany',
                    manyToMany: subTable.manyToMany || {
                        relationTable: '',
                        sourceKey: '',
                        targetKey: ''
                    }
                }));

                return config;
            }
            return {
                tableName: table.name,
                tableList: 'normal',
                tableComponent: 'vxe',
                subTables: []
            };
        });

        const response = await apiTestApi.GenerateCodeByTableNames(configs);
        if (response.data.success) {
            ElMessage.success(t('message.codeGenerate.batchGenerateSuccess'));
        } else {
            ElMessage.error(response.data.resultMsg || t('message.codeGenerate.batchGenerateFailed'));
        }
    } catch (error: any) {
        ElMessage.error(error.response?.data?.resultMsg || t('message.codeGenerate.batchGenerateFailed'));
    } finally {
        generating.value = false;
    }
};

// 加载配置
const loadConfig = async () => {
    try {
        const response = await apiTestApi.GetDbConfig();

        if (response.data.success && response.data.data) {
            // 使用解构赋值更新 formState
            Object.assign(formState, response.data.data);
        }
    } catch (error: any) {
        ElMessage.error(t('message.codeGenerate.loadConfigFailed'));
        // 如果加载失败，使用默认配置
        Object.assign(formState, defaultConfig);
    }
};

// 保存配置
const onSaveConfig = async () => {
    if (!formRef.value) return;

    await formRef.value.validate(async (valid) => {
        if (valid) {
            saving.value = true;
            try {
                const response = await apiTestApi.SaveDbConfig(formState);
                if (response.data.success) {
                    ElMessage.success(t('message.codeGenerate.saveConfigSuccess'));
                } else {
                    ElMessage.error(response.data.resultMsg || t('message.codeGenerate.saveConfigFailed'));
                }
            } catch (error: any) {
                ElMessage.error(error.response?.data?.resultMsg || t('message.codeGenerate.saveConfigFailed'));
            } finally {
                saving.value = false;
            }
        }
    });
};

// 页面加载时读取配置
onMounted(async () => {
    await loadConfig();
});

const subTableDialogVisible = ref(false);
const currentTableConfig = ref<TableGenerateConfig>({
    tableName: '',
    tableList: 'normal',
    subTables: []
});

// 添加获取表字段的方法
const getTableColumns = async (tableName: string, schemaName: string) => {
    try {
        const response = await apiTestApi.GetTableColumns(tableName, schemaName);
        if (response.data.success) {
            return response.data.data ?? [];
        }
        return [];
    } catch (error) {
        ElMessage.error(t('message.codeGenerate.loadColumnsFailed'));
        return [];
    }
};

// 修改显示子表配置对话框方法
const showSubTableConfig = async (row: TableInfo) => {
    // 清理之前的字段缓存
    subTableColumns.value = {};

    // 查找已有配置或创建新配置
    const existingConfig = tableConfigs.value.find(c => c.tableName === row.name);
    currentTableConfig.value = existingConfig || {
        tableName: row.name,
        tableList: row.tableList || 'normal',
        tableComponent: row.tableComponent || 'vxe',
        subTables: []
    };

    // 获取主表字段
    mainTableColumns.value = await getTableColumns(row.name, row.schema);

    // 如果有子表配置，预加载子表字段
    for (const subTable of currentTableConfig.value.subTables) {
        if (subTable.tableName) {
            const table = tables.value.find(t => t.name === subTable.tableName);
            if (table) {
                const columns = await getTableColumns(subTable.tableName, table.schema);
                subTableColumns.value[subTable.tableName] = columns;
            }
        }
    }

    subTableDialogVisible.value = true;
};

// 添加子表
const addSubTable = () => {
    currentTableConfig.value.subTables.push({
        tableName: '',
        tableList: 'normal',
        tableComponent: 'vxe',
        relationType: 'oneToMany',  // 默认为一对多关系
        foreignKey: '',
        parentKey: '',
        manyToMany: {              // 初始化多对多配置
            relationTable: '',
            sourceKey: '',
            targetKey: ''
        }
    });
};

// 移除子表
const removeSubTable = (index: number) => {
    const subTable = currentTableConfig.value.subTables[index];
    if (subTable.tableName) {
        delete subTableColumns.value[subTable.tableName];
    }
    currentTableConfig.value.subTables.splice(index, 1);
};

// 保存子表配置
const saveSubTableConfig = () => {
    // 过滤掉没有选择表名的子表
    currentTableConfig.value.subTables = currentTableConfig.value.subTables.filter(
        subTable => subTable.tableName
    );

    // 验证每个子表的配置
    for (const subTable of currentTableConfig.value.subTables) {
        // 确保关系类型存在
        if (!subTable.relationType) {
            subTable.relationType = 'oneToMany';
        }

        // 确保多对多配置存在
        if (!subTable.manyToMany) {
            subTable.manyToMany = {
                relationTable: '',
                sourceKey: '',
                targetKey: ''
            };
        }

        // 确保表格组件类型存在
        if (!subTable.tableComponent) {
            subTable.tableComponent = 'vxe';
        }
    }

    // 保存配置
    const index = tableConfigs.value.findIndex(c => c.tableName === currentTableConfig.value.tableName);
    if (index >= 0) {
        tableConfigs.value[index] = { ...currentTableConfig.value };
    } else {
        tableConfigs.value.push({ ...currentTableConfig.value });
    }
    subTableDialogVisible.value = false;
};

// 存储所有表配置
const tableConfigs = ref<TableGenerateConfig[]>([]);

// 添加主表字段列表
const mainTableColumns = ref<ColumnInfo[]>([]);

// 添加处理表格类型变化的方法
const handleTableTypeChange = (row: TableInfo) => {
    // 更新表格配置
    const config = tableConfigs.value.find(c => c.tableName === row.name);
    if (config) {
        config.tableList = row.tableList || 'normal';
        config.tableComponent = row.tableComponent || 'vxe';
    } else {
        tableConfigs.value.push({
            tableName: row.name,
            tableList: row.tableList || 'normal',
            tableComponent: row.tableComponent || 'vxe',
            subTables: [] as SubTableConfig[]
        });
    }
};

// 当前选中行
const currentRow = ref<TableInfo | null>(null);

// 处理当前行变化
const handleCurrentChange = (val: TableInfo | null) => {
    currentRow.value = val;
};

// 设置行的 class
const tableRowClassName = ({ row }: { row: TableInfo }) => {
    return selectedTables.value.some(selected => selected.name === row.name) ? 'selected-row' : '';
};

// 添加子表字段列表的存储
interface SubTableColumns {
    [key: string]: ColumnInfo[];  // key 是表名，值是字段列表
}
const subTableColumns = ref<SubTableColumns>({});

// 修改子表变化处理方法
const handleSubTableChange = async (tableName: string, index: number) => {
    if (!tableName) return;

    // 获取选中表的 schema
    const table = tables.value.find(t => t.name === tableName);
    if (!table) return;

    // 加载子表字段
    const columns = await getTableColumns(tableName, table.schema);
    console.log('子表字段:', columns); // 添加日志
    subTableColumns.value[tableName] = columns;

    // 确保子表配置包含必要的字段
    const subTable = currentTableConfig.value.subTables[index];
    if (!subTable.relationType) {
        subTable.relationType = 'oneToMany';
    }
    if (!subTable.manyToMany) {
        subTable.manyToMany = {
            relationTable: '',
            sourceKey: '',
            targetKey: ''
        };
    }
};

// 处理组件类型变化
const handleComponentChange = (row: TableInfo) => {
    // 更新表格配置
    const config = tableConfigs.value.find(c => c.tableName === row.name);
    if (config) {
        config.tableComponent = row.tableComponent || 'vxe';
    } else {
        tableConfigs.value.push({
            tableName: row.name,
            tableList: row.tableList || 'normal',
            tableComponent: row.tableComponent || 'vxe',
            subTables: []
        });
    }
};

// 处理关系类型变化
const handleRelationTypeChange = (index: number) => {
    const subTable = currentTableConfig.value.subTables[index];
    // 确保 manyToMany 对象存在
    if (!subTable.manyToMany) {
        subTable.manyToMany = {
            relationTable: '',
            sourceKey: '',
            targetKey: ''
        };
    }

    // 清理相关字段
    if (subTable.relationType === 'manyToMany') {
        subTable.foreignKey = '';
        subTable.parentKey = '';
    } else {
        subTable.manyToMany.relationTable = '';
        subTable.manyToMany.sourceKey = '';
        subTable.manyToMany.targetKey = '';
    }
};

// 获取关系表的字段
const getRelationTableColumns = (subTable: SubTableConfig) => {
    if (!subTable.manyToMany?.relationTable) return [];
    const columns = subTableColumns.value[subTable.manyToMany.relationTable] || [];
    console.log('关系表字段:', columns); // 添加日志以便调试
    return columns;
};

// 修改多对多关系表选择
const handleRelationTableChange = async (tableName: string, index: number) => {
    if (!tableName) return;

    // 获取选中表的 schema
    const table = tables.value.find(t => t.name === tableName);
    if (!table) return;

    // 加载关系表字段
    const columns = await getTableColumns(tableName, table.schema);
    subTableColumns.value[tableName] = columns;
};
</script>

<style scoped>
.code-generate-container {
    padding: 24px;
}

.db-form {
    max-width: 600px;
    margin: 0 auto;
}

.w-full {
    width: 100%;
}

.el-button {
    margin-right: 12px;
}

.mb-4 {
    margin-bottom: 16px;
}

.table-footer {
    margin-top: 16px;
    text-align: right;
}

.sub-table-dialog :deep(.el-dialog__body) {
    max-height: unset;
    overflow-y: auto;
    padding-right: 10px;
}

.sub-table-form {
    padding-right: 10px;
    min-width: 600px;
}

.sub-table-form :deep(.el-form-item) {
    position: relative;
}

.sub-table-form :deep(.el-form-item__label) {
    display: flex;
    align-items: center;
    min-width: 180px;
    width: 180px;
}

/* 子表列表容器 */
.sub-tables-container {
    max-height: 500px;
    /* 约两个子表的高度 */
    overflow-y: auto;
    margin: 20px 0;
    padding-right: 10px;
}

.sub-table-item {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    position: relative;
}

.sub-table-item .el-button {
    position: absolute;
    right: 15px;
    top: 15px;
}

/* 美化滚动条 */
.sub-table-dialog :deep(.el-dialog__body)::-webkit-scrollbar {
    width: 6px;
}

.sub-table-dialog :deep(.el-dialog__body)::-webkit-scrollbar-thumb {
    background: #dcdfe6;
    border-radius: 3px;
}

.sub-table-dialog :deep(.el-dialog__body)::-webkit-scrollbar-track {
    background: #f5f7fa;
}

:deep(.selected-row) {
    background-color: var(--el-table-row-hover-bg-color);
}

/* 关系配置区域样式 */
.relation-config {
    margin: 16px 0;
    padding: 16px;
    border-radius: 8px;
    background-color: var(--el-fill-color-light);
    position: relative;
}

.relation-title {
    font-size: 14px;
    font-weight: bold;
    color: var(--el-text-color-primary);
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--el-border-color-light);
}

/* 一对多关系样式 */
.one-to-many {
    border-left: 4px solid var(--el-color-primary);
}

/* 多对多关系样式 */
.many-to-many {
    border-left: 4px solid var(--el-color-success);
}

.info-icon {
    margin-left: 4px;
    color: var(--el-color-info);
    cursor: help;
}

.relation-desc {
    font-size: 12px;
    font-weight: normal;
    color: var(--el-text-color-secondary);
    margin-top: 4px;
    line-height: 1.4;
}

/* 防止表单标签换行 */
:deep(.el-form-item__label) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>