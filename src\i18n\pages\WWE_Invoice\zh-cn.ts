
export default {
    wWE_InvoiceSearch: {
        //Search Area
    },
    wWE_InvoiceButtons: {
        createWWE_Invoice: 'New Invoice',
        editWWE_Invoice: 'Edit Invoice',
        importWWE_Invoice: 'Import Invoice',
		MassMethodUpdate:'Mass Method Update',
    },
    wWE_InvoiceLabels: {
		InvoiceId: 'Invoice Id',
		AccountId: 'Account Id',
		AccountName: 'Project',
		CarrierId: 'Carrier Id',
		CarrierName: 'Carrier',
		ImageId: 'Image Id',
		InvoiceNumber: 'Invoice Number',
		ProNumber: 'Pro Number',
		Amount: 'Amount',
		DocRetrievalMethod: 'Retrieval Method',
		DocRetrievedStatus: 'Retrieval Status',
		DocRetrievalType: 'Retrieval Type',
		Status: 'Status',
		BOL: 'BOL',
		POD: 'POD',
		Others: 'Others',
		CarrierContact: 'Carrier Contact',
		CarrierContactEmail: 'Carrier Contact Email',
		CarrierContactPhone: 'Carrier Contact Phone',
		AgingDays: 'Aging Days',
		BatchNumber: 'Batch Number',
		Source: 'Source',
		Comment: 'Comment',
		CreatedById: 'Created By Id',
		CreatedByName: 'Created By',
		CreatedAt: 'Created At',
		ModifiedById: 'Modified By Id',
		ModifiedByName: 'Modified By',
		ModifiedAt: 'Modified At',
		IsDeleted: 'Is Deleted'

    },
    wWE_InvoiceFields: {

		InvoiceId: 'Invoice Id',
		AccountId: 'Account Id',
		AccountName: 'Project',
		CarrierId: 'Carrier Id',
		CarrierName: 'Carrier',
		ImageId: 'Image Id',
		InvoiceNumber: 'Invoice Number',
		ProNumber: 'Pro Number',
		Amount: 'Amount',
		DocRetrievalMethod: 'Retrieval Method',
		DocRetrievedStatus: 'Retrieval Status',
		DocRetrievalType: 'Retrieval Type',
		Status: 'Status',
		BOL: 'BOL',
		POD: 'POD',
		Others: 'Others',
		CarrierContact: 'Carrier Contact',
		CarrierContactEmail: 'Carrier Contact Email',
		CarrierContactPhone: 'Carrier Contact Phone',
		AgingDays: 'Aging Days',
		BatchNumber: 'Batch Number',
		Source: 'Source',
		Comment: 'Comment',
		CreatedById: 'Created By Id',
		CreatedByName: 'Created By',
		CreatedAt: 'Created At',
		ModifiedById: 'Modified By Id',
		ModifiedByName: 'Modified By',
		ModifiedAt: 'Modified At',
		IsDeleted: 'Is Deleted'

    },
    wWE_InvoiceDlgTips: {
        deleteError: 'There is already a configuration relationship, deletion is not allowed!',
    },
    wWE_InvoiceCommon: {
        ImportData: 'Import Data',
		UploadExcelFile: 'Upload Excel File',
        UploadCsvFile: 'Upload Csv File',
		Note: '(Note: This action will overwrite the data of the selected month)'
	},
};
            