<template>
	<div class="container layout-pd">
		<header class="header">
			<h1>How can we help?</h1>
			<el-input v-model="searchContent" placeholder="Search" class="search-box" clearable>
				<template #prefix>
					<el-icon>
						<ele-Search />
					</el-icon>
				</template>
			</el-input>
		</header>
		<main class="content">
			<div v-for="category in categories" :key="category.id" class="document-card">
				<el-icon class="document-icon">
					<ele-Document></ele-Document>
				</el-icon>

				<div class="document-info">
					<h2>{{ category.name }}</h2>
					<p>{{ category.description }}</p>
					<p>
						<el-link type="primary" :href="category.filePath" target="_blank">{{ category.fileName }}</el-link>
					</p>
				</div>
			</div>
		</main>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';

const searchContent = ref('');

const categories = reactive([
	{
		id: 1,
		name: 'Help Documents',
		description: 'User Manual',
		fileName: 'BPTS Screenshots  WWEX Dashboard Operation Manual_Draft_20240821.pdf',
		filePath: '/img/BPTS Screenshots  WWEX Dashboard Operation Manual_Draft_20240821.pdf',
	},
]);
</script>

<style scoped>
* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

.container {
	max-width: 100%;
	margin: 0 auto;
	height: 100%;
	display: flex;
	flex-direction: column;
    background-color: #fff;
}

.header {
	background-color: #fffbfb;
	padding: 40px 20px;
	text-align: center;
	width: 100%;
	height: 30%;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}

.header h1 {
	font-size: 2em;
	margin-bottom: 20px;
}

.search-box {
	width: 100%;
	max-width: 600px;
	padding: 10px;
	font-size: 1em;
}

.content {
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	align-items: center;
	background-color: #f9f9f9;
	padding-top: 20px;
	height: 70%;
	width: 100%;
}

.document-card {
	background-color: #f9f9f9;
	border: 1px solid #ddd;
	border-radius: 10px;
	padding: 20px;
	width: 80%;
	max-width: 800px;
	margin-bottom: 20px;
	display: flex;
	align-items: center;
}

.document-icon {
	width: 50px;
	margin-right: 20px;
	font-size: 30px;
	color: green;
}

.document-info h2 {
	margin-bottom: 10px;
	font-size: 1.5em;
}

.document-info p {
	color: #999;
}

@media (max-width: 768px) {
	.header h1 {
		font-size: 1.5em;
	}

	.search-box {
		width: 100%;
		font-size: 0.9em;
	}

	.document-card {
		flex-direction: row;
		align-items: center;
		margin: 20 20px;
	}

	.document-icon {
		width: 40px;
		margin-right: 10px;
	}
}
</style>
