﻿<template>
	<div class="emailTemplate-edit-container">
		<el-drawer :title="title" v-model="isShowDialog" destroy-on-close draggable :close-on-click-modal="false"
			:close-on-press-escape="false" :size="800">
			<template #header>
				<h3>{{ title }}</h3>
			</template>
			<div class="drawer-content">
				<el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="150px">
					<el-row :gutter="35">
						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb10">
							<el-form-item :label="$t('message.emailFields.templateName')" prop="name">
								<el-input v-model="ruleForm.name" size="middle" />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb10">
							<el-form-item :label="$t('message.emailFields.templateStatus')">
								<el-select v-model="ruleForm.status"
									:placeholder="$t('message.page.selectKeyPlaceholder')" clearable class="w100"
									size="middle">
									<el-option label="Active" :value="0"></el-option>
									<el-option label="Inactive" :value="1"></el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb10">
							<el-form-item :label="$t('message.emailFields.templateDescription')" prop="description">
								<el-input v-model="ruleForm.description" type="textarea" maxlength="150"
									size="middle"></el-input>
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb10">
							<el-form-item :label="$t('message.emailFields.emailSubject')" prop="subject">
								<el-input v-model="ruleForm.subject" size="middle" />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
							<el-form-item :label="$t('message.emailFields.emailBody')" prop="body">
								<Editor v-model="ruleForm.body" />
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>
				<div class="drawer-footer mt15">
					<span class="dialog-footer">
						<el-button @click="onCancel" size="small">{{ $t('message.page.buttonCancel') }}</el-button>
						<el-button v-if="dialogParams.action === 'Create'" @click="onInitForm" size="small">
							{{ $t('message.page.buttonReset') }}
						</el-button>
						<el-button v-auth="'emailTemplate.Delete'" v-if="dialogParams.action === 'Edit'"
							@click="onDelete" type="primary" size="small">
							{{ $t('message.page.buttonDelete') }}
						</el-button>
						<el-button :loading="saveLoading" type="primary" @click="onSubmit" size="small">{{
							$t('message.page.buttonSave') }}</el-button>
					</span>
				</div>
			</div>
		</el-drawer>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, getCurrentInstance, defineComponent, defineAsyncComponent } from 'vue';
import { isNotEmptyOrNull, parseThanZero } from '/@/utils';
import { ElMessageBox, ElMessage } from 'element-plus';

import emailTemplateApi from '/@/api/emailTemplate';
import { useI18n } from 'vue-i18n';

interface DialogParams {
	action: string;
	templateId: number;
}

export default defineComponent({
	name: 'emailTemplateCreateOrEdit',
	components: {
		Editor: defineAsyncComponent(() => import('/@/components/editor/indexV5.vue')),
	},
	setup(props, context) {
		const { t } = useI18n();
		const { proxy } = getCurrentInstance() as any;
		const state = reactive({
			title: t('message.emailCommon.createTemplate'),
			isShowDialog: false,
			saveLoading: false,
			deleteLoading: false,
			dialogParams: {
				action: '',
				id: -1,
			},
			ruleForm: {
				templateId: 0, //
				name: '', //
				subject: '', //
				body: '', //
				description: '', //
				status: 0,
			},
			rules: {
				name: [{ required: true, message: 'Please input', trigger: 'blur' }],
				subject: [{ required: true, message: 'Please input', trigger: 'blur' }],
				body: [{ required: true, message: 'Please input', trigger: 'blur' }],
			},
		});
		// 打开弹窗
		const openDialog = (parmas: DialogParams) => {
			state.dialogParams = parmas;

			onInitForm();

			if (parmas.action == 'Create') {
				state.title = t('message.emailCommon.createTemplate');
			} else if (parmas.action == 'Edit') {
				state.title = t('message.emailCommon.editTemplate');
				getData(parmas.templateId);
			} else {
				ElMessage.error('Parameter action cannot be empty.');
			}
		};
		// 关闭弹窗
		const closeDialog = () => {
			onInitForm();
			state.isShowDialog = false;
		};
		// 取消
		const onCancel = () => {
			closeDialog();
		};
		// 保存
		const onSubmit = () => {
			proxy.$refs.ruleFormRef.validate((valid: any) => {
				if (!valid) {
					return;
				}
				var obj = state.ruleForm;
				state.saveLoading = true;
				emailTemplateApi
					.Save(obj)
					.then(() => {
						ElMessage.success('Succeed');
						context.emit('fetchData');
						closeDialog();
					})
					.catch((error: HandledError) => {
						if (!error.isHandled) {
							const errorCode = error.code;
							const errorMessage = error.message;
							ElMessage.error(errorMessage);
						}
					})
					.finally(() => {
						state.saveLoading = false;
					});
			});
		};
		//根据id获取完整的信息
		const getData = (id: any) => {
			emailTemplateApi.GetByKey(id).then((rs) => {
				state.ruleForm = Object.assign({}, rs.data);
			});
		};
		//删除纪录
		const onDelete = () => {
			ElMessageBox.confirm(t('message.page.dlgDeleteText'), t('message.page.dlgTip'), {
				confirmButtonText: t('message.page.confirm'),
				cancelButtonText: t('message.page.cancel'),
				type: 'warning',
			}).then(() => {
				state.deleteLoading = true;
				emailTemplateApi
					.DeleteByKey(state.ruleForm.templateId)
					.then(() => {
						ElMessage.success('Succeed');
						context.emit('fetchData');
						closeDialog();
					})
					.catch((error: HandledError) => {
						if (!error.isHandled) {
							const errorCode = error.code;
							const errorMessage = error.message;
							ElMessage.error(errorMessage);
						}
					})
					.finally(() => {
						state.deleteLoading = false;
					});
			});
		};
		// 重置表单
		const onInitForm = () => {
			state.isShowDialog = true;
			if (proxy.$refs.ruleFormRef) {
				proxy.$refs.ruleFormRef.resetFields();
			}
			state.ruleForm = {
				templateId: 0, //
				name: '', //
				subject: '', //
				body: '', //
				status: 0,
				description: '', //
			};
		};
		return {
			openDialog,
			closeDialog,
			onCancel,
			onSubmit,
			onDelete,
			onInitForm,
			...toRefs(state),
		};
	},
});
</script>

<style scoped>
.drawer-content {
	padding: 20px;
}

.drawer-footer {
	text-align: right;
}
</style>
