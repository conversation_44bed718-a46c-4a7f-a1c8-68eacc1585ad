export interface WWEImagesDto {
	id: string;
	filePath: string;
	originalFileName: string;
	fileName: string;
	fileExt: string;
	fileMimeType: string;
	fileSize: number;
	priority: string;
	queue: number;
	indexed: boolean;
	comment: string;
	batchNum: string;
	receivingType: string;
	createdByName: string;
	modifiedByName: string;
}

export interface UploadFormDataStructure {
	file: File;
	batchNumber: string;
	receivingType?: string;
	priority?: string;
	queue?: number;	
}

export interface ImportRequest {
	batchNumber: string;
	receivingType?: string;
	priority?: string;
	queue?: number;
}