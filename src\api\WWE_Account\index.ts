import BaseApi from '/@/api/BaseApi';
import request from '/@/utils/request';

class wWE_AccountApi extends BaseApi {
	QueryV2(data: any) {
		return request({
			url: this.baseurl + 'QueryV2',
			method: 'post',
			data,
		});
	}

	GetAccountOptions(params?: any) {
		return request({
			url: this.baseurl + 'GetAccountOptions',
			method: 'get',
			params,
		});
	}

	GetAccountTree(params?: any) {
		return request({
			url: this.baseurl + 'GetAccountTree',
			method: 'get',
			params,
		});
	}
}

export default new wWE_AccountApi('/api/WWE_Account/', 'accountId');
