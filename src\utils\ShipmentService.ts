export const DocumentTypes = {
	BOL: 'BOL',
	POD: 'POD',
	DR: 'DR',
	OTHERS: 'Others',
} as const;

export const getDocumentTypeStatus = (row: any, type: 'BOL' | 'POD' | 'Others'): string | null => {
    if (!row.docRetrievalType) return null;
    
    // Case-insensitive check if row has the property (ignoring case)
    const typeLower = type.toLowerCase();
    const rowKeys = Object.keys(row);
    const hasTypeProperty = rowKeys.some(key => key.toLowerCase() === typeLower);
    
    if (hasTypeProperty) {
        // Find the actual key (case-insensitive) and return its value if truthy
        const actualKey = rowKeys.find(key => key.toLowerCase() === typeLower);
        if (actualKey && row[actualKey]) return type;
    }

    // Convert comma-separated string to array (case-insensitive trim)
    const docTypes = row.docRetrievalType.split(',').map((t: string) => t.trim().toUpperCase());

    switch (type) {
        case 'BOL':
            return docTypes.includes('BOL') ? 'BOL' : null;
        case 'POD':
            // POD includes both POD and DR types (case-insensitive)
            return docTypes.some(t => ['POD', 'DR'].includes(t)) ? 'POD' : null;
        case 'Others':
            // Exclude BOL, POD, DR (case-insensitive)
            const otherTypes = docTypes.filter(t => !['BOL', 'POD', 'DR'].includes(t));
            return otherTypes.length > 0 ? 'Others' : null;
        default:
            return null;
    }
};