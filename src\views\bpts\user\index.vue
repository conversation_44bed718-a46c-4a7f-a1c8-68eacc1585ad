﻿<template>
	<div class="top-container">
		<div class="list-search">
			<el-card class="search-form-card" shadow="never">
				<el-form @keyup.enter="onSearch" label-width="90px">
					<el-row :gutter="24">
						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="6" class="mb1 mt1">
							<el-form-item :label="$t('message.userFields.status')">
								<el-select :placeholder="$t('message.page.selectKeyPlaceholder')"
									v-model="tableData.param.status" clearable size="middle" class="w-20">
									<el-option :label="$t('message.userFields.Active')" value="0"></el-option>
									<el-option :label="$t('message.userFields.Inactive')" value="1"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="6" class="mb1 mt1">
							<el-form-item :label="$t('message.userFields.userName')">
								<el-input v-model="tableData.param.userName" clearable size="middle"
									:placeholder="$t('message.page.searchKeyPlaceholder')" class="w-20"></el-input>
							</el-form-item>
						</el-col>

						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="6" class="mb1 mt1">
							<el-form-item :label="$t('message.userFields.email')">
								<el-input v-model="tableData.param.email" clearable size="middle"
									:placeholder="$t('message.page.searchKeyPlaceholder')" class="w-20"></el-input>
							</el-form-item>
						</el-col>

						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="6" class="mb1 mt1">
							<el-form-item :label="$t('message.userFields.firstName')">
								<el-input v-model="tableData.param.firstName" clearable size="middle"
									:placeholder="$t('message.page.searchKeyPlaceholder')" class="w-20"></el-input>
							</el-form-item>
						</el-col>

						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="6" class="mb1 mt1">
							<el-form-item :label="$t('message.userFields.lastName')">
								<el-input v-model="tableData.param.lastName" clearable size="middle"
									:placeholder="$t('message.page.searchKeyPlaceholder')" class="w-20"></el-input>
							</el-form-item>
						</el-col>

						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="6" class="mb1 mt1">
							<el-form-item label=" ">
								<div class="form_search_btn">
									<el-button type="primary" @click="onSearch" size="small" icon="search">{{
										$t('message.page.buttonSearch') }}</el-button>
									<el-button type="danger" @click="onSearchReSet" size="small" icon="refresh-left">{{
										$t('message.page.buttonReset') }}</el-button>
								</div>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>
			</el-card>
		</div>

		<div class="list-table">
			<el-card shadow="never">
				<el-header class="table_header">
					<div class="left-panel">
						<el-button v-auth="'systemUser.Create'" type="primary" size="small" @click="onAdd" icon="plus">
							{{ $t('message.userButtons.buttonNew') }}
						</el-button>
						<el-button v-auth="'systemUser.BitchDelete'" type="danger" size="small" @click="onDeleteByList"
							:disabled="tableData.selection.length == 0" icon="delete">
							{{ $t('message.page.buttonDeleteBatch') }}
						</el-button>
					</div>
					<div class="right-panel">
						<Export ref="exportRef" pageName="User" :pageParams="tableData.param" />
					</div>
				</el-header>

				<div class="scTable">
					<div class="scTable-table">
						<el-table ref="tableRef" :data="tableData.data" v-loading="tableData.loading" border
							@selection-change="selectionChange" highlight-current-row :row-style="{ height: '33px' }"
							:cell-style="{ padding: '0px' }" :default-sort="{ prop: 'createTime', order: 'descending' }"
							@sort-change="onSortChange" @row-click="rowClick">
							<template #empty>
								<el-empty :description="$t('message.page.emptyDescription')"
									:image-size="100"></el-empty>
							</template>

							<el-table-column type="selection" />

							<el-table-column :label="$t('message.userFields.userName')" prop="userName" width="200">
								<template #default="{ row }">
									<el-link @click="onEdit(row)" type="primary">
										{{ row.userName }}
									</el-link>
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.userFields.fullName')" width="200">
								<template #default="{ row }">
									<span class="">{{ row.firstName + ' ' + (row.lastName == null ? '' : row.lastName)
									}}</span>
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.userFields.email')" width="262">
								<template #default="{ row }">
									<span>{{ row.email }}</span>
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.userFields.status')" width="200">
								<template #default="{ row }">
									<el-tag type="danger" v-if="row.status == '1'">
										{{ $t('message.userFields.Inactive') }}
									</el-tag>
									<el-tag type="success" v-if="row.status == '0'">
										{{ $t('message.userFields.Active') }}
									</el-tag>
								</template>
							</el-table-column>

							<el-table-column label="Roles" width="256">
								<template #default="{ row }">
									<span>{{ formatNames(row.roleNames) }}</span>
								</template>
							</el-table-column>

							<el-table-column label="Created At" prop="createTime" sortable="custom"
								:sort-orders="['ascending', 'descending']" width="256">
								<template #default="{ row }">
									{{ row.createTime }}
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.userFields.lockout')" width="120">
								<template #default="{ row }">
									{{ row.hasLock ? 'Locked' : '' }}
								</template>
							</el-table-column>

							<el-table-column fixed="right" :label="$t('message.page.actions')" width="90">
								<template #default="{ row }">
									<el-tooltip v-if="0 > 1" class="box-item" effect="dark" content="View"
										placement="bottom">
										<el-button size="mini" type="text" @click="onDetail(row)" icon="view">
											<!-- {{ $t('message.page.actionsView') }} -->
										</el-button>
									</el-tooltip>

									<!-- <el-row> -->

									<!-- <el-tooltip class="box-item" effect="dark" content="Edit" placement="bottom">
										<el-button v-auth="'systemUser.Edit'" size="mini" type="text"
											@click="onEdit(row)" icon="Edit">
										</el-button>
									</el-tooltip>

									<el-tooltip class="box-item" effect="dark" content="Delete" placement="bottom">
										<el-button v-auth="'systemUser.Delete'" size="mini" type="text"
											style="color: #ff3a3a" @click="onDelete(row)" icon="Delete">
										</el-button>
									</el-tooltip> -->

									<el-tooltip class="box-item" effect="dark" :content="$t('message.limits.Edit')"
										placement="bottom">
										<vxe-button mode="text" status="primary" icon="vxe-icon-edit"
											v-auth="'systemUser.Edit'" @click="onEdit(row)"></vxe-button>
									</el-tooltip>

									<el-tooltip class="box-item" effect="dark" :content="$t('message.limits.Delete')"
										placement="bottom">
										<vxe-button mode="text" status="error" icon="vxe-icon-delete"
											v-auth="'systemUser.Delete'" @click="onDelete(row)"></vxe-button>
									</el-tooltip>

									<!-- </el-row> -->
								</template>
							</el-table-column>
						</el-table>
					</div>
					<div class="scTable-page">
						<el-pagination @size-change="onSizechange" @current-change="onCurrentChange" :pager-count="5"
							:page-sizes="[20, 30, 50, 100]" v-model:current-page="tableData.param.pageIndex" background
							v-model:page-size="tableData.param.pageSize"
							layout="total, sizes, prev, pager, next, jumper" :total="tableData.total"
							small></el-pagination>
					</div>
				</div>
			</el-card>
		</div>

		<ResetPassword ref="resetPasswordRef" />
		<CreateOrEdit ref="createOrEditRef" @fetchData="onInit" />

		<el-drawer v-model="infoDrawer" :title="$t('message.page.detailTitle')" :size="600" destroy-on-close>
			<Detail ref="detailRef" :info="detailObj"></Detail>
		</el-drawer>
	</div>
</template>

<script lang="ts">
import { nextTick, toRefs, reactive, onMounted, ref, defineComponent, watch, getCurrentInstance, defineAsyncComponent, computed } from 'vue';
import { ElMessageBox, ElMessage, FormInstance } from 'element-plus';
import { formatStrDate, formatDateTime } from '/@/utils/formatTime';

import userApi from '/@/api/user/index';

import Detail from './components/detail.vue';
import CreateOrEdit from './components/createOrEdit.vue';
import ResetPassword from './components/resetPassword.vue';

import { useI18n } from 'vue-i18n';

import dictItemApi from '/@/api/dictItem/index';

import { isNotEmptyOrNull, parseThanZero } from '/@/utils';

import type { TableInstance } from 'element-plus';
const tableLayout = ref<TableInstance['tableLayout']>('auto');
import Export from '/@/components/export/index.vue';

export default defineComponent({
	name: 'systemUser',
	components: { Detail, CreateOrEdit, ResetPassword, Export },
	data() {
		return {};
	},
	setup() {
		const tableWrapperBody = ref(null as any);
		const collapse = ref(true);
		const collapseUp = ref(true);
		const visible = ref(true);
		const exportRef = ref();
		const treeRef = ref();
		const filterTreeVal = ref();

		watch(filterTreeVal, (val) => {
			if (val === '') {
				state.tableData.param.orgId = undefined;
				onInit();
			} else {
				treeRef.value!.filter(val);
			}
		});

		const { t } = useI18n();

		const delegateRef = ref();
		const createOrEditRef = ref();
		const resetPasswordRef = ref();

		const changePwdRef = ref(false);
		const printMain = ref(null);
		const tableRef = ref();
		const state = reactive({
			tableData: {
				data: [],
				total: 0,
				loading: false,
				selection: [],
				param: {
					status: '',
					email: '',
					userName: '',
					firstName: '',
					lastName: '',
					orgType: '',
					orgId: undefined,
					pageIndex: 1,
					pageSize: 20,
					searchKey: '',
					order: 'createTime',
					sort: 'desc',
					ids: [],
					ischeckType: 0,
				},
			} as any,
			orgTreeData: [],
			infoDrawer: false,
			detailObj: {},
			statusOpts: [],
			priorityData: [],
			countData: [],
			pickerDate: [],
			categoryOpts: [],
			customerOpts: [],
		});

		const displayDate = (val: string) => {
			let time_split_arr = val.split(' ');
			let timeVal = time_split_arr[0];

			return timeVal;
		};

		const displayTime = (val: string) => {
			let time_split_arr = val.split(' ');
			let timeVal = time_split_arr[1] + ' ' + time_split_arr[2];

			return timeVal;
		};

		const onInit = () => {
			state.tableData.loading = true;

			userApi
				.Query(state.tableData.param)
				.then((rs) => {
					state.tableData.data = rs.data;
					state.tableData.total = rs.totalCount;
					let obj = { entityName: 'User', pageParams: state.tableData.param };
					exportRef?.value?.getColumns(obj);
				})
				.catch((rs) => { })
				.finally(() => (state.tableData.loading = false));
		};

		const onSearch = () => {
			onInit();
		};

		const onSearchReSet = () => {
			state.tableData.param.status = '';
			state.tableData.param.userName = '';
			state.tableData.param.lastName = '';
			state.tableData.param.firstName = '';
			state.tableData.param.email = '';
			state.tableData.param.orgId = undefined;
			onInit();
		};

		const convertStatusVal = (val: any) => {
			if (val === 0) {
				return 'Active';
			} else {
				return 'InActive';
			}
		};

		const onAdd = () => {
			createOrEditRef.value.openDialog({ action: 'Create' });
		};

		const onEdit = (row: any) => {
			createOrEditRef.value.openDialog({ action: 'Edit', id: row.id });
		};

		const onDelete = (row: any) => {
			ElMessageBox.confirm(t('message.page.dlgDeleteText'), t('message.page.dlgTip'), {
				confirmButtonText: t('message.page.confirm'),
				cancelButtonText: t('message.page.cancel'),
				type: 'warning',
			}).then(() => {
				userApi
					.DeleteByKey(row.id)
					.then((rs) => {
						if (!rs.data) {
							ElMessage.error(rs.resultMsg);
							return;
						}
						onInit();
					})
					.catch((error: HandledError) => {
						if (!error.isHandled) {
							const errorCode = error.code;
							const errorMessage = error.message;
							ElMessage.error(errorMessage);
						}
					})
			});
		};

		const onDeleteByList = () => {
			ElMessageBox.confirm(
				t('message.page.dlgDeleteSelectText1') + ` ${state.tableData.selection.length} ` + t('message.page.dlgDeleteSelectText2'),
				t('message.page.dlgTip'),
				{
					type: 'warning',
				}
			).then(() => {
				var items = state.tableData.selection.map((a: { id: any }) => {
					return a.id;
				});
				userApi.DeleteMany(items).then((rs) => {
					if (rs.data == 0) {
						ElMessage.error(rs.resultMsg);
						return;
					}
					onInit();
				});
			});
		};

		const onSelectMasterUser = (value: any, row: any) => {
			ElMessageBox.confirm(t('message.userFields.dlgMasterUser'), t('message.page.dlgTip'), {
				confirmButtonText: t('message.page.confirm'),
				cancelButtonText: t('message.page.cancel'),
				type: 'warning',
			})
				.then(() => {
					var obj = { UserId: row.id, IsMasterUser: value };
					userApi.UpdateMasterUserType(obj).then((rs) => {
						if (rs.data == false) {
							ElMessage.error(rs.resultMsg);
							return;
						} else {
							ElMessage.success(t('message.page.saveSuccess'));
						}
						onInit();
					});
				})
				.catch((action) => {
					if (action === 'cancel') {
						row.master_User = !value;
					}
				});
		};

		const onDetail = (row: any) => {
			state.detailObj = {};

			userApi.Detail(row.id).then((rs) => {
				state.detailObj = rs.data;
				state.infoDrawer = true;
			});
		};

		const selectionChange = (selection: any) => {
			console.log('selection:', selection);
			state.tableData.selection = selection;
		};

		const onSortChange = (column: any) => {
			state.tableData.param.order = column.prop;
			state.tableData.param.sort = column.order;

			onInit();
		};

		const onSizechange = (val: number) => {
			state.tableData.param.pageSize = val;
			onInit();
		};

		const onCurrentChange = (val: number) => {
			state.tableData.param.pageIndex = val;
			onInit();
		};

		const onNodeClick = (data: any, node: any) => {
			state.tableData.param.orgType = data.organizationType;
			state.tableData.param.orgId = data.orgId;

			onInit();
		};

		const filterNode = (value: string, data: any) => {
			if (!value) return true;
			return data.name.toLowerCase().includes(value.toLowerCase());
		};

		onMounted(() => {
			onInit();
		});

		const formatNames = (names: any) => {
			return names?.join(', ');
		};

		const tableMaxHeight = computed(() => {
			const wrapperHeight = tableWrapperBody.value?.offsetHeight;
			console.log('wrapperHeight', wrapperHeight);
			return wrapperHeight;
		});

		const selection = ref<[]>([])

		const rowClick = (row: { id: any; }, column: any, event: any) => {
			tableRef.value!.clearSelection()

			tableRef.value!.toggleRowSelection(
				row,
				true
			)
		}

		return {
			tableWrapperBody,
			tableMaxHeight,
			filterTreeVal,
			treeRef,
			printMain,
			tableRef,
			formatStrDate,
			formatDateTime,
			onNodeClick,
			createOrEditRef,
			resetPasswordRef,
			changePwdRef,
			delegateRef,
			selectionChange,
			onSortChange,
			onInit,
			onAdd,
			onEdit,
			onDetail,
			onDelete,
			onDeleteByList,
			onSizechange,
			onCurrentChange,
			onSearch,
			onSearchReSet,
			onSelectMasterUser,
			filterNode,
			collapse,
			collapseUp,
			visible,
			displayDate,
			displayTime,
			...toRefs(state),
			isNotEmptyOrNull,
			tableLayout,
			exportRef,
			Export,
			formatNames,
			rowClick
		};
	},
});
</script>

<style scoped lang="scss"></style>
