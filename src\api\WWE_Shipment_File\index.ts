import BaseApi from '/@/api/BaseApi';
import request from '/@/utils/request';

class wWE_Shipment_FileApi extends BaseApi {
	QueryV2(data: any) {
		return request({
			url: this.baseurl + 'QueryV2',
			method: 'post',
			data,
		});
	}
	ImportInvoice(data: any) {
		return request({
			url: this.baseurl + 'ImportInvoice',
			method: 'post',
			data,
		});
	}
	TabsPageQuery(params: any) {
		return request({
			url: this.baseurl + 'TabsPageQuery',
			method: 'get',
			params,
		});
	}

	UpLoadPDF(invoiceId: string) {
		return request({
			url: this.baseurl + 'UpLoadPDF/' + invoiceId,
			method: 'post',
			data: {},
		});
	}

	DeleteFile(data: any) {
		return request({
			url: this.baseurl + 'DeleteFile',
			method: 'post',
			data,
		});
	}
	
}

export default new wWE_Shipment_FileApi('/api/WWE_Shipment_File/', 'shipmentFileId');
