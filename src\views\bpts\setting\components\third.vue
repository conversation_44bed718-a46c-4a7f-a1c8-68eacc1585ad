<template>
	<div class="top-container">
		<el-tabs tab-position="left" class="demo-tabs">
			<el-tab-pane :label="$t('message.emailFields.toEmail')">
				<Email :emailObj="ruleForm" />
			</el-tab-pane>
			<el-tab-pane :label="$t('message.emailFields.toSftp')">
				<Sftp :sftpObj="ruleForm.sftp" />
			</el-tab-pane>
			<el-tab-pane label="短信" v-if="false"> </el-tab-pane>
		</el-tabs>
	</div>
</template>

<script lang="ts">
import { toRefs, reactive, onMounted, defineComponent, getCurrentInstance, watch } from 'vue';
import { ElMessage } from 'element-plus';
import configApi from '/@/api/config/index';
import Email from './email.vue';
import Sftp from './sftp.vue';

export default defineComponent({
	name: 'thirdCom',
	components: { Email, Sftp },
	props: {
		emailObj: {
			type: Object,
			default: {},
		},
		sftpObj: {
			type: Object,
			default: {},
		},
	},
	setup(props: any) {
		const { proxy } = getCurrentInstance() as any;
		const state = reactive({
			tabPosition: 'Email',
			loading: false,
			ruleForm: {
				email: {},
				sftp: {},
			},
			rules: {
				tokenExp: [{ required: true, message: 'Please input', trigger: 'blur' }],
				tokenMode: [{ required: true, message: 'Please select', trigger: 'change' }],
				tokenValid: [{ required: true, message: 'Please select', trigger: 'change' }],
			},
		});

		const onSave = () => {
			proxy.$refs.ruleFormRef.validate((valid: boolean) => {
				if (!valid) {
					return;
				}
				var obj = { Token: state.ruleForm };
				state.loading = true;
				configApi
					.SaveConfig(obj)
					.then(() => {
						ElMessage.success('Saved Successfully');
					})
					.finally(() => {
						state.loading = false;
					});
			});
		};
		watch(
			() => props.emailObj,
			() => {
				state.ruleForm = props.emailObj || {};
			}
		);
		watch(
			() => props.sftpObj,
			() => {
				state.ruleForm.sftp = props.sftpObj || {};
			}
		);
		// 页面加载时
		onMounted(() => {
			// console.log(state.ruleForm)
		});
		return {
			onSave,
			...toRefs(state),
		};
	},
});
</script>
