<template>
	<div class="system-dic-container">
		<el-card shadow="hover" class="role-list">
			<template #header>
				<div class="card-header">
					<span>Dictionary category</span>
				</div>
			</template>
			<el-row class="mb-4">
				<el-button type="success" round size="small" @click="onCreateType">Create</el-button>
				<el-button type="primary" size="small" round>Refresh</el-button>
				<el-button type="danger" size="small" round @click="onDeleteDictType">Delete</el-button>
			</el-row>

			<el-menu :default-active="-1" class="el-menu" active-text-color="#ffd04b" background-color="#545c64" text-color="#fff" @select="dictTypeSelect">
				<el-menu-item :index="index" v-for="(item, index) in dictData" :key="item.dictId">
					<el-icon class="el-input__icon">
						<ele-Menu />
					</el-icon>
					<span>{{ item.name }}</span>
				</el-menu-item>
			</el-menu>
		</el-card>

		<div class="action-container">
			<el-card shadow="hover">
				<div class="system-user-search mb15">
					<el-input size="small" v-model="tableData.param.searchKey" placeholder="" style="max-width: 180px"> </el-input>
					<el-button size="small" type="primary" class="ml10" @click="onSearch">
						<el-icon>
							<ele-Search />
						</el-icon>
						Search
					</el-button>
					<el-button size="small" type="success" class="ml10" @click="onOpenAddDic">
						<el-icon>
							<ele-FolderAdd />
						</el-icon>
						Create
					</el-button>
				</div>
				<el-table :data="tableData.data" style="width: 100%">
					<el-table-column type="index" label="Serial#" width="100" />
					<el-table-column prop="Name" label="Name" show-overflow-tooltip></el-table-column>
					<el-table-column prop="Value" label="Value" show-overflow-tooltip></el-table-column>
					<el-table-column prop="Enable" label="Enable" show-overflow-tooltip>
						<template #default="scope">
							<el-tag type="success" v-if="scope.row.Enable">Enable</el-tag>
							<el-tag type="info" v-else>禁用</el-tag>
						</template>
					</el-table-column>
					<el-table-column prop="Description" label="Description" show-overflow-tooltip></el-table-column>
					<el-table-column prop="CreatedAt" label="Created At" show-overflow-tooltip>
						<template #default="scope">
							<span>{{ scope.row.CreatedAt }}</span>
						</template>
					</el-table-column>
					<el-table-column label="Actions" width="170">
						<template #default="scope">
							<el-button size="small" type="primary" @click="onOpenEditDic(scope.row)">Edit</el-button>
							<el-button size="small" type="danger" @click="onRowDel(scope.row)">Delete</el-button>
						</template>
					</el-table-column>
				</el-table>
				<el-pagination
					@size-change="onHandleSizeChange"
					@current-change="onHandleCurrentChange"
					class="mt15"
					:pager-count="5"
					:page-sizes="[10, 20, 30]"
					v-model:current-page="tableData.param.pageNum"
					background
					v-model:page-size="tableData.param.pageSize"
					layout="total, sizes, prev, pager, next, jumper"
					:total="tableData.total"
				>
				</el-pagination>
			</el-card>
		</div>
		<CreateOrEdit ref="createOrEditRef" @fetchData="onSearch" />
		<EditDicType ref="editDicTypeRef" @fetchData="getDictList" />
	</div>
</template>

<script lang="ts">
import { toRefs, reactive, onMounted, ref, defineComponent } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import CreateOrEdit from '/@/views/bpts/dict/component/createOrEdit.vue';
import EditDicType from './component/editDicType.vue';

import dictApi from '/@/api/dict/index';
import dictItemApi from '/@/api/dictItem/index';

// 定义接口来定义对象的类型
interface DictItemInfo {
	itemId: 0; //
	dictId: 0; //
	parentId: 0; //
	name: ''; //
	value: ''; //
	sort: 0; //
	enable: false; //
	description: ''; //
}

interface TableDataState {
	tableData: {
		data: Array<DictItemInfo>;
		total: number;
		loading: boolean;
		param: {
			searchType: string;
			searchKey: string;
			pageNum: number;
			pageSize: number;
			dictTypeId: number;
		};
	};
	dictData: object;
	dictIndex: number;
	requestDetailData: [];
}

export default defineComponent({
	name: 'createOrEditDic',
	components: { CreateOrEdit, EditDicType },
	setup() {
		const createOrEditRef = ref();
		const editDicTypeRef = ref();
		const state = reactive<TableDataState>({
			tableData: {
				data: [],
				total: 0,
				loading: false,
				param: {
					searchType: '',
					searchKey: '',
					pageNum: 1,
					pageSize: 10,
					dictTypeId: -1,
				},
			},
			dictData: [],
			dictIndex: -1,
			requestDetailData: [],
		});

		// 初始化表格数据
		const initTableData = (dictTypeId?: any) => {
			dictItemApi.Query(state.tableData.param).then((rs) => {
				state.tableData.data = rs.data;
				state.tableData.total = rs.totalCount;
			});
		};
		const onSearch = () => {
			initTableData();
		};
		// 打开新增字典弹窗
		const onOpenAddDic = () => {
			createOrEditRef.value.openDialog({
				action: 'Create',
				dictTypeId: state.tableData.param.dictTypeId,
			});
		};
		// 打开修改字典弹窗
		const onOpenEditDic = (row: DictItemInfo) => {
			createOrEditRef.value.openDialog({
				action: 'Edit',
				itemId: row.itemId,
			});
		};
		// 删除字典
		const onRowDel = (row: DictItemInfo) => {
			ElMessageBox.confirm(`Are you sure to delete this?`, 'Tips', {
				confirmButtonText: 'OK',
				cancelButtonText: 'No，Thanks',
				type: 'warning',
			}).then(() => {
				dictItemApi
					.DeleteByKey(row.itemId)
					.then(() => {
						ElMessage.success('Succeed');
						initTableData(row.dictId);
					})
					.catch((rs) => {
						ElMessage.error(rs.ResultMsg || rs.toString());
					});
			});
		};
		// 分页改变
		const onHandleSizeChange = (val: number) => {
			state.tableData.param.pageSize = val;
		};
		// 分页改变
		const onHandleCurrentChange = (val: number) => {
			state.tableData.param.pageNum = val;
		};

		const onCreateType = () => {
			editDicTypeRef.value.openDialog();
		};

		const dictTypeSelect = (index: any) => {
			var item = state.dictData[index];
			state.tableData.param.dictTypeId = item.dictId;
			initTableData(state.tableData.param.dictTypeId);
		};

		const onDeleteDictType = () => {
			ElMessageBox.confirm(`Are you sure you want to delete?`, 'Tips', {
				confirmButtonText: 'OK',
				cancelButtonText: 'NO',
				type: 'warning',
			}).then(() => {
				dictApi
					.DeleteByKey(state.tableData.param.dictTypeId)
					.then(() => {
						state.dictIndex = -1;
						getDictList();
						ElMessage.success('Succeeded');
					})
					.catch((rs) => {
						ElMessage.error(rs.resultMsg || rs.toString());
					});
			});
		};

		const getDictList = () => {
			dictApi.GetList({}).then((rs) => {
				state.dictData = rs.data;
			});
		};

		// 页面加载时
		onMounted(() => {
			initTableData();
			getDictList();
		});
		return {
			createOrEditRef,
			editDicTypeRef,
			onSearch,
			onOpenAddDic,
			onOpenEditDic,
			onRowDel,
			onHandleSizeChange,
			onHandleCurrentChange,
			onCreateType,
			getDictList,
			onDeleteDictType,
			dictTypeSelect,
			...toRefs(state),
		};
	},
});
</script>

<style scoped lang="scss">
.system-dic-container {
	position: absolute;
	width: 100%;
	height: 100%;
	display: flex;
	padding-top: 10px;
	margin: 0px;
	background: #eef1f6;

	.role-list {
		width: 260px;
		border: 1px solid #eee;
		border-bottom: 0;
		background: white;
	}

	.action-container {
		border-radius: 4px;
		flex: 1;

		margin-left: 21px;
		background: white;
		border: 1px solid #eaeaea;
		border: 1px solid red;
		display: flex;
		flex-direction: column;
	}

	.el-menu {
		margin-top: 10px;
	}
}
</style>
