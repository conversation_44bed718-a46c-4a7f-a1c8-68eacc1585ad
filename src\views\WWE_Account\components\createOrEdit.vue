<template>
	<div class="system-edit-menu-container">
		<el-drawer v-model="state.showDialog" destroy-on-close :title="title" draggable :close-on-click-modal="false" :close-on-press-escape="false" width="500px" :direction="direction">
			<div class="drawer-content">
				<el-form :model="state.form" ref="formRef" :rules="state.rules" label-width="130px" label-position="top">
					<el-row :gutter="35">
						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.wWE_AccountFields.AccountCode')" prop="accountCode">
								<el-input v-model="state.form.accountCode" clearable size="default" />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.wWE_AccountFields.AccountName')" prop="accountName">
								<el-input v-model="state.form.accountName" clearable size="default" />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.wWE_AccountFields.CustomerEmailAddress')" prop="customerEmailAddress">
								<el-input v-model="state.form.customerEmailAddress" clearable size="default" />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.wWE_AccountFields.FileNamingConvention')" prop="fileNamingConvention">
								<el-input v-model="state.form.fileNamingConvention" clearable size="default" />
								<div class="example-box">
									<small>
										Use placeholders (e.g., <code>{ProNumber}</code>, <code>{DocumentType}</code>) separated by <code>_</code>. <br />
										Full Example: <code>{CompanyAccountName}_{ProNumber}_{CarrierName}_{InvoiceNumber}_{DocumentType}_{DateTime}</code>
									</small>
								</div>
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.wWE_AccountFields.IfCombine')" prop="ifCombine">
								<el-select v-model="state.form.ifCombine" :placeholder="$t('message.page.selectKeyPlaceholder')" clearable class="w100" size="default">
									<el-option label="No" value="No"></el-option>
									<el-option label="Yes" value="Yes"></el-option>
									<el-option label="Yes and Include Invoice" value="Yes and Include Invoice"></el-option>
								</el-select>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>

				<div class="drawer-footer">
					<span class="dialog-footer">
						<el-button @click="onCancel" size="small">{{ $t('message.page.buttonCancel') }}</el-button>
						<el-button :loading="state.loading" type="primary" @click="onSubmit" size="small">{{ $t('message.page.buttonSave') }}</el-button>
					</span>
				</div>
			</div>
		</el-drawer>
	</div>
</template>

<script lang="ts" setup name="wWE_Account/createOrEdit">
import { reactive, toRefs, ref, getCurrentInstance, computed, watch } from 'vue';

import { cloneDeep } from 'lodash-es';

import { useI18n } from 'vue-i18n';

import { ElMessageBox, ElMessage } from 'element-plus';

import type { DrawerProps } from 'element-plus';

import { formatNumberWithDecimal, formatDecimal, validateForbiddenSymbols } from '/@/utils/toolsValidate';
import DateSelector from '/@/components/common/DateSelector.vue';

import wWE_AccountApi from '/@/api/WWE_Account/index';
import dictItem from '/@/api/dictItem';

const validateFilenameTemplate = (rule: any, value: any, callback: any) => {
	console.log('validateFilenameTemplate', rule, value, callback);
	if (!value || typeof value !== 'string') {
		callback(new Error('Input is not a valid string.'));
	}
	const allowedVariables = new Set([
		'{CompanyAccountName}',
		'{ProNumber}',
		'{CarrierName}', // Includes space
		'{InvoiceNumber}',
		'{DateTime}',
		'{DocumentType}',
	]);
	const trimmedTemplate = value.trim();

	if (trimmedTemplate.startsWith('_') || trimmedTemplate.endsWith('_') || trimmedTemplate.includes('__')) {
		callback(new Error('Contains leading/trailing or double underscores.'));
	}

	const parts = trimmedTemplate.split('_');
	const usedPlaceholders = new Set();
	const suffixPattern = /^[a-zA-Z0-9]+$/;

	if (parts.indexOf('{ProNumber}') === -1) {
		callback(new Error('Missing required placeholder: {ProNumber}'));
	}

	// --- Validate Parts ---
	for (let i = 0; i < parts.length; i++) {
		const part = parts[i];
		const isLastPart = i === parts.length - 1;

		// Check if it's an allowed placeholder
		if (allowedVariables.has(part)) {
			if (usedPlaceholders.has(part)) {
				callback(new Error(`Validation Fail: Duplicate placeholder found: "${part}"`));
			}
			usedPlaceholders.add(part);
			// If it's a valid placeholder, continue to the next part
			continue;
		}

		// If it's *not* an allowed placeholder, it *might* be the suffix *if* it's the last part
		if (isLastPart && suffixPattern.test(part)) {
			// It's the last part and matches the suffix pattern (alphanumeric)
			continue; // Valid suffix
		}

		// If it's not an allowed placeholder AND (it's not the last part OR it is the last part but doesn't match the suffix pattern)
		callback(new Error(`Validation Fail: Invalid part found: "${part}". It's not an allowed placeholder, and if it's the last part, it's not a valid alphanumeric suffix.`));
	}
	callback();
};

interface CreateOrEditInput {
	accountId: number;
	accountCode: string;
	accountName: string;
	customerEmailAddress:string;
	fileNamingConvention: string;
	ifCombine: string;
	requiredFileTypes: string | string[];
	createdByName: string;
	modifiedByName: string;
}

defineProps({
	title: {
		type: String,
		default: '',
	},
});

const emit = defineEmits(['refreshData']);

const direction = ref<DrawerProps['direction']>('rtl');

const { t } = useI18n();
const formRef = ref();

const state = reactive({
	showDialog: false,
	loading: false,
	form: {} as CreateOrEditInput,
	rules: {
		accountCode: [
			{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: ['blur', 'change'] },
			{
				validator: (_, value, callback) => {
					const error = validateForbiddenSymbols(value);
					error ? callback(new Error(error)) : callback();
				},
				trigger: ['blur', 'change'],
			},
		],
		accountName: [
			{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: ['blur', 'change'] },
			{
				validator: (_, value, callback) => {
					const error = validateForbiddenSymbols(value);
					error ? callback(new Error(error)) : callback();
				},
				trigger: ['blur', 'change'],
			},
		],
		ifCombine: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: ['blur', 'change'] }],
		fileNamingConvention: [
			{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: ['blur'] },
			{ validator: validateFilenameTemplate, trigger: ['blur'] },
		],
		customerEmailAddress: [
			{
				validator: (rule: any, value: any, callback: any) => {
					if (!value) {
						// 空值直接通过（非必填）
						callback();
						return;
					}

					const regEmail = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
					if (regEmail.test(value)) {
						callback(); // 验证通过
					} else {
						callback(new Error('Please enter a valid email address')); // 验证失败
					}
				},
				trigger: ['blur', 'change'],
			},
		],
	},
});

const documentTypeData = ref([]);

const open = async (row: CreateOrEditInput) => {
	let formData = cloneDeep(row) as CreateOrEditInput;
	// 如果 requiredFileTypes 是一个字符串（逗号分隔），则将其转换为数组
	if (formData.requiredFileTypes && typeof formData.requiredFileTypes === 'string') {
		formData.requiredFileTypes = formData.requiredFileTypes.split(',');
		formData.fileNamingConvention = formData.fileNamingConvention || '{ProNumber}_{DocumentType}';
	}
	state.form = formData;

	state.showDialog = true;

	dictItem.Many(['DocumentType']).then((rs: any) => {
		const result = rs.data.find((item: any) => item.dictValue === 'DocumentType');
		if (result && result.items) {
			documentTypeData.value = result.items;
		}
	});
};

const onCancel = () => {
	closeDialog();
};

const closeDialog = () => {
	onInitForm();
	state.showDialog = false;
};

const onSubmit = async () => {
	formRef.value.validate(async (valid: boolean) => {
		if (!valid) return;

		if (Array.isArray(state.form.requiredFileTypes)) {
			state.form.requiredFileTypes = state.form.requiredFileTypes.join(',');
		}

		state.loading = true;

		let obj = state.form;

		await wWE_AccountApi
			.Save(obj)
			.then(() => {
				ElMessage.success(t('message.page.Success'));
				emit('refreshData');

				closeDialog();
				state.loading = false;
			})
			.catch((error: HandledError) => {
				if (!error.isHandled) {
					const errorCode = error.code;
					const errorMessage = error.message;
					ElMessage.error(errorMessage);
				}
			})
			.finally(() => {
				state.loading = false;
			});
	});
};

const onInitForm = async () => {
	state.form.fileNamingConvention = '{ProNumber}_{DocumentType}';
};

defineExpose({
	open,
});
</script>

<style scoped>
.drawer-content {
	padding: 20px;
}

.drawer-footer {
	text-align: right;
}
small {
	color: #666;
	font-size: 0.85em;
}
code {
	background-color: #f0f0f0;
	padding: 0.1em 0.3em;
	border-radius: 3px;
	font-family: monospace;
}
</style>
