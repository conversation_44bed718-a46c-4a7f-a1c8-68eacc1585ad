import BaseApi from '/@/api/BaseApi';
import request from '/@/utils/request';

class wWE_Shipment_DocumentApi extends BaseApi {
  QueryV2(data: any) {
    return request({
      url: this.baseurl + 'QueryV2',
      method: 'post',
      data,
    });
  }
  Download(id: string ) {
    return request({
      url: this.baseurl + 'Download/' + id,
      method: 'get',
      responseType: 'blob'
    })
  }
}

export default new wWE_Shipment_DocumentApi('/api/WWE_Shipment_Document/', 'id');
