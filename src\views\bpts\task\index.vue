﻿<template>
	<div class="yl-page schedule-container layout-pd">
		<el-row :gutter="15">
			<el-col :xl="6" :lg="6" :md="8" :sm="12" :xs="24" v-for="item in tableData.data" :key="item.id">
				<el-card class="task task-item mb10" shadow="hover">
					<h2>{{ item.name }}</h2>
					<ul>
						<li>
							<h4>{{ $t('message.taskCommon.executeClass') }}</h4>
							<p>{{ item.className }}</p>
						</li>
						<li>
							<h4>{{ $t('message.taskCommon.TimingRules') }}</h4>
							<p>{{ item.cron }}</p>
						</li>
					</ul>
					<div class="bottom">
						<div class="state">
							<el-tag v-if="item.isStart == '1'" size="small">{{ $t('message.taskCommon.Running') }}</el-tag>
							<el-tag v-if="item.isStart == '0'" size="small" type="info">{{ $t('message.taskCommon.deactivated') }}</el-tag>
						</div>
						<div class="handler">
							<el-popconfirm :title="getDlgExecuteText(item.isStart)" @confirm="run(item)">
								<template #reference>
									<el-button type="primary" icon="ele-CaretRight" circle />
								</template>
							</el-popconfirm>

							<el-dropdown v-auths="['systemTask.Edit', 'systemTask.Delete']" trigger="click" style="margin-left: 10px">
								<el-button type="primary" icon="ele-More" circle plain></el-button>
								<template #dropdown>
									<el-dropdown-menu>
										<Auth :value="'systemTask.Edit'">
											<el-dropdown-item @click="onEdit(item)">{{ $t('message.page.buttonEdit') }}</el-dropdown-item>
										</Auth>
										<Auth :value="'systemTask.Edit'">
											<el-dropdown-item @click="onExecute(item)">{{ $t('message.page.buttonExecute') }}</el-dropdown-item>
										</Auth>
										<!-- <el-dropdown-item @click="onLogs(item)">{{ $t('message.page.buttonLog') }}</el-dropdown-item> -->
										<Auth :value="'systemTask.Delete'">
											<el-dropdown-item @click="onDelete(item)" divided>{{ $t('message.page.buttonDelete') }}</el-dropdown-item>
										</Auth>
									</el-dropdown-menu>
								</template>
							</el-dropdown>
						</div>
					</div>
				</el-card>
			</el-col>
			<el-col :xl="6" :lg="6" :md="8" :sm="12" :xs="24">
				<el-card v-auth="'systemTask.Create'" class="task task-add" shadow="none" @click="add">
					<el-icon>
						<ele-Plus />
					</el-icon>
					<p>{{ $t('message.taskCommon.AddingScheduledTasks') }}</p>
				</el-card>
			</el-col>
		</el-row>
		<CreateOrEdit ref="createOrEditRef" @fetchData="onInit"></CreateOrEdit>
		<el-drawer :title="$t('message.taskCommon.Taskschedulinglog')" v-model="dialog.logsVisible" :size="600" direction="rtl" destroy-on-close>
			<LogList ref="logListRef"></LogList>
		</el-drawer>
	</div>
</template>

<script lang="ts">
//import { useRouter } from 'vue-router';
import { toRefs, reactive, onMounted, ref, defineComponent, nextTick } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { formatStrDate } from '/@/utils/formatTime';
import print from '/@/utils/print.js';

import taskQueueApi from '/@/api/taskQueue/index';
import Detail from './components/detail.vue';
import CreateOrEdit from './components/createOrEdit.vue';
import LogList from './components/logList.vue';
import { useI18n } from 'vue-i18n';
import Auth from '/@/components/auth/auth.vue';

export default defineComponent({
	name: 'systemTask',
	components: { CreateOrEdit, LogList, Auth },
	setup() {
		const { t } = useI18n();
		//const router = useRouter();
		const createOrEditRef = ref();
		const logListRef = ref();
		const state = reactive({
			dialog: {
				save: false,
				logsVisible: false,
			},
			tableData: {
				data: [],
				total: 0,
				loading: false,
				selection: [],
				param: {
					pageIndex: 1,
					pageSize: 50,
					searchKey: '',
					order: 'id',
					sort: 'desc', // asc or desc
				},
			},
		});

		//初始化
		const onInit = () => {
			state.tableData.loading = true;
			taskQueueApi
				.GetList(state.tableData.param)
				.then((rs) => {
					state.tableData.data = rs.data;
				})
				.catch((rs) => {})
				.finally(() => (state.tableData.loading = false));
		};

		const add = () => {
			createOrEditRef.value.openDialog({ action: 'Create' });
		};
		// 修改
		const onEdit = (row: Object) => {
			createOrEditRef.value.openDialog({ action: 'Edit', id: row.id });
			//router.push('/taskQueue/edit/' + row.id);
		};
		// 删除
		const onDelete = (row: Object) => {
			ElMessageBox.confirm(t('message.page.dlgDeleteText'), t('message.page.dlgTip'), {
				confirmButtonText: t('message.page.confirm'),
				cancelButtonText: t('message.page.cancel'),
				type: 'warning',
			}).then(() => {
				taskQueueApi
					.DeleteByKey(row.id)
					.then((rs) => {
						if (!rs.data) {
							ElMessage.error(rs.resultMsg);
							return;
						}
						onInit();
					})
					.catch((rs) => {});
			});
		};

		const onLogs = async (row: Object) => {
			state.dialog.logsVisible = true;
			await nextTick();
			logListRef.value.onShow(row.id);
		};

		const getDlgExecuteText = (isStart: boolean) => {
			const action = isStart ? t('message.page.stopJob') : t('message.page.startJob');
			return t('message.page.dlgExecuteText').replace('$[action]', action);
		};

		const run = (obj: any) => {
			const { isStart } = obj;
			const type = isStart ? 'StopJob' : 'StartJob';
			taskQueueApi.OperationTask(obj.id, type).then((rs) => {
				if (rs.data) {
					ElMessage.success(t('message.page.executeSuccess'));
					onInit();
				}
			});
		};

		const onExecute = (obj: any) => {
			ElMessageBox.confirm(t('message.page.dlgImmediatelyText'), t('message.page.dlgTip'), {
				confirmButtonText: t('message.page.confirm'),
				cancelButtonText: t('message.page.cancel'),
				type: 'warning',
			}).then(() => {
				const type = 'ExecuteJob';
				taskQueueApi.OperationTask(obj.id, type).then((rs) => {
					if (rs.data) {
						ElMessage.success(t('message.page.executeSuccess'));
						onInit();
					}
				});
			});
		};

		//本地更新数据
		const handleSuccess = (data, mode) => {};
		// 页面加载时
		onMounted(() => {
			onInit();
		});
		return {
			logListRef,
			add,
			formatStrDate,
			onDelete,
			onEdit,
			run,
			onInit,
			onLogs,
			handleSuccess,
			createOrEditRef,
			onExecute,
			getDlgExecuteText,
			...toRefs(state),
		};
	},
});
</script>

<style scoped lang="scss">
.schedule-container {
	.el-card {
		border: 1px solid var(--el-card-border-color);
	}
	.task {
		height: 210px;
	}

	.task-item h2 {
		font-size: 15px;
		color: #3c4a54;
		padding-bottom: 15px;
	}

	.task-item li {
		list-style-type: none;
		margin-bottom: 10px;
	}

	.task-item li h4 {
		font-size: 12px;
		font-weight: normal;
		color: #999;
	}

	.task-item li p {
		margin-top: 5px;
	}

	.task-item .bottom {
		border-top: 1px solid #ebeef5;
		text-align: right;
		padding-top: 10px;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.task-add {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		text-align: center;
		cursor: pointer;
		color: #999;
	}

	.task-add:hover {
		color: #409eff;
	}

	.task-add i {
		font-size: 30px;
	}

	.task-add p {
		font-size: 12px;
		margin-top: 20px;
	}

	.el-button i.el-icon {
		margin-right: 0px;
	}
}
</style>
