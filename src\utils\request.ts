import axios from 'axios';
import { useUserInfo } from '/@/stores/userInfo';
import { Local } from '/@/utils/storage';
import loginApi from '/@/api/login/index';
import { debugLog } from '/@/utils';
import { ElMessage, ElMessageBox } from 'element-plus';
import appSettings from '/@/config/index.js';
import { API_ERROR_CODES, HTTP_ERROR_CODES, LocalStorageKeys, URLs } from '/@/constants';
import { isApiResult } from '/@/utils/toolsValidate';
import forge from 'node-forge';

const service = axios.create({
	baseURL: appSettings.BASE_URL,
	timeout: appSettings.API_TIMEOUT,
	headers: { 'Content-Type': 'application/json' },
});

const errorMessages: Record<number | string, string> = {
	[API_ERROR_CODES.INVALID_PARAMS]: 'Invalid request parameters',
	[API_ERROR_CODES.UNAUTHORIZED_401]: 'Unauthorized, please log in',
	[API_ERROR_CODES.FORBIDDEN_403]: 'You do not have permission to access this resource',
	[API_ERROR_CODES.NOT_FOUND]: 'Requested resource not found',
	[API_ERROR_CODES.INVALID_SESSION_4005]: 'Session has expired, please log in again',
	[API_ERROR_CODES.ACCESS_DENIED]: 'Access denied, operation refused',
	[API_ERROR_CODES.DATA_CONFLICT]: 'Data conflict',
	[API_ERROR_CODES.CUSTOM_SERVER_ERROR]: 'Internal server custom error',
	[HTTP_ERROR_CODES.BAD_REQUEST]: 'Invalid request',
	[HTTP_ERROR_CODES.UNAUTHORIZED]: 'Unauthorized, please log in',
	[HTTP_ERROR_CODES.FORBIDDEN]: 'You do not have permission to access this resource',
	[HTTP_ERROR_CODES.NOT_FOUND]: 'Resource not found',
	[HTTP_ERROR_CODES.INTERNAL_SERVER_ERROR]: 'Internal server error',
	[HTTP_ERROR_CODES.BAD_GATEWAY]: 'Bad gateway',
	[HTTP_ERROR_CODES.SERVICE_UNAVAILABLE]: 'Service unavailable',
	[HTTP_ERROR_CODES.GATEWAY_TIMEOUT]: 'Gateway timeout',
};

function encryptData(publicKeyPem: string, data: string): string {
	const publicKey = forge.pki.publicKeyFromPem(publicKeyPem);
	const encrypted = publicKey.encrypt(data, 'RSA-OAEP', {
		md: forge.md.sha256.create(),
	});
	return forge.util.encode64(encrypted);
}

// 生成唯一的 nonce
const generateNonce = (): string => {
	const array = new Uint8Array(16);
	window.crypto.getRandomValues(array);
	return Array.from(array, (byte) => byte.toString(16).padStart(2, '0')).join('');
};

service.interceptors.request.use(
	async (config) => {
		// 如果是刷新 Token 的请求，使用 refreshToken
		if (config.url && config.url.toLowerCase().endsWith(URLs.Api_RefreshToken.toLowerCase())) {
			const refreshToken = Local.get(LocalStorageKeys.RefreshToken);
			if (refreshToken) {
				config.headers['Authorization'] = `Bearer ${refreshToken}`;
			}
		} else if (Local.get(LocalStorageKeys.Token)) {
			// 普通请求使用 accessToken
			config.headers['Authorization'] = `Bearer ${Local.get(LocalStorageKeys.Token)}`;
		}

		// 添加当前访问 URL 到请求头
		if (window.location.href) {
			config.headers['AccessUrl'] = window.location.href;
		}

		// 生成nonce和时间戳
		const nonce = generateNonce();
		const timestamp = Math.floor(new Date().getTime() / 1000); // 使用 UTC 时间
		const dataToEncrypt = `${nonce}:${timestamp}`;
		const publicKey = import.meta.env.VITE_PUBLIC_KEY;
		const encryptedData = encryptData(publicKey, dataToEncrypt);

		// 添加加密后的数据到请求头
		config.headers['Auth-Token'] = encryptedData;

		return config;
	},
	(error) => {
		return Promise.reject(error);
	}
);

service.interceptors.response.use(
	async (response) => {
		debugLog(`response ${response.config.url}`, response);
		const { config } = response;
		if (config.responseType === 'blob') {
			return response;
		}
		const rs = response.data;
		// 检查是否为标准的 API 响应结构
		if (isApiResult(rs)) {
			if (rs.resultCode === API_ERROR_CODES.SUCCESS) {
				return rs;
			} else {
				const handledResult: any = await handleError({ response: { data: rs } });
				// 处理错误,如果处理后的结果是成功的 API 响应 (401重试后可能会返回成功的结果)
				if (isApiSuccess(handledResult)) {
					return handledResult;
				}
				return Promise.reject(handledResult);
			}
		}

		return rs;
	},
	async (error) => {
		if (!error.isHandled) {
			const handledResult: any = await handleError(error);
			// 处理错误,如果处理后的结果是成功的 API 响应 (401重试后可能会返回成功的结果)
			if (isApiSuccess(handledResult)) {
				return handledResult;
			}

			if (handledResult.isHandled) {
				error.isHandled = true; // 标记为已处理
			}
			return Promise.reject(handledResult);
		}
		return Promise.reject(error);
	}
);

// 通用错误处理函数
export const handleError = async (error: any): Promise<HandledError> => {
	let message = '';
	let isHandled = true;
	let code = null;

	if (error.response) {
		code = error.response.data?.resultCode || error.response.status;
		message = errorMessages[code] || error.response.data?.resultMsg || error.response.statusText || 'Unknown error';

		// 特殊处理 401
		if (code === HTTP_ERROR_CODES.UNAUTHORIZED || code === API_ERROR_CODES.UNAUTHORIZED_401) {
			return await handleUnauthorizedError(error);
		}

		// 特殊处理 403
		if (code === HTTP_ERROR_CODES.FORBIDDEN || code === API_ERROR_CODES.FORBIDDEN_403) {
			ElMessageBox.alert(message, 'Tip', {})
				.then(() => {
					//clearAuthDataAndRedirect();
				})
				.catch(() => {});
			return new Error(message) as HandledError;
		}

		// 特殊处理 4005 (Invalid Session)
		if (code === API_ERROR_CODES.INVALID_SESSION_4005) {
			// ElMessageBox.alert(message, 'Tip');
			isHandled = false;
			clearAuthDataAndRedirect();
		}

		if (code === API_ERROR_CODES.SERVER_ERROR) {
			message = error.response.data?.resultMsg || error.response.statusText || 'Unknown error';
		}

		if(code === API_ERROR_CODES.LOGIN_ERROR) {
			message = error.response.data?.resultMsg;
			isHandled = false;
		}


	} else {
		message = error.message || 'Network error';
		//isHandled = false;
	}

	// 如果错误已处理，显示提示信息
	if (isHandled && message) {
		ElMessage.error(message);
	}

	// 构造返回的错误对象
	const handledError: HandledError = new Error(message) as HandledError;
	handledError.isHandled = isHandled;
	handledError.code = code;
	handledError.message = message;
	handledError.response = error.response;
	return handledError;
};

// 处理 401 错误，尝试刷新 Token 并重新发起请求
const handleUnauthorizedError = async (error: any): Promise<HandledError> => {
	if (error.config && !error.config._retry) {
		error.config._retry = true; // 标记为已重试
		try {
			await getNewToken();
			return service.request(error.config);
		} catch (refreshError) {
			// ElMessage.error('Session has expired. Please log in again.');
			// clearAuthDataAndRedirect();

			clearAuthDataAndRedirect();

			return Promise.reject(refreshError);
		}
	} else {
		// 如果已经重试过，清除会话并跳转到登录页
		// ElMessage.error('Unauthorized access. Please log in again.');
		// clearAuthDataAndRedirect();

		clearAuthDataAndRedirect();

		return Promise.reject(error);
	}
};

// 刷新 Token 的逻辑
let refreshTokenPromise: Promise<any> | null = null;
const getNewToken = async () => {
	if (refreshTokenPromise) {
		return refreshTokenPromise;
	}

	const refreshToken = Local.get(LocalStorageKeys.RefreshToken);
	const stores = useUserInfo();
	const userName = stores.userInfos.userName;

	if (!refreshToken || !userName) {
		clearAuthDataAndRedirect();
		return;
	}

	refreshTokenPromise = loginApi
		.RefreshToken({ token: refreshToken, userName })
		.then((rs) => {
			if (!rs.data) {
				throw new Error('Refresh token failed');
			}
			Local.set(LocalStorageKeys.Token, rs.data.token);
			Local.set(LocalStorageKeys.TokenExp, rs.data.exp);
			Local.set(LocalStorageKeys.RefreshToken, rs.data.refreshToken);
		})
		.catch((error) => {
			clearAuthDataAndRedirect();
			throw error;
		})
		.finally(() => {
			refreshTokenPromise = null;
		});

	return refreshTokenPromise;
};

const clearAuthDataAndRedirect = () => {
	const currentPath = window.location.pathname.toLowerCase();
	const loginPath = URLs.LOGIN.toLowerCase();
	if (currentPath.endsWith(loginPath)) {
		Local.clearSessionAndTokens();
		window.location.reload();
	} else {
		Local.clearAndLogin();
		window.location.href = URLs.LOGIN;
	}
};

const isApiSuccess = (data: any) => {
	return data && data.resultCode && data.resultCode === 200 && data.succeeded;
};

export default service;
