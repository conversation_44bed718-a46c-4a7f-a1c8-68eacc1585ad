<template>
	<div class="send-email-container">
		<el-card shadow="never" class="mb10">
			<template #header>
				<div class="card-header">
					<span>{{ $t('message.emailCommon.emailInformation') }}</span>
					<div>
						<el-button v-if="isDraftOrNew" :loading="saveDraftloading" type="primary" @click="saveForm(0)" size="small">{{ $t('message.page.buttonSave') }}</el-button>
						<el-button v-if="isDraftOrNew" :loading="sendloading" type="primary" @click="saveForm(1)" size="small">{{ $t('message.page.buttonSend') }}</el-button>
						<el-button v-if="ruleForm.status > 1" @click="onReSend" type="danger" size="small">{{ $t('message.page.reSend') }}</el-button>
						<el-button @click="onBack" size="small">
							{{ $t('message.page.cancel') }}
						</el-button>
					</div>
				</div>
			</template>

			<el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="150px" class="mt10">
				<el-row :gutter="35">
					<template v-if="emailId > 0">
						<el-col :span="24" class="mb6">
							<el-form-item :label="$t('message.page.createAt')">
								<span>{{ ruleForm.createdAt }}</span>
							</el-form-item>
						</el-col>
						<el-col :span="24" class="mb6">
							<el-form-item :label="$t('message.emailFields.sendDate')">
								<span>{{ ruleForm.sendData }}</span>
							</el-form-item>
						</el-col>
						<el-col :span="24" class="mb6">
							<el-form-item :label="$t('message.emailFields.emailStatus')">
								<el-select v-model="ruleForm.status" clearable class="w100" size="middle">
									<el-option label="Draft" :value="0" />
									<el-option label="Sending" :value="1" />
									<el-option label="Sent" :value="2" />
									<el-option label="Failed" :value="3" />
								</el-select>
							</el-form-item>
						</el-col>
					</template>

					<el-col :span="24" class="mb6">
						<el-form-item :label="$t('message.emailFields.emailSubject')" prop="subject">
							<el-input v-model="ruleForm.subject" size="middle" />
						</el-form-item>
					</el-col>

					<el-col v-if="emailId > 0" :span="24" class="mb6">
						<el-form-item :label="$t('message.emailFields.emailFrom')" prop="fromAddr">
							<el-input v-model="ruleForm.fromAddr" disabled size="middle" />
						</el-form-item>
					</el-col>

					<el-col :span="24" class="mb6">
						<el-form-item :label="$t('message.emailFields.emailTo')" prop="addrObjList">
							<mail-input v-model:list="ruleForm.addrObjList" />
						</el-form-item>
					</el-col>

					<el-col :span="24" class="mb6">
						<el-form-item :label="$t('message.emailFields.emailCc')">
							<mail-input v-model:list="ruleForm.ccAddrObjList" />
						</el-form-item>
					</el-col>

					<el-col :span="24" class="mb6">
						<el-form-item :label="$t('message.emailFields.attachment')">
							<div class="upload-div">
								<sc-upload-file v-model="fileurlArr" :action="actionStr" :limit="100" name="files">
									<el-button type="primary" style="width: 150px" icon="ele-Upload" size="small">
										{{ $t('message.page.uploadFile') }}
									</el-button>
								</sc-upload-file>
							</div>
						</el-form-item>
					</el-col>

					<el-col :span="24" class="mb6">
						<el-form-item :label="$t('message.emailFields.emailBody')" prop="bodyHtml">
							<Editor v-model="ruleForm.bodyHtml" class="ed" />
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
		</el-card>
	</div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, getCurrentInstance, onUnmounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { ElMessage, ElMessageBox } from 'element-plus';
import emailTemplateApi from '/@/api/emailTemplate';
import emailsApi from '/@/api/emails';
import mittBus from '/@/utils/mitt';
import { isNotEmptyOrNull, parseThanZero } from '/@/utils';
import Editor from '/@/components/editor/indexV5.vue';
import MailInput from './mailInput.vue';
import { Session } from '/@/utils/storage';
import { SessionStorageKeys, URLs } from '/@/constants';

import invoiceEmailApi from '/@/api/WWE_Invoice_Email';

const { t } = useI18n();
const router = useRouter();
const route = useRoute();
const { proxy } = getCurrentInstance() as any;
const ruleFormRef = ref();

const saveDraftloading = ref(false);
saveDraftloading.value = false;
const sendloading = ref(false);

const ruleForm = reactive({
	status: 1,
	fromAddr: '<EMAIL>',
	dataType: 'SendEmail',
	addrObjList: [] as any[],
	ccAddrObjList: [] as any[],
	subject: '',
	bodyHtml: '',
	from: '',
});

const rules = reactive({
	subject: [{ required: true, message: 'Please input', trigger: 'blur' }],
	bodyHtml: [{ required: true, message: 'Please input', trigger: 'blur' }],
	addrObjList: [{ required: true, message: 'Please input', trigger: 'change' }],
});

const emailId = ref('');
const tempData = ref([]);
const allAddrData = ref([]);
const fileurlArr = ref([]);
const BASE_URL = proxy?.$appSettings?.BASE_URL || import.meta.env.VITE_API_URL;
const actionStr = `${BASE_URL}Api/Files/UpLoad`;

const isDraftOrNew = computed(() => Number(emailId.value) === 0 || ruleForm.status === 0);

// 组件卸载时清理临时数据
onUnmounted(() => {
	clearTempData();
});

const clearTempData = () => {
	if (!emailId.value || emailId.value === '0') {
		Session.remove(SessionStorageKeys.sendEmailData);
	}
};

const convertToEmailAttachments = (list: any[]) =>
	list?.map((item) => ({
		FileId: item.fileId,
		FileName: item.name,
		OriginalName: item.originalName,
		FilePath: item.path,
		Size: item.size,
		FileType: item.fileType,
		Ext: item.ext,
	})) ?? [];

const saveForm = (status: number) => {
	const toAddrList = [] as any[];
	const ccAddrList = [] as any[];
	let hasFaild = false;

	ruleForm.addrObjList.forEach((item: any) => {
		if (item.status === 'err') hasFaild = true;
		else toAddrList.push(item.email);
	});
	ruleForm.ccAddrObjList.forEach((item: any) => {
		if (item.status === 'err') hasFaild = true;
		else ccAddrList.push(item.email);
	});

	if (hasFaild) return proxy.$message('Incorrect email format', 'error');

	ruleFormRef.value.validate((valid: boolean) => {
		if (!valid) return;

		const payload = {
			...ruleForm,
			status,
			toAddrList,
			ccAddrList,
			attachmentList: convertToEmailAttachments(fileurlArr.value),
			sendMode: 1,
		};

		if (status === 0) saveDraftloading.value = true;
		if (status === 1) sendloading.value = true;
		console.log('payload', payload);

		if (payload.dataType === 'SendMissingDocsEmail') {
			const sendEmailData = Session.get(SessionStorageKeys.sendEmailData);
			const request = {
				...payload,
				...sendEmailData,
			};
			invoiceEmailApi
				.SendMissingDocsEmail(request)
				.then((rs) => {
					if (!rs.data) {
						ElMessage.error(t('message.emailStatus.failed'));
						return;
					}
					clearTempData();
					ElMessage.success(t('message.emailStatus.sended'));
					//mittBus.emit('onCurrentContextmenuClick', Object.assign({}, { contextMenuClickId: 1, ...route }));
					//router.push(URLs.EmailList);
					mittBus.emit('onCurrentContextmenuClick', Object.assign({}, { contextMenuClickId: 1, ...route }));
					router.go(-1); // 返回上一页
				})
				.finally(() => {
					saveDraftloading.value = false;
					sendloading.value = false;
				});
		} else {
			emailsApi
				.SendEmail(payload)
				.then((rs) => {
					if (!rs.data || rs.data <= 0) {
						ElMessage.error(t('message.emailStatus.failed'));
						return;
					}
					clearTempData();
					ElMessage.success(t('message.emailStatus.sended'));
					mittBus.emit('onCurrentContextmenuClick', Object.assign({}, { contextMenuClickId: 1, ...route }));
					router.go(-1); // 返回上一页
					//router.push(URLs.EmailList);
				})
				.finally(() => {
					saveDraftloading.value = false;
					sendloading.value = false;
				});
		}
	});
};

const onReSend = () => {
	saveForm(1);
};

const onBack = () => {
	mittBus.emit('onCurrentContextmenuClick', { contextMenuClickId: 1, ...route });
};

const convertToRecipientList = (emailString: string) => {
	if (!emailString) return [];

	return emailString
		.split(';')
		.map((item) => item.trim())
		.filter((item) => item) // 过滤空值
		.map((item) => {
			// 尝试匹配 "名字 <邮箱>" 格式
			const match = item.match(/^(.*?)\s*<(.*?)>$/);
			if (match) {
				// 如果匹配成功，提取名字和邮箱
				return {
					content: item, // 保持原格式
					name: match[1].trim(), // 提取的名字
					email: match[2].trim(), // 提取的邮箱
					status: 'normal',
				};
			} else {
				// 如果没有匹配到，可能是纯邮箱格式
				return {
					content: `${item}`, // 格式化为标准格式
					name: item.split('@')[0], // @前面的部分作为名字
					email: item, // 完整邮箱地址
					status: 'normal',
				};
			}
		});
};

onMounted(async () => {
	if (parseThanZero(route.params.id)) {
		clearTempData();
		emailId.value = Array.isArray(route.params.id) ? route.params.id[0] : route.params.id;
		const { data: item } = await emailsApi.Detail(route.params.id);
		ruleForm.addrObjList = convertToRecipientList(item.toAddrs);
		console.log('item.addrObjList', ruleForm.addrObjList);
		ruleForm.ccAddrObjList = convertToRecipientList(item.ccAddrs);
		fileurlArr.value = item.attachmentList ?? [];
		Object.assign(ruleForm, item);
	} else {
		const sendEmailData = Session.get(SessionStorageKeys.sendEmailData);
		if (sendEmailData) {
			ruleForm.dataType = 'SendMissingDocsEmail';
			invoiceEmailApi.BuildMissingDocsEmail(sendEmailData).then((res) => {
				if (res.data) {
					Object.assign(ruleForm, res.data);
					if (res.data.toAddrs) {
						ruleForm.addrObjList = convertToRecipientList(res.data.toAddrs);
						console.log('ruleForm.addrObjList', ruleForm.addrObjList);
					}
					if (res.data.ccAddrs) {
						ruleForm.ccAddrObjList = convertToRecipientList(res.data.ccAddrs);
					}
				}
			});
		}
	}
	const [emailListRes, templateListRes] = await Promise.all([emailsApi.GetEmails(), emailTemplateApi.GetList({})]);

	allAddrData.value = emailListRes.data;
	tempData.value = templateListRes.data;
	console.log('ruleForm.dataType', ruleForm.dataType);
});
</script>

<style scoped lang="scss">
.send-email-container {
	padding: 10px;
	background-color: #ffffff;

	.card-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.upload-div {
		width: 300px;
	}

	.ed {
		width: 100%;
	}
}

.el-card ::v-deep .el-card__header {
	padding: 5px 10px;
}
</style>
