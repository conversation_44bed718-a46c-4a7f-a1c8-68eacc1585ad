<template>
	<div class="top-container">
		<div class="list-search">
			<el-card class="list-search-card" shadow="never">
				<el-form label-width="100px" @keyup.enter="onSearch">
					<el-row :gutter="24">
						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="5" class="mb1 mt1">
							<el-form-item :label="$t('message.wWE_ImagesLabels.FileName')" prop="originalFileName">
								<el-input v-model="state.formTable.params.originalFileName" clearable
									:placeholder="$t('message.page.searchKeyPlaceholder')" class="w-20"
									size="default"></el-input>
							</el-form-item>
						</el-col>

						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="5" class="mb1 mt1">
							<el-form-item :label="$t('message.wWE_ImagesLabels.Priority')" prop="priority">
								<el-input v-model="state.formTable.params.priority" clearable
									:placeholder="$t('message.page.searchKeyPlaceholder')" class="w-20"
									size="default"></el-input>
							</el-form-item>
						</el-col>

						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="5" class="mb1 mt1">
							<el-form-item :label="$t('message.wWE_ImagesLabels.Queue')" prop="queue">
								<el-input v-model="state.formTable.params.queue" clearable
									:placeholder="$t('message.page.searchKeyPlaceholder')" class="w-20"
									size="default"></el-input>
							</el-form-item>
						</el-col>

						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="5" class="mb1 mt1">
							<el-form-item :label="$t('message.wWE_ImagesLabels.BatchNum')" prop="batchNum">
								<el-input v-model="state.formTable.params.batchNum" clearable
									:placeholder="$t('message.page.searchKeyPlaceholder')" class="w-20"
									size="default"></el-input>
							</el-form-item>
						</el-col>

						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="5" class="mb1 mt1">
							<el-form-item :label="$t('message.wWE_ImagesLabels.CreatedAt')" prop="createdAt">
								<DateRangeSelector v-model:startDate="state.formTable.params.createdAtStart"
									v-model:endDate="state.formTable.params.createdAtEnd" dateType="day"
									:editable="true" :clearable="false" />
							</el-form-item>
						</el-col>

						<el-col :xs="12" :sm="8" :md="7" :lg="6" :xl="5" class="mb1 mt1">
							<el-form-item class="nowidth" label=" ">
								<div class="searchBtn">
									<el-button type="primary" icon="search" @click="onSearch">
										{{ $t('message.page.buttonSearch') }}
									</el-button>
									<el-button type="danger" icon="refresh-left" @click="onClear">
										{{ $t('message.page.buttonReset') }}
									</el-button>
								</div>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>
			</el-card>
		</div>

		<div class="list-table">
			<el-card shadow="never">
				<el-header class="table_header">
					<div class="left-panel">
						<el-button type="primary" icon="plus" @click="onAdd" v-auth="'WWE_Images.Create'">
							{{ $t('message.wWE_ImagesButtons.createWWE_Images') }}
						</el-button>
					</div>

					<div class="right-panel" v-auth="'pending_images.Export'">
						<Export ref="exportRef" pageName="WWE_Images" :pageParams="state.formTable.params" />
					</div>
				</el-header>

				<div class="scTable">
					<div class="scTable-table">
						<vxe-table show-overflow border="full" :min-height="25" ref="tableRef"
							:header-cell-config="{ height: 23, padding: false }"
							:row-config="{ isHover: true, height: 33 }" :column-config="{ resizable: true }"
							:checkbox-config="{ highlight: true }" :loading="state.formTable.loading"
							:loading-config="{ text: $t('message.page.loading') }" :tree-config="{ transform: true }"
							:scroll-y="{ enabled: false, gt: 0, mode: 'wheel' }" :data="state.formTable.data"
							:header-cell-style="headerCellStyle" :sortConfig="sortConfig" @sort-change="onSortChange"
							@cell-click="cellClick" stripe>
							<template #empty>
								<el-empty :description="$t('message.page.emptyDescription')"
									:image-size="100"></el-empty>
							</template>

							<vxe-column type="checkbox" width="50"></vxe-column>

							<vxe-column :title="$t('message.wWE_ImagesFields.OriginalFileName')"
								field="originalFileName" min-width="150"></vxe-column>

							<vxe-column title="Import Time" field="createdAt" min-width="180" sortable></vxe-column>

							<vxe-column :title="$t('message.wWE_ImagesFields.Priority')" field="priority"
								min-width="120"></vxe-column>

							<vxe-column :title="$t('message.wWE_ImagesFields.Queue')" field="queue"
								min-width="120"></vxe-column>

							<vxe-column :title="$t('message.wWE_ImagesFields.BatchNum')" field="batchNum"
								min-width="120"></vxe-column>

							<vxe-column fixed="right" :title="$t('message.page.actions')" width="90">
								<template #default="scope">
									<el-tooltip class="box-item" effect="dark" :content="$t('message.limits.Restore')"
										placement="bottom">
										<vxe-button mode="text" status="primary" icon="vxe-icon-repeat"
											@click="onRestore(scope.row)" v-auth="'WWE_Images.Restore'"></vxe-button>
									</el-tooltip>
								</template>
							</vxe-column>
						</vxe-table>
					</div>
					<div class="scTable-page">
						<el-pagination @size-change="onSizeChange" @current-change="onCurrentChange" :pager-count="5"
							:page-sizes="[20, 30, 50, 100]" v-model:current-page="state.formTable.params.pageIndex"
							background v-model:page-size="state.formTable.params.pageSize"
							layout="total, sizes, prev, pager, next, jumper" :total="state.formTable.total" small>
						</el-pagination>
					</div>
				</div>
			</el-card>
		</div>
	</div>
</template>

<script lang="ts" setup name="wWE_Images/DeletedList">
import { ref, computed, reactive, onMounted, nextTick, onActivated, onDeactivated, getCurrentInstance } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { useI18n } from 'vue-i18n';

import DateRangeSelector from '/@/components/common/DateRangeSelector.vue';

import wWE_ImagesApi from '/@/api/WWE_Images/index';

import Export from '/@/components/export/index.vue';
import { VxeTablePropTypes, VxeGridInstance } from 'vxe-table';

const { proxy } = getCurrentInstance() as any;
const { t } = useI18n();
const createOrEditRef = ref();
const exportRef = ref();

interface RowVO {
	filePath: string;
	originalFileName: string;
	fileName: string;
	fileExt: string;
	fileMimeType: string;
	fileSize: number;
	priority: string;
	queue: number;
	indexed: string;
	comment: string;
	batchNum: string;
	receivingType: string;
	deletedAt: string;
	accountId: number;
	createdByName: string;
	modifiedByName: string;
	id: number;
	createdById: number;
	createdAt: string;
	modifiedById: number;
	modifiedAt: string;
}

const tableRef = ref<VxeGridInstance<RowVO>>();

const sortConfig = ref<VxeTablePropTypes.SortConfig<RowVO>>({
	defaultSort: {
		field: 'createdAt',
		order: 'desc',
	},
	trigger: "cell"
});

const headerCellStyle = ({ column }: any) => {
	if (column.sortable) {
		return {
			cursor: 'pointer',
		};
	}
};

function getDefaultQueryParams() {
	return {
		pageIndex: 1,
		pageSize: 20,
		order: 'createdAt',
		sort: 'desc',
		filePath: '',
		originalFileName: '',
		fileName: '',
		fileExt: '',
		fileMimeType: '',
		fileSize: '',
		priority: '',
		queue: '',
		indexed: null,
		comment: '',
		batchNum: '',
		receivingType: '',
		isDeleted: true,
		deletedAt: null,
		deletedAtStart: null,
		deletedAtEnd: null,
		accountId: 0,
		createdByName: '',
		modifiedByName: '',
		id: 0,
		createdById: '',
		createdAt: null,
		createdAtStart: null,
		createdAtEnd: null,
		modifiedById: '',
		modifiedAt: null,
		modifiedAtStart: null,
		modifiedAtEnd: null,
	};
}

const state = reactive({
	fromTitle: '',
	maxHeight: 0,
	formTable: {
		data: [],
		loading: false,
		total: 0,
		params: getDefaultQueryParams(),
	},
	options: [
		{ label: t('message.userFields.Active'), value: false },
		{ label: t('message.userFields.Inactive'), value: true },
	],
});

// 设置默认的最大高度
const tableMaxHeight = computed(() => (state.maxHeight > 0 ? state.maxHeight : 500));

var isMounted = false;

onMounted(async () => {
	isMounted = true;

	if (isMounted) {
		onSearch();
	}

	updateMaxHeight();
	window.addEventListener('resize', updateMaxHeight);
});

const updateMaxHeight = () => {
	nextTick(() => {
		setTimeout(() => {
			state.maxHeight = window.innerHeight - 420;
		}, 100);
	});
};

onActivated(async () => {
	if (!isMounted) {
		await onSearch();
	}
});

onDeactivated(() => {
	isMounted = false;
});

const onAdd = () => {
	state.fromTitle = t('message.wWE_ImagesButtons.createWWE_Images');
	createOrEditRef.value.open(getDefaultQueryParams());
};

const onRestore = async (row: any) => {
	await wWE_ImagesApi.restore(row.id)
		.then((rs: any) => {
			ElMessage.success(t('message.page.Success'));
			onSearch();
		})
		.catch((rs: any) => {
			if (rs !== 'cancel') {
				ElMessage.error(rs.resultMsg);
			}
		});
}

const onSearch = async () => {
	state.formTable.loading = true;

	await wWE_ImagesApi
		.Query(state.formTable.params)
		.then((rs: any) => {
			state.formTable.data = rs.data;
			state.formTable.total = rs.totalCount;

			let obj = { entityName: 'WWE_Images', pageParams: state.formTable.params };
			exportRef?.value?.getColumns(obj);
		})
		.catch((rs: any) => {
			ElMessage.error(rs);
		})
		.finally(() => {
			state.formTable.loading = false;

			if (state.formTable.data.length <= 0) {
				//数据为空，清空表头的默认选中
				nextTick(() => {
					proxy.$refs.tableRef.clearCheckboxRow();
				});
			}
		});
};

const onSortChange = (column: any) => {
	if (!column.field) {
		if (column.column.sortable) {
			state.formTable.params.order = column.column.field;

			let field = column.column.field;

			// 默认降序
			let order: 'desc' | 'asc' = 'desc';

			const $table = tableRef.value;

			if (state.formTable.params.sort == 'desc') {
				state.formTable.params.sort = 'asc';
				order = 'asc';

				if ($table) {
					$table.setSort({ field, order });
				}
			} else {
				state.formTable.params.sort = 'desc';
				order = 'desc';

				if ($table) {
					$table.setSort({ field, order });
				}
			}

			onSearch();
		}
	} else {
		state.formTable.params.order = column.field;
		state.formTable.params.sort = column.order;

		onSearch();
	}
};

const onClear = () => {
	clearQueryParams(state.formTable.params);
	onSearch();
};

function clearQueryParams(params: any): void {
	Object.assign(params, getDefaultQueryParams());
}

const onSizeChange = (val: number) => {
	state.formTable.params.pageSize = val;

	onSearch();
};

const onCurrentChange = (val: number) => {
	state.formTable.params.pageIndex = val;

	onSearch();
};

const cellClick = ({ row }: any) => {
	const $table = tableRef.value;
	if ($table) {
		$table.toggleCheckboxRow(row);
	}
};
</script>

<style scoped lang="scss">
@media (max-width: 800px) {
	:deep(.el-drawer) {
		width: 100% !important;
	}
}
</style>
