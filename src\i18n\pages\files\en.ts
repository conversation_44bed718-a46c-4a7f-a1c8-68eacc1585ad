
export default {
    filesSearch: {
        //Search Area
    },
    filesButtons: {
        createFiles: 'New Files',
        editFiles: 'Edit Files',
        importFiles: 'Import Files',
    },
    filesLabels: {
		FileId: 'File Id',
		FileCode: 'File Code',
		Path: 'Path',
		ThumbPath: 'Thumb Path',
		Name: 'Name',
		OriginalName: 'Original Name',
		Ext: 'Ext',
		Size: 'Size',
		Downloads: 'Downloads',
		Extra: 'Extra',
		FileUrl: 'File Url',
		FileType: 'File Type',
		Description: 'Description',
		IsDeleted: 'Is Deleted',
		CreatedAt: 'Uploaded at',
		UploadById: 'Uploaded by',
		FileServer: 'File Server',
		Content: 'Content',
		TotalCount: 'Total Count',
		FileBath: 'File Batch',
    },
    filesFields: {
		FileId: 'File Id',
		FileCode: 'File Code',
		Path: 'Path',
		ThumbPath: 'Thumb Path',
		Name: 'Name',
		OriginalName: 'Original Name',
		Ext: 'Ext',
		Size: 'Size',
		Downloads: 'Downloads',
		Extra: 'Extra',
		FileUrl: 'File Url',
		FileType: 'File Type',
		Description: 'Description',
		IsDeleted: 'Is Deleted',
		CreatedAt: 'Uploaded at',
		UploadById: 'Upload By Id',
		UploadBy: 'Uploaded by',
		FileServer: 'File Server',
		Content: 'Content',
		TotalCount: 'Total Count',
		FileBath: 'File Batch',
    },
    filesDlgTips: {
        deleteError: 'There is already a configuration relationship, deletion is not allowed!',
    },
    filesCommon: {
        ImportData: 'Import Data',
		UploadExcelFile: 'Upload Excel File',
        UploadCsvFile: 'Upload Csv File',
		Note: '(Note: This action will overwrite the data of the selected month)'
	},
};
            