<template>
	<el-select v-bind="$attrs" v-model="selectedValues" :multiple="multiple" :popper-class="['enhanced-select', $attrs.popperClass]">
		<!-- 全选功能 -->
		<template v-if="showCheckAll && multiple" #header>
			<div class="enhanced-select-header">
				<el-checkbox v-model="checkAll" :indeterminate="checkAllIndeterminate" @change="handleCheckAllChange">
					{{ checkAllText }}
				</el-checkbox>
				<slot name="header-extra"></slot>
			</div>
		</template>

		<!-- 支持外部传入的 el-option -->
		<slot>
			<!-- 默认选项渲染 -->
			<el-option v-for="item in processedOptions" :key="item.value" :label="item.label" :value="item.value" :disabled="item.disabled" />
		</slot>
	</el-select>
</template>

<script lang="ts" setup>
// ==============================================
// 增强型选择器组件 - 基于 el-select 封装
//
// 功能特性：
// 1. 默认启用 clearable、multiple、filterable、collapse-tags
// 2. 支持全选/全不选功能（多选模式下）
// 3. 支持两种选项传入方式：
//    - 通过 options prop 传入选项数组
//    - 通过 el-option 子组件传入选项
// 4. 保留所有 el-select 原生功能和样式
// 5. 支持自定义头部和底部内容
//
// 使用示例：
//
// 方式一：通过 options prop 传入选项
// <EnhancedSelect
//   v-model="selectedValues"
//   :options="[
//     { value: '1', label: '选项1' },
//     { value: '2', label: '选项2', disabled: true }
//   ]"
// />
//
// 方式二：通过 el-option 子组件传入选项
// <EnhancedSelect v-model="selectedValues">
//   <el-option
//     v-for="item in optionsData"
//     :key="item.id"
//     :label="item.name"
//     :value="item.id"
//   />
// </EnhancedSelect>
//
// 配置全选功能：
// :show-check-all="false" // 关闭全选功能
// check-all-text="全选"   // 自定义全选按钮文本
//
// 自定义插槽：
// #header-extra - 在头部添加额外内容
//
// 注意：
// 1. 全选功能仅在 multiple=true 时生效
// 2. 会自动跳过 disabled 的选项
// 3. 支持所有 el-select 的原生属性和事件
// ==============================================
import { ref, watch, computed, useSlots, onMounted } from 'vue';
import type { CheckboxValueType } from 'element-plus';

const props = defineProps({
	modelValue: {
		type: [Array, String, Number, Boolean] as any,
		default: () => [],
	},
	options: {
		type: Array as () => Array<{
			value: any;
			label: string;
			disabled?: boolean;
		}>,
		default: () => [],
	},
	multiple: {
		type: Boolean,
		default: false,
	},
	showCheckAll: {
		type: Boolean,
		default: true, // 默认显示全选功能
	},
	checkAllText: {
		type: String,
		default: 'All',
	},
});

const slots = useSlots();
const emit = defineEmits(['update:modelValue']);

const selectedValues = computed({
	get: () => props.modelValue,
	set: (value) => emit('update:modelValue', value),
});

const checkAll = ref(false);
const checkAllIndeterminate = ref(false);

// 获取所有可用的选项值（修正版）
const getAvailableOptionValues = () => {
	// 通过 options prop 传入的选项
	if (props.options.length > 0) {
		return props.options.filter((item) => !item.disabled).map((item) => item.value);
	}

	// 通过 el-option 子组件传入的选项
	const slotOptions = slots.default?.() || [];

	// 修正：从 children 中提取选项数据
	return slotOptions.flatMap((vnode) => {
		// 处理 vnode.children 数组中的每个子节点
		const children = Array.isArray(vnode.children) ? vnode.children : [];
		return children
			.filter((child) => !child.props?.disabled) // 过滤掉禁用的选项
			.map((child) => child.props?.value) // 提取选项值
			.filter((value) => value !== undefined); // 过滤掉未定义的值
	});
};

// 更新全选复选框状态
const updateCheckAllState = () => {
	if (!props.multiple || !props.showCheckAll) return;

	const availableValues = getAvailableOptionValues();
	const currentValues = selectedValues.value || [];

	if (currentValues.length === 0) {
		checkAll.value = false;
		checkAllIndeterminate.value = false;
	} else if (currentValues.length === availableValues.length) {
		checkAll.value = true;
		checkAllIndeterminate.value = false;
	} else {
		checkAllIndeterminate.value = true;
	}
};

// 处理全选/取消全选
const handleCheckAllChange = (checked: CheckboxValueType) => {
	console.log('props', props, checked);
	if (!props.multiple || !props.showCheckAll) return;

	selectedValues.value = checked ? [...getAvailableOptionValues()] : [];
};

// 监听必要的变化
watch(() => props.modelValue, updateCheckAllState, { deep: true });
watch(() => props.options, updateCheckAllState, { deep: true });
watch(() => slots.default?.(), updateCheckAllState, { flush: 'post' });

// 初始化时更新状态
onMounted(updateCheckAllState);
</script>

<style>
.enhanced-select {
	.enhanced-select-header {
		padding: 5px 10px;
		.el-checkbox {
			display: flex;
			height: unset;
			width: 100%;
		}
	}
}
</style>
