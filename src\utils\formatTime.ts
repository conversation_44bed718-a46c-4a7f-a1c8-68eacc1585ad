/**
 * 时间日期转换
 * @param date 当前时间，new Date() 格式
 * @param format 需要转换的时间格式字符串
 * @description format 字符串随意，如 `YYYY-mm、YYYY-mm-dd`
 * @description format 季度："YYYY-mm-dd HH:MM:SS QQQQ"
 * @description format 星期："YYYY-mm-dd HH:MM:SS WWW"
 * @description format 几周："YYYY-mm-dd HH:MM:SS ZZZ"
 * @description format 季度 + 星期 + 几周："YYYY-mm-dd HH:MM:SS WWW QQQQ ZZZ"
 * @returns 返回拼接后的时间字符串
 */
export function formatDate(date: Date, format: string): string {

	let we = date.getDay(); // 星期
	let z = getWeek(date); // 周
	let qut = Math.floor((date.getMonth() + 3) / 3).toString(); // 季度

	const opt: { [key: string]: string } = {
		'Y+': date.getFullYear().toString(), // 年
		'm+': (date.getMonth() + 1).toString(), // 月(月份从0开始，要+1)
		'd+': date.getDate().toString(), // 日
		'H+': date.getHours().toString(), // 时
		'M+': date.getMinutes().toString(), // 分
		'S+': date.getSeconds().toString(), // 秒
		'q+': qut, // 季度
	};

	const opt1: { [key: string]: string } = {
		'Y+': date.getFullYear().toString(), // 年
	};

	const monthNamesShort = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

	// 中文数字 (星期)
	const week: { [key: string]: string } = {
		'0': '日',
		'1': '一',
		'2': '二',
		'3': '三',
		'4': '四',
		'5': '五',
		'6': '六',
	};

	// 中文数字（季度）
	const quarter: { [key: string]: string } = {
		'1': '一',
		'2': '二',
		'3': '三',
		'4': '四',
	};

	if (/(W+)/.test(format))
		format = format.replace(RegExp.$1, RegExp.$1.length > 1 ? (RegExp.$1.length > 2 ? '星期' + week[we] : '周' + week[we]) : week[we]);

	if (/(Q+)/.test(format)) format = format.replace(RegExp.$1, RegExp.$1.length == 4 ? '第' + quarter[qut] + '季度' : quarter[qut]);

	if (/(Z+)/.test(format)) format = format.replace(RegExp.$1, RegExp.$1.length == 3 ? '第' + z + '周' : z + '');

	var isMMMDate = false

	if (/(MMM)/.test(format)) {
		isMMMDate = true
		format = format.replace(RegExp.$1, monthNamesShort[date.getMonth()])
	}

	if (isMMMDate) {
		for (let k in opt1) {
			let r = new RegExp('(' + k + ')').exec(format);
			if (r) format = format.replace(r[1], opt1[k]);
		}
	} else {
		for (let k in opt) {
			let r = new RegExp('(' + k + ')').exec(format);
			// 若输入的长度不为1，则前面补零
			if (r) format = format.replace(r[1], RegExp.$1.length == 1 ? opt[k] : opt[k].padStart(RegExp.$1.length, '0'));
		}
	}

	return format;
}

function getMonthFromString(mon: any) {
	return new Date(Date.parse(mon + " 1, 2012")).getMonth() + 1;
}

export function convertToDateString(monthYearStr: any) {
	const [monthStr, yearStr] = monthYearStr.split(' ');
	const month = getMonthFromString(monthStr);
	const year = parseInt(yearStr, 10);
	const date = new Date(year, month - 1, 1); // 设置为该月的第一天
	return formatDate(date, 'YYYY-mm-dd'); // 使用你的formatDate函数格式化日期
}

export function formatStrDate(strDate: string): string {
	if (!strDate) return '';
	if (strDate==undefined)
	{
		return '';
	}
	if (strDate.includes("1900") ||strDate.includes("1970")  ||strDate.includes("0001") ) {
		return "";
	}

	const filteredString = strDate.replace(/(AM|PM)/g, '').trim();

	var d = new Date(filteredString);

	return formatDate(d, 'YYYY-mm-dd HH:MM');
}

export function formatDay(strDate: string): string {
	if (!strDate) return '';

	const filteredString = strDate.replace(/(AM|PM)/g, '').trim();

	var d = new Date(filteredString);

	return formatDate(d, 'mm/dd/YYYY');
}
export function formatDateTime(): string {
	var strDate = new Date().toString();

	const filteredString = strDate.replace(/(AM|PM)/g, '').trim();

	var d = new Date(filteredString);

	return formatDate(d, 'YYYYmmddHHMM');
}
/**
 * 获取当前日期是第几周
 * @param dateTime 当前传入的日期值
 * @returns 返回第几周数字值
 */
export function getWeek(dateTime: Date): number {
	let temptTime = new Date(dateTime.getTime());
	// 周几
	let weekday = temptTime.getDay() || 7;
	// 周1+5天=周六
	temptTime.setDate(temptTime.getDate() - weekday + 1 + 5);
	let firstDay = new Date(temptTime.getFullYear(), 0, 1);
	let dayOfWeek = firstDay.getDay();
	let spendDay = 1;
	if (dayOfWeek != 0) spendDay = 7 - dayOfWeek + 1;
	firstDay = new Date(temptTime.getFullYear(), 0, 1 + spendDay);
	let d = Math.ceil((temptTime.valueOf() - firstDay.valueOf()) / 86400000);
	let result = Math.ceil(d / 7);
	return result;
}

/**
 * 将时间转换为 `几秒前`、`几分钟前`、`几小时前`、`几天前`
 * @param param 当前时间，new Date() 格式或者字符串时间格式
 * @param format 需要转换的时间格式字符串
 * @description param 10秒：  10 * 1000
 * @description param 1分：   60 * 1000
 * @description param 1小时： 60 * 60 * 1000
 * @description param 24小时：60 * 60 * 24 * 1000
 * @description param 3天：   60 * 60* 24 * 1000 * 3
 * @returns 返回拼接后的时间字符串
 */
export function formatPast(param: string | Date, format: string = 'YYYY-mm-dd'): string {
	// 传入格式处理、存储转换值
	let t: any, s: number;
	// 获取js 时间戳
	let time: number = new Date().getTime();
	// 是否是对象
	typeof param === 'string' || 'object' ? (t = new Date(param).getTime()) : (t = param);
	// 当前时间戳 - 传入时间戳
	time = Number.parseInt(`${time - t}`);
	if (time < 10000) {
		// 10秒内
		return '刚刚';
	} else if (time < 60000 && time >= 10000) {
		// 超过10秒少于1分钟内
		s = Math.floor(time / 1000);
		return `${s}秒前`;
	} else if (time < 3600000 && time >= 60000) {
		// 超过1分钟少于1小时
		s = Math.floor(time / 60000);
		return `${s}分钟前`;
	} else if (time < 86400000 && time >= 3600000) {
		// 超过1小时少于24小时
		s = Math.floor(time / 3600000);
		return `${s}小时前`;
	} else if (time < ********* && time >= 86400000) {
		// 超过1天少于3天内
		s = Math.floor(time / 86400000);
		return `${s}天前`;
	} else {
		// 超过3天
		let date = typeof param === 'string' || 'object' ? new Date(param) : param;
		return formatDate(date, format);
	}
}

/**
 * 时间问候语
 * @param param 当前时间，new Date() 格式
 * @description param 调用 `formatAxis(new Date())` 输出 `上午好`
 * @returns 返回拼接后的时间字符串
 */
export function formatAxis(param: Date): string {
	let hour: number = new Date(param).getHours();
	if (hour < 6) return '凌晨好';
	else if (hour < 9) return '早上好';
	else if (hour < 12) return '上午好';
	else if (hour < 14) return '中午好';
	else if (hour < 17) return '下午好';
	else if (hour < 19) return '傍晚好';
	else if (hour < 22) return '晚上好';
	else return '夜里好';
}

export function formatTypeTime(param: any) {
	const startTime = new Date(new Date(new Date().toLocaleDateString()).getTime());
	let endTime = new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1);
	if (param == 0) {
		return [startTime, endTime];
	}
	if (param == 1) {
		return [new Date(getBeginYesterday()), new Date(getEndYesterday())];
	}
	if (param == 2) {
		return [new Date(getBeginWeek()), new Date(getEndWeek())];
	}
	if (param == 3) {
		return [new Date(getBeginLastWeek()), new Date(getEndLastWeek())];
	}
	if (param == 4) {
		return [new Date(getBeginMonth()), new Date(getEndMonth())];
	}
}

/**
 * 日期范围工具类
 */
export function startTime(time?: any) {
	const nowTimeDate = new Date(time);
	return nowTimeDate.setHours(0, 0, 0, 0);
}

export function endTime(time?: any) {
	const nowTimeDate = new Date(time);
	return nowTimeDate.setHours(23, 59, 59, 999);
}

/***
 * 当前时间
 */
export function getCurrentDate() {
	return new Date();
}

/***
 * 今天的开始时间
 */
export function getBeginToday(): any {
	return new Date(new Date(new Date().toLocaleDateString()).getTime());
}

/***
 * 昨天开始时间
 */
export function getBeginYesterday() {
	return startTime(getBeginToday() - 24 * 60 * 60 * 1000);
}

/***
 * 昨天结束时间时间
 */
export function getEndYesterday() {
	return endTime(getBeginToday() - 24 * 60 * 60 * 1000);
}
/***
 * 本周的第一天时间
 */
export function getBeginWeek() {
	var currentDate = getCurrentDate();
	var week = currentDate.getDay();

	//一天的毫秒数
	var millisecond = 1000 * 60 * 60 * 24;
	//减去的天数
	var minusDay = week != 0 ? week - 1 : 6;
	//本周 周一
	var monday = new Date(currentDate.getTime() - minusDay * millisecond);
	return monday;
}

/***
 * 本周的最后一天时间
 */
export function getEndWeek() {
	var currentDate = getCurrentDate();
	var week = currentDate.getDay();
	//一天的毫秒数
	var millisecond = 1000 * 60 * 60 * 24;
	//减去的天数
	var minusDay = week != 0 ? week - 1 : 6;
	//本周 周日
	var monday = new Date(currentDate.getTime() - minusDay * millisecond);
	var sunday = new Date(monday.getTime() + 6 * millisecond);
	//返回
	return endTime(sunday);
}

/***
 * 上周的开始
 */
export function getBeginLastWeek() {
	var currentDate = getCurrentDate();
	var first = currentDate.getDate() - currentDate.getDay() - 6;
	var startDate = new Date(currentDate.setDate(first));
	return startTime(startDate);
}

/***
 * 上周的结束
 */
export function getEndLastWeek() {
	var currentDate = getCurrentDate();
	var first = currentDate.getDate() - currentDate.getDay() - 6;
	var last = first + 6;
	var endDate = new Date(currentDate.setDate(last));
	return endTime(endDate);
}

/***
 * 本月的第一天时间
 */
export function getBeginMonth() {
	var currentDate = getCurrentDate();
	var currentMonth = currentDate.getMonth();
	//获得当前年份4位年
	var currentYear = currentDate.getFullYear();
	//求出本月第一天
	var firstDay = new Date(currentYear, currentMonth, 1);

	return firstDay;
}

/***
 * 本月的最后一天时间
 */
export function getEndMonth() {
	//获取当前时间
	var currentDate = getCurrentDate();
	var fullYear = currentDate.getFullYear();
	var month = currentDate.getMonth() + 1; // getMonth 方法返回 0-11，代表1-12月
	var endOfMonth = new Date(fullYear, month, 0);
	return endTime(endOfMonth);
}

export function getEndMonthV2() {
	//获取当前时间
	var currentDate = getCurrentDate();
	var fullYear = currentDate.getFullYear();
	var month = currentDate.getMonth() + 1; // getMonth 方法返回 0-11，代表1-12月
	var endOfMonth = new Date(fullYear, month, 0);
	return endOfMonth;
}

/***
 * 上月的第一天时间
 */
export function getBeginLastMonth() {
	//获取当前时间
	var currentDate = getCurrentDate();
	//获得当前月份0-11
	var currentMonth = currentDate.getMonth();
	//获得当前年份4位年
	var currentYear = currentDate.getFullYear();
	//获得上一个月的第一天
	var priorMonthFirstDay = getPriorMonthFirstDay(currentYear, currentMonth);
	return priorMonthFirstDay;
}

/***
 * 上月的最后一天时间
 */
export function getEndLastMonth() {
	//获取当前时间
	var currentDate = getCurrentDate();
	//获得当前月份0-11
	var currentMonth = currentDate.getMonth();
	//获得当前年份4位年
	var currentYear = currentDate.getFullYear();

	//当为12月的时候年份需要加1
	//月份需要更新为0 也就是下一年的第一个月
	if (currentMonth == 11) {
		currentYear++;
		currentMonth = 0; //就为
	} else {
		//否则只是月份增加,以便求的下一月的第一天
		currentMonth++;
	}

	//一天的毫秒数
	var millisecond = 1000 * 60 * 60 * 24;
	//求出上月的最后一天
	var lastDay = new Date(getBeginMonth().getTime() - millisecond);

	return endTime(lastDay);
}

/**
 * 返回上一个月的第一天Date类型
 * @param year 年
 * @param month 月
 **/
export function getPriorMonthFirstDay(year: any, month: any) {
	//年份为0代表,是本年的第一月,所以不能减
	if (month == 0) {
		month = 11; //月份为上年的最后月份
		year--; //年份减1
		return new Date(year, month, 1);
	}
	//否则,只减去月份
	month--;
	return new Date(year, month, 1);
}
