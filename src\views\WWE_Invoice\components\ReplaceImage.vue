<template>
	<div class="replace-image-container">
		<el-dialog v-model="dialogVisible" title="Replace Invoice PDF" width="500px" :close-on-click-modal="false" draggable>
			<div class="upload-wrapper">
				<scUploadFile name="files" v-model="fileList" :limit="1" accept=".pdf" :drag="true" :auto-upload="true" :on-success="handleUploadSuccess" :on-before="handleBeforeUpload" :data="uploadParams">
					<template #default>
						<div class="upload-content">
							<el-icon class="upload-icon"><UploadFilled /></el-icon>
							<p class="upload-text">
								Click to upload<br />
								PDF files under 10MB.
							</p>
						</div>
					</template>
				</scUploadFile>
			</div>

			<template #footer>
				<el-button @click="dialogVisible = false" :disabled="uploading">Cancel</el-button>
				<el-button type="primary" @click="submitReplace" :loading="uploading" :disabled="fileList.length==0">Confirm Replace</el-button>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { UploadFilled } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import invoiceApi from '/@/api/WWE_Invoice/index';

const dialogVisible = ref(false);
const fileList = ref<any[]>([]);
const fileId = ref<string | null>(null);
const uploading = ref(false);
const invoiceId = ref<string | null>(null);

const emit = defineEmits(['complete']);

const open = (id: string) => {
	dialogVisible.value = true;
	invoiceId.value = id;
	console.log('Open replace image dialog for invoiceId:', id);
	fileList.value = [];
	fileId.value = null;
};

const handleBeforeUpload = (file: File) => {
	const isPDF = file.type === 'application/pdf';
	const isLt10M = file.size / 1024 / 1024 < 10;

	if (!isPDF) {
		ElMessage.error('Only PDF files are allowed.');
		return false;
	}
	if (!isLt10M) {
		ElMessage.error('File size must be less than 10MB.');
		return false;
	}

	uploading.value = true;
	return true;
};

const handleUploadSuccess = (response: any) => {
	const files = response.data;
	const firstResponse = Array.isArray(files) ? files[0] : files;
	const id = firstResponse?.fileId;

	if (!id) {
		ElMessage.error('No fileId returned from server');
		uploading.value = false;
		return;
	}

	fileId.value = id;
	uploading.value = false;
	ElMessage.success('Upload successful. Click confirm to replace.');
};

const submitReplace = () => {
	if (!fileId.value) {
		ElMessage.warning('Please upload a file first.');
		return;
	}

	uploading.value = true;
	var requestParams = {
		invoiceId: invoiceId.value,
		fileId: fileId.value,
	};
	console.log('Request parameters:', requestParams);

	invoiceApi
		.ReplaceImage(requestParams)
		.then((rs) => {
			ElMessage.success('Invoice image replaced successfully!');
			dialogVisible.value = false;
			emit('complete', { success: true, imageId: rs.data });
		})
		.catch((error: any) => {
			const err = error as HandledError;
			if (!err.isHandled) {
				ElMessage.error(err.message || 'Replacement failed');
			}
		})
		.finally(() => {
			uploading.value = false;
			fileList.value = [];
		});
};

defineExpose({ open });
</script>

<style lang="scss" scoped>
.replace-image-container {
	.upload-wrapper {
		border-radius: 6px;
		text-align: center;

		margin-bottom: 20px;

		.upload-content {
			display: flex;
			flex-direction: column;
			align-items: center;

			.upload-icon {
				font-size: 40px;
				color: #00b894;
				margin-bottom: 8px;
			}

			.upload-text {
				font-size: 14px;
				color: #999;
				line-height: 1.4;
			}
		}
	}
}
</style>
