﻿<template>
	<div class="user-edit-container">
		<el-dialog :title="title" v-model="isShowDialog" width="400px" :close-on-click-modal="false">
			<el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="90px" label-position="top">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item :label="$t('message.userFields.firstName')">
							<el-input v-model="ruleForm.firstName" size="middle" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item :label="$t('message.userFields.userName')">
							<el-input v-model="ruleForm.userName" size="middle" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item>
							<el-button @click="onCancel" size="small">{{ $t('message.page.buttonCancel') }}</el-button>
							<el-button :loading="saveLoading" type="primary" @click="onSubmit" size="small">{{
								$t('message.page.buttonPasswordReset') }}</el-button>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
		</el-dialog>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, getCurrentInstance, defineComponent } from 'vue';
import { ElMessage } from 'element-plus';

import userApi from '/@/api/user';
import { useI18n } from 'vue-i18n';

export default defineComponent({
	name: 'ResetPasswordDlg',
	components: {},
	setup() {
		const { t } = useI18n();
		const { proxy } = getCurrentInstance() as any;
		const state = reactive({
			title: t('message.page.buttonPasswordReset'),
			isShowDialog: false,
			saveLoading: false,
			userId: 0,
			ruleForm: {},
			rules: {
				status: [{ required: true, message: 'Please input', trigger: 'blur' }],
			},
		});
		// 打开弹窗
		const openDialog = (params: any) => {
			state.ruleForm = params;
			state.isShowDialog = true;
		};

		// 关闭弹窗
		const closeDialog = () => {
			state.isShowDialog = false;
		};
		// 取消
		const onCancel = () => {
			closeDialog();
		};
		// 保存
		const onSubmit = () => {
			proxy.$refs.ruleFormRef.validate((valid: any) => {
				if (!valid) {
					return;
				}
				var obj = { userId: state.ruleForm.id };
				state.saveLoading = true;
				userApi
					.AdminResetPassword(obj)
					.then(() => {
						ElMessage.success('Succeed');
						closeDialog();
					})
					.catch((error: HandledError) => {
						if (!error.isHandled) {
							const errorCode = error.code;
							const errorMessage = error.message;
							ElMessage.error(errorMessage);
						}
					})
					.finally(() => {
						state.saveLoading = false;
					});
			});
		};

		return {
			openDialog,
			closeDialog,
			onCancel,
			onSubmit,
			...toRefs(state),
		};
	},
});
</script>
