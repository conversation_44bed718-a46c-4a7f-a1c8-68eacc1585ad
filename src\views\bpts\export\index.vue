<template>
<h1>ssssssssssssssss</h1>
</template>

<script lang="ts" setup>

import {onMounted} from 'vue'
import exportApi from '/@/api/export/index';
import { formatDateTime } from '/@/utils/formatTime';


onMounted(() => {
    
    let TreeNodes=[{value:'aa',label:'aaa'}]
    getExportData(TreeNodes);

})
const getExportData=async(name:any)=>{

    await exportApi
    .Export(name)
    .then((rs: any) => {

        downloadCallback(rs);
    })
    .catch(() => { })
    .finally();



}
const downloadCallback = (rs: any) => {
    let data = rs;
    var newBlob = new Blob([data], { type: 'text/plain;charset=UTF-8' });
    var anchor = document.createElement('a');
    //anchor.download = 'Groups_' + formatDateTime() + '.xlsx';
    anchor.download = 'Groups_' + formatDateTime() + '.csv';
    anchor.href = window.URL.createObjectURL(newBlob);
    anchor.click();
};

</script>

<style  scoped></style>