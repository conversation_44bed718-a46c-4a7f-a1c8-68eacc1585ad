import request from '/@/utils/request';

class loginApi {
	Signin(data: any) {
		return request({
			url: '/api/token/Login',
			method: 'post',
			data,
		});
	}

	SignOut(data: any) {
		return request({
			url: '/api/token/Logout',
			method: 'post',
			data,
		});
	}
	
	RefreshToken(data: any) {
		return request({
			url: '/api/token/RefreshToken',
			method: 'post',
			data,
		});
	}

	ResetInitPassword(data : any){
		return request({
			url: '/api/token/ResetInitPassword',
			method: 'post',
			data,
		});
	}

	HasNeedToChangePwd(data : any) {
		return request({
			url: '/api/token/HasNeedtToChangePwd',
			method: 'post',
			data,
		});
	}

	GetMyInfo(params?: any) {
		return request({
			url: '/api/token/GetMyInfo',
			method: 'get',
			params,
		});
	}

	TokenCheck(params?: any){
		return request({
			url: '/api/token/TokenCheck',
			method: 'get',
			params,
		});
	}

	SSOLogin(params?: any) {
		return request({
			url: '/api/token/SSOLogin',
			method: 'get',
			params,
		});
	}


	SSOLogout(params?: any) {
		return request({
			url: '/api/token/SSOLogout',
			method: 'get',
			params,
		});
	}

	GetSSOLoginMessage(params?: any) {
		return request({
			url: '/api/token/GetSSOLoginMessage',
			method: 'get',
			params,
		});
	}
}

export default new loginApi();
