<template>
	<div class="system-edit-menu-container">
		<el-drawer v-model="state.showDialog" destroy-on-close :title="title" draggable :close-on-click-modal="false" :close-on-press-escape="false" width="500px" :direction="direction">
			<div class="drawer-content">
				<el-form :model="state.form" ref="formRef" :rules="state.rules" label-width="130px" label-position="top">
					<el-row :gutter="35">
						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.wWE_Carrier_LoginFields.AccountName')" prop="accountId">
								<el-select v-model="state.form.accountId" :placeholder="$t('message.page.selectKeyPlaceholder')" mu filterable clearable>
									<el-option v-for="item in accountOptions" :key="item.key" :label="item.label" :value="item.key" />
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.wWE_Carrier_LoginFields.CarrierName')" prop="carrierId">
								<el-select v-model="state.form.carrierId" :placeholder="$t('message.page.selectKeyPlaceholder')" filterable clearable>
									<el-option v-for="item in carrierOptions" :key="item.key" :label="item.label" :value="item.key" />
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.wWE_Carrier_LoginFields.Login')" prop="login">
								<el-input v-model="state.form.login" clearable size="default" />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.wWE_Carrier_LoginFields.Password')" prop="password">
								<el-input :type="state.isShowPassword ? 'text' : 'password'" :placeholder="$t('message.login.Password')" v-model="state.form.password" autocomplete="off" size="default">
									<template #prefix>
										<el-icon class="el-input__icon"><ele-Unlock /></el-icon>
									</template>
									<template #suffix>
										<el-icon @click="state.isShowPassword = !state.isShowPassword">
											<template v-if="state.isShowPassword">
												<img src="/img/show.svg" />
											</template>
											<template v-else>
												<img src="/img/hide.svg" />
											</template>
										</el-icon>
									</template>
								</el-input>
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.wWE_Carrier_LoginFields.ContactEmail')" prop="contactEmail">
								<el-input v-model="state.form.contactEmail" clearable size="default" />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.wWE_Carrier_LoginFields.ContactPhone')" prop="contactPhone">
								<el-input v-model="state.form.contactPhone" clearable size="default" />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.wWE_Carrier_LoginFields.RequiredFileTypes')" prop="requiredFileTypes">
								<el-select v-model="state.form.requiredFileTypes" multiple clearable size="default" :placeholder="$t('message.page.selectKeyPlaceholder')">
									<el-option v-for="item in allFileTypeOptions" :key="item.itemId" :label="item.itemName" :value="item.itemValue"> </el-option>
								</el-select>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>

				<div class="drawer-footer">
					<span class="dialog-footer">
						<el-button @click="onCancel" size="small">{{ $t('message.page.buttonCancel') }}</el-button>
						<el-button :loading="state.loading" type="primary" @click="onSubmit" size="small">{{ $t('message.page.buttonSave') }}</el-button>
					</span>
				</div>
			</div>
		</el-drawer>
	</div>
</template>

<script lang="ts" setup name="wWE_Carrier_Login/createOrEdit">
import { reactive, toRefs, ref, getCurrentInstance, computed, watch } from 'vue';

import { cloneDeep } from 'lodash-es';

import { useI18n } from 'vue-i18n';

import { ElMessageBox, ElMessage } from 'element-plus';

import type { DrawerProps } from 'element-plus';

import accountApi from '/@/api/WWE_Account';
import carrierApi from '/@/api/WWE_Carrier';
import invoiceApi from '/@/api/WWE_Invoice/index';
import carrierLoginApi from '/@/api/WWE_Carrier_Login/index';
import { DictionaryOption, SelectOption } from '/@/models';

interface CreateOrEditInput {
	id: string;
	accountId: string;
	accountName: string;
	carrierId: string;
	carrierName: string;
	carrierCode: string;
	contactEmail: string;
	contactPhone: string;
	login: string;
	password: string;
	requiredFileTypes: string | string[];
	createdByName: string;
	modifiedByName: string;
}

defineProps({
	title: {
		type: String,
		default: '',
	},
});

const emit = defineEmits(['refreshData']);

const direction = ref<DrawerProps['direction']>('rtl');

const { t } = useI18n();
const formRef = ref();

const allFileTypeOptions = ref<DictionaryOption[]>([]);
const accountOptions = ref<SelectOption[]>([]);
const carrierOptions = ref<SelectOption[]>([]);

const state = reactive({
	isShowPassword: false,
	showDialog: false,
	loading: false,
	form: {} as CreateOrEditInput,
	rules: {
		accountId: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: ['blur', 'change'] }],
		carrierId: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: ['blur', 'change'] }],
		login: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: ['blur', 'change'] }],
		password: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: ['blur', 'change'] }],
		requiredFileTypes: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: ['blur', 'change'] }],
		contactEmail: [
			{
				validator: (rule: any, value: any, callback: any) => {
					if (!value) {
						// 空值直接通过（非必填）
						callback();
						return;
					}

					const regEmail = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
					if (regEmail.test(value)) {
						callback(); // 验证通过
					} else {
						callback(new Error('Please enter a valid email address')); // 验证失败
					}
				},
				trigger: ['blur', 'change'],
			},
		],
	},
});

const handleCarrierChange = (newCarrierId: string) => {
	const carrier = carrierOptions.value.find((c) => c.key === newCarrierId);
	const fileTypes = carrier?.requiredFileTypes?.split(',') || [];
	allFileTypeOptions.value = fileTypes.map((t, i) => ({
		itemId: t,
		itemName: t,
		itemValue: t,
	}));
};

watch(() => state.form.carrierId, handleCarrierChange);

const open = async (row: CreateOrEditInput) => {
	let formData = cloneDeep(row) as CreateOrEditInput;
	if (formData.requiredFileTypes && typeof formData.requiredFileTypes === 'string') {
		formData.requiredFileTypes = formData.requiredFileTypes.split(',');
	}
	state.form = formData;
	state.showDialog = true;
	await loadData();
};

const loadData = async () => {
	allFileTypeOptions.value = [];
	const [accountsResponse, carriersResponse] = await Promise.all([accountApi.GetAccountOptions(), carrierApi.GetCarrierOptions()]);
	accountOptions.value = accountsResponse?.data || [];
	carrierOptions.value = (carriersResponse?.data || []).map((carrier) => ({
		...carrier,
		label: `${carrier.value} - ${carrier.label}`,
	}));
	if (state.form.carrierId) {
		handleCarrierChange(state.form.carrierId);
	}
};

const onCancel = () => {
	closeDialog();
};

const closeDialog = () => {
	onInitForm();
	state.showDialog = false;
};

const onSubmit1 = async () => {
	formRef.value.validate(async (valid: boolean) => {
		if (!valid) return;

		let obj = Object.assign({}, state.form);
		if (Array.isArray(obj.requiredFileTypes)) {
			obj.requiredFileTypes = obj.requiredFileTypes.join(',');
		}
		state.loading = true;
		await carrierLoginApi
			.Save(obj)
			.then(() => {
				ElMessage.success(t('message.page.Success'));
				emit('refreshData');

				closeDialog();
				state.loading = false;
			})
			.catch((error: HandledError) => {
				if (!error.isHandled) {
					const errorCode = error.code;
					const errorMessage = error.message;
					ElMessage.error(errorMessage);
				}
			})
			.finally(() => {
				state.loading = false;
			});
	});
};

const onSubmit = async () => {
	formRef.value.validate(async (valid: boolean) => {
		if (!valid) return;

		state.loading = true;

		if (state.form.id) {
			try {
				const req = {
					accountId: state.form.accountId,
					carrierId: state.form.carrierId,
				};
				const result = await invoiceApi.CheckAssociatedData(req);

				if (result.data) {
					ElMessageBox.confirm('This record has associated data. Are you sure you want to proceed?', 'Warning', {
						confirmButtonText: 'Confirm',
						cancelButtonText: 'Cancel',
						type: 'warning',
					})
						.then(async () => {
							await saveData();
						})
						.catch(() => {
							state.loading = false;
						});
					return;
				}
			} catch (error) {
				ElMessage.error('Operation failed.');
				state.loading = false;
				return;
			}
		}

		await saveData();
	});
};

const saveData = async () => {
	let obj = Object.assign({}, state.form);
	if (Array.isArray(obj.requiredFileTypes)) {
		obj.requiredFileTypes = obj.requiredFileTypes.join(',');
	}

	await carrierLoginApi
		.Save(obj)
		.then(() => {
			ElMessage.success(t('message.page.Success'));
			emit('refreshData');

			closeDialog();
			state.loading = false;
		})
		.catch((error: HandledError) => {
			if (!error.isHandled) {
				const errorCode = error.code;
				const errorMessage = error.message;
				ElMessage.error(errorMessage);
			}
		})
		.finally(() => {
			state.loading = false;
		});
};

const onInitForm = async () => {};

defineExpose({
	open,
});
</script>

<style scoped>
.drawer-content {
	padding: 20px;
}

.drawer-footer {
	text-align: right;
}

.el-icon img {
	height: 1em;
	width: 1em;
	cursor: pointer;
}
</style>
