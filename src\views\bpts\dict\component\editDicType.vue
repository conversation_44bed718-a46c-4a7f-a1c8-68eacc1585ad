﻿<template>
	<div class="dict-edit-container">
		<el-drawer :title="title" v-model="isShowDialog" destroy-on-close draggable :close-on-click-modal="false"
			:close-on-press-escape="false" :size="600">
			<template #header>
				<h3>{{ title }}</h3>
			</template>
			<div class="drawer-content">
				<el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="90px">
					<el-row :gutter="35">
						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item label="Name" prop="name">
								<el-input v-model="ruleForm.name" placeholder="Please input " size="middle" />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item label="Value" prop="value">
								<el-input v-model="ruleForm.value" placeholder="Please input " size="middle" />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item label="Description" prop="description">
								<el-input v-model="ruleForm.description" type="textarea" placeholder=""
									maxlength="150" size="middle"> </el-input>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>
				<div class="drawer-footer mt10">
					<span class="dialog-footer">
						<el-button @click="onCancel" size="small">Cancel</el-button>
						<el-button v-if="ruleForm.dictId <= 0" @click="onInitForm" size="small">Reset</el-button>
						<el-button v-if="ruleForm.dictId > 0" @click="onDelete" type="danger"
							size="small">Delete</el-button>
						<el-button :loading="loading" type="primary" @click="onSubmit" size="small">Save</el-button>
					</span>
				</div>
			</div>
		</el-drawer>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, getCurrentInstance } from 'vue';
import { isNotEmptyOrNull, parseThanZero } from '/@/utils';
import { ElMessageBox, ElMessage } from 'element-plus';

import dictApi from '/@/api/dict/index';

export default {
	name: 'DictEdit',
	components: {},
	setup(props, context) {
		const { proxy } = getCurrentInstance() as any;
		const state = reactive({
			title: 'Create Dictionary category',
			isShowDialog: false,
			loading: false,
			ruleForm: {},
			rules: {
				name: [{ required: true, message: 'Please input', trigger: 'blur' }],
				value: [{ required: true, message: 'Please input', trigger: 'blur' }],
			},
		});
		// 打开弹窗
		const openDialog = (row: Object) => {
			onInitForm();
			state.ruleForm = row || state.ruleForm;
			state.isShowDialog = true;

			if (parseThanZero(state.ruleForm.dictId)) {
				state.title = 'Update Dictionary Category';
			} else {
				state.title = 'Create Dictionary Category';
			}
		};
		// 关闭弹窗
		const closeDialog = () => {
			onInitForm();
			state.isShowDialog = false;
		};
		// 取消
		const onCancel = () => {
			closeDialog();
		};
		// 保存
		const onSubmit = () => {
			proxy.$refs.ruleFormRef.validate((valid: any) => {
				if (!valid) {
					return;
				}
				var obj = state.ruleForm;
				state.loading = true;
				dictApi
					.Save(obj)
					.then((rs) => {
						context.emit('fetchData');
						closeDialog();
					})
					.catch((error: HandledError) => {
						if (!error.isHandled) {
							const errorCode = error.code;
							const errorMessage = error.message;
							ElMessage.error(errorMessage);
						}
					})
					.finally(() => {
						state.loading = false;
					});
			});
		};
		//根据id获取完整的信息
		const getData = (id: any) => {
			dictApi.GetByKey(id).then((rs) => {
				state.ruleForm = Object.assign({}, rs.data);
			});
		};
		//删除纪录
		const onDelete = () => {
			ElMessageBox.confirm(`Are you sure to delete this?`, 'Tips', {
				confirmButtonText: 'OK',
				cancelButtonText: 'No，Thanks',
				type: 'warning',
			}).then(() => {
				dictApi
					.DeleteByKey(state.ruleForm.dictId)
					.then(() => {
						ElMessage.success('Succeed');
						context.emit('fetchData');
						closeDialog();
					})
					.catch((error: HandledError) => {
						if (!error.isHandled) {
							const errorCode = error.code;
							const errorMessage = error.message;
							ElMessage.error(errorMessage);
						}
					});
			});
		};
		// 重置表单
		const onInitForm = () => {
			if (proxy.$refs.ruleFormRef) {
				proxy.$refs.ruleFormRef.resetFields();
			}
			state.ruleForm = {
				dictId: 0, //
				name: '', //名称
				value: '', //对应值
				description: '', //描述
			};
			//初始化dictId=0，不生效，注释注释
			// if (state.ruleForm.dictId) {
			// 	state.title = 'Edit Dictionary category';
			// }
		};
		return {
			openDialog,
			closeDialog,
			onCancel,
			onSubmit,
			onDelete,
			onInitForm,
			...toRefs(state),
		};
	},
};
</script>

<style scoped>
.drawer-content {
	padding: 20px;
}

.drawer-footer {
	text-align: right;
}
</style>
