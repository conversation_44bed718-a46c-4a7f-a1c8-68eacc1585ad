﻿<template>
	<el-container class="tablelist" style="height: 100%; overflow: hidden">
		<el-card shadow="never">
			<div class="modules-index-search">
				<el-input size="small" v-model="tableData.param.searchKey" clearable style="max-width: 180px"
					@keyup.enter="onSearch"> </el-input>
				<el-button size="small" type="primary" class="ml10" @click="onSearch">
					<el-icon>
						<ele-Search />
					</el-icon>
					{{ $t('message.page.buttonSearch') }}
				</el-button>
			</div>
		</el-card>
		<el-card>
			<div class="left-panel">
				<el-button size="small" type="primary" icon="plus" class="ml10" @click="onAdd">
					{{ $t('message.page.buttonCreate') }}
				</el-button>

				<el-button type="danger" size="small" icon="delete" :disabled="tableData.selection.length == 0"
					@click="onDeleteByList">{{
						$t('message.page.buttonDeleteBatch')
					}}</el-button>
			</div>
			<div class="right-panel">
				<el-button size="small" class="ml10" @click="onAdd">
					<el-icon>
						<ele-Refresh />
					</el-icon>
					{{ $t('message.page.buttonRefresh') }}
				</el-button>

				<el-button size="small" class="ml10" @click="onPrint">
					<el-icon>
						<ele-Printer />
					</el-icon>
					{{ $t('message.page.buttonPrint') }}
				</el-button>
			</div>
		</el-card>
		<el-main class="nopadding" ref="printMain">
			<div class="scTable" style="height: 100%" ref="scTableMain">
				<div class="scTable-table">
					<el-table :data="tableData.data" v-loading="tableData.loading" height="calc(100%)" border
						@row-dblclick="onDetail" @selection-change="selectionChange">
						<template #empty>
							<el-empty :description="$t('message.page.emptyDescription')" :image-size="100"></el-empty>
						</template>
						<el-table-column type="selection" />

						<el-table-column :label="$t('message.roleFields.name')" width="200" show-overflow-tooltip>
							<template #default="{ row }">
								<span>{{ row.name }}</span>
							</template>
						</el-table-column>

						<el-table-column :label="$t('message.roleFields.code')" width="200" show-overflow-tooltip>
							<template #default="{ row }">
								<span>{{ row.code }}</span>
							</template>
						</el-table-column>

						<el-table-column :label="$t('message.roleFields.description')" show-overflow-tooltip>
							<template #default="{ row }">
								<span>{{ row.description }}</span>
							</template>
						</el-table-column>
						<el-table-column :label="$t('message.roleFields.createTime')" width="200">
							<template #default="{ row }">
								<span>{{ formatStrDate(row.createTime) }}</span>
							</template>
						</el-table-column>
						<el-table-column :label="$t('message.roleFields.orderSort')" width="100" align="left">
							<template #default="{ row }">
								<span>{{ row.orderSort }}</span>
							</template>
						</el-table-column>
						<el-table-column fixed="right" align="center" :label="$t('message.roleFields.permission')"
							width="150">
							<template #default="{ row }">
								<el-button size="mini" type="text" @click="onSetPermission(row)">{{
									$t('message.page.actionsSet') }}</el-button>
							</template>
						</el-table-column>

						<el-table-column fixed="right" align="left" :label="$t('message.page.actions')" width="160">
							<template #default="{ row }">
								<el-tooltip class="box-item" effect="dark" content="View" placement="bottom">
									<el-button size="mini" type="text" @click="onDetail(row)" icon="view">
										<!-- {{ $t('message.page.actionsView') }} -->
									</el-button></el-tooltip>
								<el-tooltip class="box-item" effect="dark" content="Edit" placement="bottom">
									<el-button size="mini" type="text" @click="onEdit(row)" icon="editPen">
										<!-- {{ $t('message.page.actionsEdit') }} -->
									</el-button></el-tooltip>
								<el-tooltip class="box-item" effect="dark" content="Delete" placement="bottom">
									<el-button size="mini" type="text" style="color: #ff3a3a" @click="onDelete(row)"
										icon="delete">
										<!-- {{ $t('message.page.actionsDelete') }}  -->
									</el-button>
								</el-tooltip>
							</template>
						</el-table-column>
					</el-table>
				</div>
				<div class="scTable-page">

					<el-pagination @size-change="onSizechange" @current-change="onCurrentChange" :pager-count="5"
						:page-sizes="[10, 20, 30]" v-model:current-page="tableData.param.pageIndex" background
						v-model:page-size="tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
						:total="tableData.total" small>
					</el-pagination>
				</div>
			</div>

			<Permission ref="permissionRef"></Permission>
			<CreateOrEdit ref="createOrEditRef" @fetchData="onInit"></CreateOrEdit>
			<el-drawer v-model="infoDrawer" title="信息详情" :size="600" destroy-on-close>
				<Detail ref="detailRef" :info="detailObj"></Detail>
			</el-drawer>
		</el-main>
	</el-container>
</template>

<script lang="ts">
//import { useRouter } from 'vue-router';
import { toRefs, reactive, onMounted, ref, defineComponent } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { formatStrDate } from '/@/utils/formatTime';
import print from '/@/utils/print.js';

import roleApi from '/@/api/role/index';
import Detail from './components/detail.vue';
import CreateOrEdit from './components/createOrEdit.vue';
import Permission from './components/permissionTable.vue';

export default defineComponent({
	name: 'systemRole',
	components: { Detail, CreateOrEdit, Permission },
	setup() {
		//const router = useRouter();
		const createOrEditRef = ref();
		const permissionRef = ref();
		const printMain = ref(null);
		const state = reactive({
			tableData: {
				data: [],
				total: 0,
				loading: false,
				selection: [],
				param: {
					pageIndex: 1,
					pageSize: 20,
					searchKey: '',
					order: 'id',
					sort: 'desc', // asc or desc
				},
			},
			infoDrawer: false,
			detailObj: {},
		});

		//初始化
		const onInit = () => {
			state.tableData.loading = true;
			roleApi
				.Query(state.tableData.param)
				.then((rs) => {
					state.tableData.data = rs.data;
					state.tableData.total = rs.totalCount;
				})
				.catch((rs) => { })
				.finally(() => (state.tableData.loading = false));
		};
		//搜索
		const onSearch = () => {
			onInit();
		};

		// 添加
		const onAdd = () => {
			createOrEditRef.value.openDialog({ action: 'Create' });
			//router.push('/role/Create');
		};
		// 修改
		const onEdit = (row: Object) => {
			createOrEditRef.value.openDialog({ action: 'Edit', id: row.id });
			//router.push('/role/edit/' + row.id);
		};
		// 删除
		const onDelete = (row: Object) => {
			ElMessageBox.confirm(`此操作将永久当前纪录，是否继续?`, '提示', {
				confirmButtonText: '确认',
				cancelButtonText: '取消',
				type: 'warning',
			}).then(() => {
				roleApi
					.DeleteByKey(row.id)
					.then((rs) => {
						if (!rs.data) {
							ElMessage.error(rs.resultMsg);
							return;
						}
						onInit();
					})
					.catch((rs) => { });
			});
		};
		//批量删除
		const onDeleteByList = () => {
			ElMessageBox.confirm(`确定删除选中的 ${state.tableData.selection.length} 项吗？`, '提示', {
				type: 'warning',
			}).then(() => {
				roleApi.Delete({ keys: state.tableData.selection.join(',') }).then((rs) => {
					if (rs.data == 0) {
						ElMessage.error(rs.resultMsg);
						return;
					}
					onInit();
				});
			});
		};
		//详细信息
		const onDetail = (row: any) => {
			state.detailObj = {};

			roleApi.Detail(row.id).then((rs) => {
				state.detailObj = rs.data;
				state.infoDrawer = true;
			});
		};
		//表格选择后回调事件
		const selectionChange = (selection: any) => {
			state.tableData.selection = selection;
		};
		//打印
		const onPrint = () => {
			print(printMain.value);
		};
		// pageSize 改变时触发
		const onSizechange = (val: number) => {
			state.tableData.param.pageSize = val;
			onInit();
		};
		// current-change 改变时触发
		const onCurrentChange = (val: number) => {
			state.tableData.param.pageIndex = val;
			onInit();
		};
		const onSetPermission = (row: Object) => {
			permissionRef.value.openDialog(row);
		};
		// 页面加载时
		onMounted(() => {
			setInterval(() => {
				console.log('这是每隔 3 秒钟打印一次的消息！', __NEXT_VERSION__);
			}, 3000);
			onInit();
		});

		return {
			printMain,
			onSetPermission,
			formatStrDate,
			onPrint,
			permissionRef,
			createOrEditRef,
			selectionChange,
			onInit,
			onAdd,
			onEdit,
			onDetail,
			onDelete,
			onDeleteByList,
			onSizechange,
			onCurrentChange,
			onSearch,
			...toRefs(state),
		};
	},
});
</script>
