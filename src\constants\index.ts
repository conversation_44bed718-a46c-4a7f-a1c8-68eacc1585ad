export * from './errorCodes';
export * from './storageKeys';
export * from './urls';
export * from './roles';

/**
 * 屏幕尺寸断点，用于响应式设计。
 * - US: 超小屏幕（手机）
 * - XS: 小屏幕（手机）
 * - SM: 中等屏幕（平板）
 * - MD: 大屏幕（桌面）
 * - LG: 超大屏幕（桌面）
 * - XL: 超宽屏幕（桌面）
 */
export const ScreenSizes = {
	US: 376, // 超小屏幕（手机）
	XS: 576, // 小屏幕（手机）
	SM: 768, // 中等屏幕（平板）
	MD: 992, // 大屏幕（桌面）
	LG: 1200, // 超大屏幕（桌面）
	XL: 1920, // 超宽屏幕（桌面）
};

/**
 * DictionaryTypes 对象包含了在应用中常用的字典类型常量。
 * 每个常量对应一个具体的字典类型，用于统一管理和引用。
 */
export const DictionaryTypes = {
	/**
	 * TIME_ZONE: 'Time Zone'
	 * 代表时区类型的常量，常用于与时区相关的功能和操作。
	 */
	TIME_ZONE: 'Time Zone',

	/**
	 * WORKFLOW: 'WorkflowType'
	 * 代表工作流程类型的常量，用于管理和定义不同的工作流程。
	 */
	WORKFLOW: 'WorkflowType',

	/**
	 * CATEGORY: 'Category'
	 * 代表分类类型的常量，用于对不同类别的数据进行分类和处理。
	 */
	CATEGORY: 'Category',
};

export const Consts = {
	Environment_Prod: 'production',
	Environment_Demo: 'demo',
	Environment_Test: 'test',
	Environment_Dev: 'development',

	Action_Create: 'create',
	Action_Update: 'update',
	Action_Delete: 'delete',
	Action_GetById: 'getById',

	SubmitType_Account: 'SubmitAccount',
	SubmitType_Invoice: 'SubmitInvoice',
};
