<template>
	<div class="invoice-import-container bg-white p-4">
		<div style="color: #337abf; font-size: 16px" class="mb-4 font-bold">PROCESS IMAGES:</div>
		<div class="p-4 mb-4 border flex-col flex-js-ac" style="background-color: #f8f9fb">
			<div style="font-size: 14px; align-self: flex-start" class="mb-4">MASS UPLOAD</div>
			<div class="text-center mb-4" style="color: var(--el-text-color-regular)">Urgent: There are {{ urgentCount }} valid images in {{ urgentPath }}.</div>
			<div class="form-wrapper">
				<el-form inline label-suffix=":" :model="urgentform">
					<el-form-item label="Batch Number" required>
						<el-input v-model="urgentform.batchNumber" />
					</el-form-item>
					<el-form-item label="Receiving Type">
						<el-select :placeholder="$t('message.page.selectKeyPlaceholder')" v-model="urgentform.receivingType" @change="handleReceivingTypeChange('Urgent', urgentform.receivingType)">
							<el-option v-for="option in receivingTypeOptions" :key="option.value" :label="option.label" :value="option.value" />
						</el-select>
					</el-form-item>
					<el-form-item label="Queue">
						<el-select :placeholder="$t('message.page.selectKeyPlaceholder')" v-model="urgentform.queue" style="width: 60px">
							<el-option v-for="option in queueOptions" :key="option.value" :label="option.label" :value="option.value" />
						</el-select>
					</el-form-item>
					<el-form-item>
						<el-button type="primary" @click="submitImport(urgentform)" v-auth="'WWE_Images.Import'">Import</el-button>
					</el-form-item>
				</el-form>
			</div>

			<div class="text-center mb-4 mt-4" style="color: var(--el-text-color-regular)">Standard: There are {{ standardCount }} valid images in {{ standardPath }}.</div>
			<div class="form-wrapper">
				<el-form inline label-suffix=":" :model="standardForm">
					<el-form-item label="Batch Number" required>
						<el-input v-model="standardForm.batchNumber" />
					</el-form-item>
					<el-form-item label="Receiving Type">
						<el-select :placeholder="$t('message.page.selectKeyPlaceholder')" v-model="standardForm.receivingType" @change="handleReceivingTypeChange('Standard', standardForm.receivingType)">
							<el-option v-for="option in receivingTypeOptions" :key="option.value" :label="option.label" :value="option.value" />
						</el-select>
					</el-form-item>
					<el-form-item label="Queue">
						<el-select :placeholder="$t('message.page.selectKeyPlaceholder')" v-model="standardForm.queue" style="width: 60px">
							<el-option v-for="option in queueOptions" :key="option.value" :label="option.label" :value="option.value" />
						</el-select>
					</el-form-item>
					<el-form-item>
						<el-button type="primary" @click="submitImport(standardForm)" v-auth="'WWE_Images.Import'">Import</el-button>
					</el-form-item>
				</el-form>
			</div>
		</div>

		<div class="p-4 mb-4 border flex-col flex-js-ac" style="background-color: #f8f9fb">
			<div style="font-size: 14px; align-self: flex-start" class="mb-4">SINGLE UPLOAD</div>
			<div class="form-wrapper flex-col flex-js-ac">
				<el-form inline label-suffix=":" :model="uploadForm">
					<el-form-item label="Batch Number" required>
						<el-input v-model="uploadForm.batchNumber" />
					</el-form-item>
					<el-form-item label="Receiving Type">
						<el-select :placeholder="$t('message.page.selectKeyPlaceholder')" v-model="uploadForm.receivingType">
							<el-option v-for="option in receivingTypeOptions" :key="option.value" :label="option.label" :value="option.value" />
						</el-select>
					</el-form-item>
					<el-form-item label="Queue">
						<el-select :placeholder="$t('message.page.selectKeyPlaceholder')" v-model="uploadForm.queue" style="width: 60px">
							<el-option v-for="option in queueOptions" :key="option.value" :label="option.label" :value="option.value" />
						</el-select>
					</el-form-item>
					<el-form-item label="Priority">
						<el-select :placeholder="$t('message.page.selectKeyPlaceholder')" v-model="uploadForm.priority">
							<el-option label="Standard" value="Standard"></el-option>
							<el-option label="Urgent" value="Urgent"></el-option>
						</el-select>
					</el-form-item>
				</el-form>

				<div class="upload-wrapper">
					<el-upload ref="uploadRef" class="upload" :auto-upload="false" v-model:file-list="uploadFiles" :limit="1">
						<template #trigger>
							<el-button type="primary">select file</el-button>
						</template>

						<el-button class="ml-3" type="primary" @click="submitUpload" v-auth="'WWE_Images.Upload'"> upload </el-button>

						<template #tip>
							<div class="el-upload__tip">jpg/png/pdf files with a size less than 10MB</div>
						</template>
					</el-upload>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ElMessage, ElMessageBox } from 'element-plus';
import { onMounted, reactive, ref } from 'vue';
import WWE_Images from '/@/api/WWE_Images';

type importFormData = {
	batchNumber: string;
	receivingType: string;
	queue: number;
	priority: string;
};

const urgentform = reactive<importFormData>({
	batchNumber: '',
	receivingType: 'Email',
	queue: 1,
	priority: 'Urgent',
});
const standardForm = reactive<importFormData>({
	batchNumber: '',
	receivingType: 'Email',
	queue: 1,
	priority: 'Standard',
});

const uploadForm = reactive({
	batchNumber: '',
	receivingType: 'Email',
	queue: 1,
	priority: 'Standard',
});

const receivingTypeOptions = [
	{ label: 'Email', value: 'Email' },
	{ label: 'US P.O. BOX', value: 'US P.O. BOX' },
	{ label: 'Mail & Paper', value: 'Mail & Paper' },
];

const queueOptions = [
	{ label: '1', value: 1 },
	{ label: '2', value: 2 },
	{ label: '3', value: 3 },
	{ label: '4', value: 4 },
];

const uploadFiles = ref();

const submitUpload = () => {
	// console.log('uploadFiles: ', uploadFiles.value);
	if (!uploadFiles.value || uploadFiles.value?.length === 0) {
		ElMessage.warning('Please select a file to upload');
		return;
	}
	// 判断文件类型
	const fileType: string = uploadFiles.value[0].name.split('.').pop();
	if (fileType.toLowerCase() !== 'jpg' && fileType.toLowerCase() !== 'png' && fileType.toLowerCase() !== 'pdf') {
		ElMessage.warning('Only jpg/png/pdf files are allowed');
		return;
	}

	// 判断文件大小
	const file = uploadFiles.value[0].raw;
	if (file.size > 10240 * 1024) {
		ElMessage.warning('File size exceeds 10MB');
		return;
	}

	if (!uploadForm.batchNumber) {
		ElMessage.warning('Please enter a batch number');
		return;
	}

	WWE_Images.upload({
		file: uploadFiles.value[0].raw,
		batchNumber: uploadForm.batchNumber,
		receivingType: uploadForm.receivingType,
		queue: uploadForm.queue,
		priority: uploadForm.priority,
	})
		.then((res) => {
			if (res.resultCode === 200) {
				ElMessage.success('Upload successful');
				// 清空文件列表
				uploadFiles.value = [];
			} else {
				ElMessage.error('Upload failed');
			}
		})
		.catch((error: HandledError) => {
			if (!error.isHandled) {
				const errorCode = error.code;
				const errorMessage = error.message;
				ElMessage.error(errorMessage);
			}
		});
};

const submitImport = (form: importFormData) => {
	if (!form.batchNumber) {
		ElMessage.warning('Please enter a batch number');
		return;
	}

	WWE_Images.ImportInvoiceFromLocal({
		batchNumber: form.batchNumber,
		receivingType: form.receivingType,
		queue: form.queue,
		priority: form.priority,
	})
		.then((res) => {
			if (res.resultCode === 200) {
				if (res.data.totalCount === 0) {
					ElMessage.warning('No valid images found');
					return;
				}

				if (res.data.totalCount === res.data.successCount) {
					// 提示共有多少个上传成功
					ElMessage.success(`Import successful, ${res.data.successCount} images imported successfully`);
				}
				if (res.data.totalCount > res.data.successCount) {
					// 提示有多少个上传成功, 上传失败, 以及错误信息(errorMessage)
					ElMessage.warning(`Import partially successful, ${res.data.successCount} images imported successfully, ${res.data.failCount} images failed to import, ${res.data.errorMessage}`);
				}
				initImagesInfo(form.priority, form.receivingType);
			} else {
				ElMessage.error('Import failed');
			}
		})
		.catch((error: HandledError) => {
			if (!error.isHandled) {
				const errorCode = error.code;
				const errorMessage = error.message;
				ElMessage.error(errorMessage);
			}
		});
};

const standardPath = ref('');
const urgentPath = ref('');
const standardCount = ref(0);
const urgentCount = ref(0);

onMounted(() => {
	initImagesInfo('Urgent', urgentform.receivingType);
	initImagesInfo('Standard', standardForm.receivingType);
});

const handleReceivingTypeChange = (priority: string, receivingType: string) => {
	// console.log('priority: ', priority);
	// console.log('receivingType: ', receivingType);

	initImagesInfo(priority, receivingType);
};

const initImagesInfo = (priority: string, receivingType: string) => {
	WWE_Images.getValidImagesInfo(priority, receivingType)
		.then((res) => {
			if (res.resultCode === 200) {
				if (priority === 'Standard') {
					standardPath.value = res.data.path;
					standardCount.value = res.data.count;
				} else {
					urgentPath.value = res.data.path;
					urgentCount.value = res.data.count;
				}
			} else {
				ElMessage.error('Failed to get valid images info');
			}
		})
		.catch((error: HandledError) => {
			if (!error.isHandled) {
				const errorCode = error.code;
				const errorMessage = error.message;
				ElMessage.error(errorMessage);
			}
		});
};
</script>

<style lang="scss" scoped>
@use '../../style/base.scss';
.form-wrapper .el-input {
	--el-input-width: 150px;
}

.form-wrapper .el-select {
	--el-select-width: 150px;
}

.upload-wrapper .upload {
	::v-deep(.el-upload--text) {
		vertical-align: middle;
		margin-right: 10px;
	}
}
</style>
