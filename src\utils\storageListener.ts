import { Router } from 'vue-router';
import { LocalStorageKeys, URLs } from '/@/constants';
import { Local, Session } from './storage';
import mittBus from '/@/utils/mitt';
import { equals } from '.';

export function setupStorageListener(router: Router) {
	window.addEventListener('storage', async function (event: any) {
		let userInfoKey = Local.setKey(LocalStorageKeys.UserInfo);
		let needToChangePwdKey = Local.setKey(LocalStorageKeys.NeedToChangePwd);
		let tagsKey = 'tagsViewList';
		if (event.key === userInfoKey || event.key === needToChangePwdKey) {
			//console.log(event.key + '改变触发了addEventListener');
			Session.remove(tagsKey);
			mittBus.emit('onCurrentContextmenuClick', {
				contextMenuClickId: 0,
				path: '/Dashboard',
				meta: {},
				name: '',
			});
			if (equals(router.currentRoute.value.path, URLs.LOGIN)) {
				location.reload();
			} else {
				router.push(URLs.LOGIN);
			}
		}
	});
}
