import BaseApi from '/@/api/BaseApi';
import request from '/@/utils/request';

class wWE_Invoice_EmailApi extends BaseApi {
  QueryV2(data: any) {
    return request({
      url: this.baseurl + 'QueryV2',
      method: 'post',
      data,
    });
  }

  BuildMissingDocsEmail(data: any) {
    return request({
      url: this.baseurl + 'BuildMissingDocsEmail',
      method: 'post',
      data,
    });
  }

  SendMissingDocsEmail(data: any) {
    return request({
      url: this.baseurl + 'SendMissingDocsEmail',
      method: 'post',
      data,
    });
  }

  ReSendMissingDocsEmail(data: any) {
    return request({
      url: this.baseurl + 'ReSendMissingDocsEmail',
      method: 'post',
      data,
    });
  }

  SendShipmentEmail(data: any) {
    return request({
      url: this.baseurl + 'SendShipmentEmail',
      method: 'post',
      data,
    });
  }
  
}

export default new wWE_Invoice_EmailApi('/api/WWE_Invoice_Email/', 'id');
