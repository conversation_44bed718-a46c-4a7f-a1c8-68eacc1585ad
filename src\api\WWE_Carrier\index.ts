import BaseApi from '/@/api/BaseApi';
import request from '/@/utils/request';

class wWE_CarrierApi extends BaseApi {
  QueryV2(data: any) {
    return request({
      url: this.baseurl + 'QueryV2',
      method: 'post',
      data,
    });
  }

  GetCarrierOptions(params?: any) {
    return request({
      url: this.baseurl + 'GetCarrierOptions',
      method: 'get',
      params,
    });
  }

}

export default new wWE_CarrierApi('/api/WWE_Carrier/', 'carrierId');
